# FA-PD-APP - Fare Management Application

[![Java](https://img.shields.io/badge/Java-1.8-orange.svg)](https://www.oracle.com/java/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.7.6-brightgreen.svg)](https://spring.io/projects/spring-boot)
[![Gradle](https://img.shields.io/badge/Gradle-Build-blue.svg)](https://gradle.org/)
[![IBM](https://img.shields.io/badge/IBM-Taiwan%20THSRC-blue.svg)](https://www.ibm.com/)

## Overview

FA-PD-APP is a comprehensive fare management application developed by IBM for Taiwan High Speed Rail Corporation (THSRC). This Spring Boot application implements sophisticated fare calculation, pricing strategies, and ticket management using modern enterprise patterns including CQRS (Command Query Responsibility Segregation) and Domain-Driven Design.

## 🏗️ Architecture

### Core Design Patterns

- **CQRS Implementation**: Clear separation between Command and Query operations
- **Domain-Driven Design**: Rich domain models with aggregate roots and event sourcing
- **Repository Pattern**: Spring Data JPA with custom decorators
- **Entity Inheritance**: JPA inheritance strategies for fare plan hierarchies

### Technology Stack

| Component | Technology | Version |
|-----------|------------|---------|
| **Runtime** | Java | 1.8 |
| **Framework** | Spring Boot | 2.7.6 |
| **Build Tool** | Gradle | Latest |
| **Database** | SQL Server (prod), H2 (test) | 11.2.1.jre8 |
| **ORM** | Hibernate JPA | Spring Data JPA |
| **Testing** | JUnit 5 | Spring Boot Test |
| **Code Quality** | SonarQube, JaCoCo | 3.4.0.2513 |

## 📁 Project Structure

```
src/main/java/com/ibm/tw/thsrc/bsm/fare/
├── controller/     # REST API endpoints (42+ controllers)
├── service/        # Business logic with CQRS pattern
│   ├── *CommandService.java    # Create/Update/Delete operations
│   └── *QueryService.java      # Read operations and searches
├── entity/         # JPA entities with inheritance hierarchies
├── repository/     # Spring Data repositories with decorators
├── stepservice/    # Multi-step business processes
├── translate/      # Entity-DTO translation layer
├── vo/            # Value objects (filters, sorts)
├── config/        # Configuration classes
└── scheduler/     # Background job scheduling
```

## 🚀 Quick Start

### Prerequisites

- Java 1.8+
- SQL Server (for production) or H2 (for testing)
- Redis (for caching and messaging)
- Docker (for containerization)

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd fa-pd-app
   ```

2. **Build the application**
   ```bash
   ./gradlew build
   ```

3. **Run tests**
   ```bash
   ./gradlew test
   ```

4. **Start the application**
   ```bash
   ./gradlew bootRun
   ```

The application will start on `http://localhost:8087`

### Database Configuration

**Development (SQL Server):**
```properties
spring.datasource.url=****************************************************************;
spring.datasource.username=sa
spring.datasource.password=P@ssw0rd
spring.jpa.properties.hibernate.default_schema=fa
```

**Testing (H2 In-Memory):**
```properties
spring.datasource.url=jdbc:h2:mem:testdb
spring.jpa.hibernate.ddl-auto=create-drop
```

## 🔧 Configuration

### Environment Profiles

| Profile | Description | Configuration File |
|---------|-------------|-------------------|
| `dev` | Development environment | `application-dev.properties` |
| `uat` | User acceptance testing | `application-uat.properties` |
| `stage` | Staging environment | `application-stage.properties` |
| `prod` | Production environment | `application-prod.properties` |
| `test` | Unit testing | `application.properties` (test) |

### External System Integration

#### MEIG Integration (OAuth2)
```properties
integration.meig.enabled=false
integration.meig.access-token-uri=https://meigkc.fatapat.com.tw:8443/auth/realms/MEIG/protocol/openid-connect/token
integration.meig.mock-controller=true
```

#### RES Integration (Socket-based)
```properties
integration.res.enabled=true
integration.res.class-name=com.ibm.tw.thsrc.bsm.res.connection.MockSocketConnection
integration.res.host-ip=***********
integration.res.port=1502
```

#### Redis Configuration
```properties
integration.redis.host=127.0.0.1
integration.redis.port=6377
integration.redis.timeout=4h
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
./gradlew test

# Run specific test class
./gradlew test --tests "com.ibm.tw.thsrc.bsm.fare.service.BasicFarePlanCommandServiceTest"

# Generate coverage report
./gradlew jacocoTestReport

# Run SonarQube analysis
./gradlew sonarqube
```

### Test Structure

- **Unit Tests**: Mirror production package structure
- **Integration Tests**: Use H2 in-memory database
- **Test Data**: Centralized in `testing/data/` package
- **Mocking**: Mockito with custom publisher configuration

## 🐳 Docker & Deployment

### Building Docker Image

```bash
# Build application
./gradlew build

# Build Docker image (WebSphere Liberty)
./gradlew buildDockerImage

# Push to registry
./gradlew pushDockerImage
```

### OpenShift Deployment

```bash
# Deploy to OpenShift cluster
./gradlew deployContainer

# Check deployment status
./gradlew checkPodStatus
```

### Docker Configuration

The application uses WebSphere Liberty as the runtime container:

```dockerfile
ARG WEBSPHERE_IMAGE
FROM ${WEBSPHERE_IMAGE}
ENV APP_NAME='fa-pd-app-0.0.1-SNAPSHOT.jar'
USER 1001
COPY build/libs/${APP_NAME} /config/dropins/spring/
```

## 📊 Monitoring & Health

### Health Endpoints

- **Health Check**: `GET /actuator/health`
- **Application Info**: `GET /actuator/info`

### Logging Configuration

- **AORS Logger**: Configurable log levels and appenders
- **Audit Logging**: Database and REST appenders
- **Log Rotation**: Configurable size limits (12MB max, 128KB slices)

## 🔒 Security & Compliance

- **IBM Confidential**: Copyright © 2023 IBM Corporation
- **Authentication**: OAuth2 integration with MEIG
- **Data Encryption**: SQL Server encryption support
- **Audit Trail**: Comprehensive audit logging for all operations

## 🛠️ Development Guidelines

### Code Quality

- **Lombok**: Extensive use for boilerplate reduction
- **JaCoCo**: Minimum coverage requirements
- **SonarQube**: Code quality gates
- **Naming Strategy**: Custom uppercase naming for database entities

### Best Practices

1. **Service Layer**: Always extend `AbstractCommandService` or `AbstractQueryService`
2. **Repository Pattern**: Use decorators for complex integrations
3. **Event Sourcing**: Publish domain events through `EventStore`
4. **Testing**: Maintain comprehensive test coverage
5. **Configuration**: Use environment-specific property files

## 📚 API Documentation

The application provides REST APIs for fare management operations:

### Core Endpoints

- **Fare Plans**: `/api/fare-plans/*`
- **Pricing**: `/api/basic-fare-prices/*`
- **Tickets**: `/api/tickets/*`
- **Promotions**: `/api/promotions/*`
- **Co-branded Cards**: `/api/co-branded-cards/*`

### Response Format

All APIs follow consistent response patterns with proper HTTP status codes and error handling.

## 🤝 Contributing

1. Follow the established CQRS patterns
2. Maintain test coverage above 80%
3. Use proper logging and error handling
4. Update documentation for new features
5. Follow IBM coding standards

## 📞 Support

For technical support or questions:

- **Team**: IBM Taiwan THSRC BSM Team
- **Environment**: Development, UAT, Production
- **Monitoring**: SonarQube, Application logs

## 📄 License

Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
