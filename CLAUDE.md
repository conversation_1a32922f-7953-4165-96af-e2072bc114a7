# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build & Test
- `./gradlew build` - Full build including tests and code generation
- `./gradlew test` - Run unit tests with JUnit 5
- `./gradlew jacocoTestReport` - Generate code coverage report
- `./gradlew sonarqube` - Run SonarQube analysis (requires credentials)
- `./gradlew clean` - Clean build artifacts

### Docker & Deployment
- `./gradlew buildDockerImage` - Build WebSphere Liberty Docker image
- `./gradlew pushDockerImage` - Push image to registry
- `./gradlew deployContainer` - Deploy to OpenShift cluster
- `./gradlew checkPodStatus` - Verify deployment status

### Single Test Execution
Use IntelliJ IDEA or run specific test classes:
```bash
./gradlew test --tests "com.ibm.tw.thsrc.bsm.fare.service.BasicFarePlanCommandServiceTest"
```

## Architecture Overview

This is a **Spring Boot 2.7.6** application implementing fare management for IBM Taiwan High Speed Rail using **CQRS pattern** with clear separation between Command and Query services.

### Core Patterns

**CQRS Implementation:**
- Command Services: Handle create/update/delete operations (`*CommandService`)
- Query Services: Handle read operations and complex searches (`*QueryService`)
- All services extend `AbstractCommandService` or `AbstractQueryService`

**Domain-Driven Design:**
- Entities extend `AggregateRoot` for event sourcing
- Repository pattern with Spring Data JPA
- Domain events published through `EventStore`
- Decorator pattern for repository extensions (e.g., `ReissueTicketRefundFeeResRepositoryDecorator`)

**Entity Inheritance:**
- Base entities like `FarePlan` with specific implementations (`BasicFarePlan`)
- Uses `@Inheritance(strategy = InheritanceType.JOINED)`

### Package Structure
```
com.ibm.tw.thsrc.bsm.fare/
├── controller/     # REST endpoints (42+ controllers)
├── service/        # Business logic (Command/Query split)
├── entity/         # JPA entities with inheritance
├── repository/     # Spring Data repositories
├── stepservice/    # Multi-step processing services
├── translate/      # Entity-DTO translation
└── vo/            # Value objects (filters/sorts)
```

### Key Technologies
- **Database**: SQL Server (prod), H2 (test) with `fa` schema
- **Dependencies**: Multiple IBM BSM libraries for integration
- **Testing**: JUnit 5, Spring Boot Test, H2 in-memory database
- **Integration**: MEIG (OAuth2), RES (socket-based), Redis caching

### Configuration Management
- Environment-specific properties: `application-{env}.properties`
- Main environments: dev, uat, prod, stage
- Test configuration uses H2 with `@ActiveProfiles("test")`
- Integration configurations for external systems (MEIG, RES)

### Testing Conventions
- Test classes mirror production structure
- Dedicated test data classes in `testing/data/` package
- `MockitoPublisherConfiguration` for event mocking
- Test application: `FareTestApplication` with separate config

### Development Notes
- Uses Lombok extensively - ensure annotation processing enabled
- Hibernate JPA model generation creates metamodel classes
- Custom naming strategy: `UpperCaseNamingStrategy`
- Repository decorator pattern for complex integrations
- All services follow consistent validation and transaction patterns

### External System Integration
- **MEIG**: OAuth2-based with mock support (`integration.meig.mock-controller=true`)
- **RES**: Socket connection with pooling
- **Redis**: Caching and messaging
- **Email**: SMTP notifications

### Code Quality
- JaCoCo for coverage reporting
- SonarQube integration (requires credentials in gradle.properties)
- Lombok configuration in `lombok.config`
- Comprehensive test coverage expected for all services