# Server Config
spring.application.name=fa-pd-app
server.port=8087

# Spring Probe
management.endpoints.web.exposure.include=health,info

# Control Data Model Postgres Connection Config
spring.datasource.url=${SPRING_DATASOURCE_URL}
spring.datasource.username=${SPRING_DATASOURCE_USERNAME}
spring.datasource.password=${SPRING_DATASOURCE_PASSWORD}
spring.jpa.properties.hibernate.default_schema=${SPRING_JPA_PROPERTIES_HIBERNATE_DEFAULT_SCHEMA}
spring.jpa.show-sql=true
spring.jpa.generate-ddl=true
spring.jpa.hibernate.ddl-auto=update
spring.jackson.serialization.FAIL_ON_EMPTY_BEANS=false
spring.jpa.hibernate.naming.physical-strategy=com.ibm.tw.thsrc.bsm.core.config.UpperCaseNamingStrategy
spring.jpa.properties.hibernate.use_nationalized_character_data=true
spring.jpa.properties.hibernate.format_sql=true

# BSM Integration MEIG Config
integration.meig.enabled=false
integration.meig.channel=BSM
integration.meig.system=BSM
integration.meig.location=OMC
integration.meig.host=test
integration.meig.client=${INTEGRATION_MEIG_CLIENT}
integration.meig.secret=${INTEGRATION_MEIG_SECRET}
integration.meig.access-token-uri=${INTEGRATION_MEIG_ACCESS_TOKEN_URI}
integration.meig.mock-controller=true
integration.meig.url=${INTEGRATION_MEIG_URL}

# AORS Integration RES Config
integration.res.enabled=true
integration.res.class-name=com.ibm.tw.thsrc.bsm.res.connection.SocketConnection
integration.res.host-ip=${INTEGRATION_RES_HOST_IP}
integration.res.port=${INTEGRATION_RES_PORT}
integration.res.connection-pool-size=10
integration.res.connection-timeout=240000
integration.res.session-timeout=240000
integration.res.mock-file-path=screen/
integration.res.retry.till-hhmm=0400
integration.res.retry.interval=60000
integration.res.session-ids=${INTEGRATION_RES_SESSION_IDS}
#
# GZip compression, HTTP/2, caching
#
# Enable response compression
server.compression.enabled=true
# The comma-separated list of mime types that should be compressed
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json,application/xml
# Compress the response only if the response size is at least 1KB
server.compression.min-response-size=1024
# Enable HTTP/2 support, if the current environment supports it
server.http2.enabled=true

#AORS log size config
aors.log.max.size=12
aors.log.slice.size=128