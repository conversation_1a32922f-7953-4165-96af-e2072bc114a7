# Generate Database Schema with Spring Data JPA
spring.jpa.properties.javax.persistence.schema-generation.scripts.action=drop-and-create
spring.jpa.properties.javax.persistence.schema-generation.scripts.create-target=../ap-shared-components/test-util-lib/fa-create.sql
spring.jpa.properties.javax.persistence.schema-generation.scripts.drop-target=../ap-shared-components/test-util-lib/fa-drop.sql
spring.jpa.properties.javax.persistence.schema-generation.create-source=metadata
spring.jpa.properties.javax.persistence.schema-generation.drop-source=metadata
