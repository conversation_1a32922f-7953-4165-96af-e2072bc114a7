{"properties": [{"name": "integration.meig.enabled", "type": "java.lang.String", "description": "A description for 'integration.meig.enabled'"}, {"name": "integration.meig.channel", "type": "java.lang.String", "description": "A description for 'integration.meig.channel'"}, {"name": "integration.meig.system", "type": "java.lang.String", "description": "A description for 'integration.meig.system'"}, {"name": "integration.meig.location", "type": "java.lang.String", "description": "A description for 'integration.meig.location'"}, {"name": "integration.meig.host", "type": "java.lang.String", "description": "A description for 'integration.meig.host'"}, {"name": "integration.meig.client", "type": "java.lang.String", "description": "A description for 'integration.meig.client'"}, {"name": "integration.meig.secret", "type": "java.lang.String", "description": "A description for 'integration.meig.secret'"}, {"name": "integration.meig.access-token-uri", "type": "java.lang.String", "description": "A description for 'integration.meig.access-token-uri'"}, {"name": "integration.meig.mock-controller", "type": "java.lang.String", "description": "A description for 'integration.meig.mock-controller'"}, {"name": "integration.meig.url", "type": "java.lang.String", "description": "A description for 'integration.meig.url'"}, {"name": "integration.res.enabled", "type": "java.lang.String", "description": "A description for 'integration.res.enabled'"}, {"name": "integration.res.class-name", "type": "java.lang.String", "description": "A description for 'integration.res.class-name'"}, {"name": "integration.res.host-ip", "type": "java.lang.String", "description": "A description for 'integration.res.host-ip'"}, {"name": "integration.res.port", "type": "java.lang.String", "description": "A description for 'integration.res.port'"}, {"name": "integration.res.connection-pool-size", "type": "java.lang.String", "description": "A description for 'integration.res.connection-pool-size'"}, {"name": "integration.res.connection-timeout", "type": "java.lang.String", "description": "A description for 'integration.res.connection-timeout'"}, {"name": "integration.res.session-timeout", "type": "java.lang.String", "description": "A description for 'integration.res.session-timeout'"}, {"name": "integration.res.mock-file-path", "type": "java.lang.String", "description": "A description for 'integration.res.mock-file-path'"}, {"name": "integration.res.retry.till-hhmm", "type": "java.lang.String", "description": "A description for 'integration.res.retry.till-hhmm'"}, {"name": "integration.res.retry.interval", "type": "java.lang.String", "description": "A description for 'integration.res.retry.interval'"}, {"name": "integration.res.session-ids", "type": "java.lang.String", "description": "A description for 'integration.res.session-ids'"}, {"name": "integration.cronjob.schedule-job-url", "type": "java.lang.String", "description": "A description for 'integration.cronjob.schedule-job-url'"}, {"name": "integration.endpoint", "type": "java.lang.String", "description": "A description for 'integration.endpoint'"}, {"name": "integration.channel.topic", "type": "java.lang.String", "description": "A description for 'integration.channel.topic'"}, {"name": "integration.service.aop.enabled", "type": "java.lang.String", "description": "A description for 'integration.service.aop.enabled'"}, {"name": "integration.redis.host", "type": "java.lang.String", "description": "A description for 'integration.redis.host'"}, {"name": "integration.redis.port", "type": "java.lang.String", "description": "A description for 'integration.redis.port'"}, {"name": "integration.redis.password", "type": "java.lang.String", "description": "A description for 'integration.redis.password'"}, {"name": "integration.redis.database", "type": "java.lang.String", "description": "A description for 'integration.redis.database'"}, {"name": "integration.redis.timeout", "type": "java.lang.String", "description": "A description for 'integration.redis.timeout'"}, {"name": "integration.redis.pool.max-active", "type": "java.lang.String", "description": "A description for 'integration.redis.pool.max-active'"}, {"name": "integration.redis.pool.max-wait", "type": "java.lang.String", "description": "A description for 'integration.redis.pool.max-wait'"}, {"name": "integration.redis.pool.max-idle", "type": "java.lang.String", "description": "A description for 'integration.redis.pool.max-idle'"}, {"name": "integration.redis.pool.min-idle", "type": "java.lang.String", "description": "A description for 'integration.redis.pool.min-idle'"}, {"name": "integration.messaging.redis.host", "type": "java.lang.String", "description": "A description for 'integration.messaging.redis.host'"}, {"name": "integration.messaging.redis.port", "type": "java.lang.String", "description": "A description for 'integration.messaging.redis.port'"}, {"name": "integration.messaging.redis.password", "type": "java.lang.String", "description": "A description for 'integration.messaging.redis.password'"}, {"name": "integration.messaging.redis.database", "type": "java.lang.String", "description": "A description for 'integration.messaging.redis.database'"}, {"name": "integration.messaging.redis.timeout", "type": "java.lang.String", "description": "A description for 'integration.messaging.redis.timeout'"}, {"name": "integration.messaging.redis.pool.max-active", "type": "java.lang.String", "description": "A description for 'integration.messaging.redis.pool.max-active'"}, {"name": "integration.messaging.redis.pool.max-wait", "type": "java.lang.String", "description": "A description for 'integration.messaging.redis.pool.max-wait'"}, {"name": "integration.messaging.redis.pool.max-idle", "type": "java.lang.String", "description": "A description for 'integration.messaging.redis.pool.max-idle'"}, {"name": "integration.messaging.redis.pool.min-idle", "type": "java.lang.String", "description": "A description for 'integration.messaging.redis.pool.min-idle'"}, {"name": "integration.des.enabled", "type": "java.lang.String", "description": "A description for 'integration.des.enabled'"}, {"name": "integration.des.host", "type": "java.lang.String", "description": "A description for 'integration.des.host'"}, {"name": "integration.des.port", "type": "java.lang.String", "description": "A description for 'integration.des.port'"}, {"name": "integration.des.binary.username", "type": "java.lang.String", "description": "A description for 'integration.des.binary.username'"}, {"name": "integration.des.binary.passwordd", "type": "java.lang.String", "description": "A description for 'integration.des.binary.passwordd'"}, {"name": "integration.des.binary.folder", "type": "java.lang.String", "description": "A description for 'integration.des.binary.folder'"}, {"name": "integration.des.binary.train.path", "type": "java.lang.String", "description": "A description for 'integration.des.binary.train.path'"}, {"name": "integration.des.binary.train.op.path", "type": "java.lang.String", "description": "A description for 'integration.des.binary.train.op.path'"}, {"name": "integration.des.rdo.username", "type": "java.lang.String", "description": "A description for 'integration.des.rdo.username'"}, {"name": "integration.des.rdo.passwordd", "type": "java.lang.String", "description": "A description for 'integration.des.rdo.passwordd'"}, {"name": "integration.des.rdo.folder", "type": "java.lang.String", "description": "A description for 'integration.des.rdo.folder'"}, {"name": "integration.endpoint.bsm.job", "type": "java.lang.String", "description": "A description for 'integration.endpoint.bsm.job'"}, {"name": "email.service.enabled", "type": "java.lang.String", "description": "A description for 'email.service.enabled'"}, {"name": "email.service.from", "type": "java.lang.String", "description": "A description for 'email.service.from'"}, {"name": "bsm.web.host.url", "type": "java.lang.String", "description": "A description for 'bsm.web.host.url'"}, {"name": "aors.log.config.properties.name", "type": "java.lang.String", "description": "A description for 'aors.log.config.properties.name'"}, {"name": "aors.alert.config.properties.name", "type": "java.lang.String", "description": "A description for 'aors.alert.config.properties.name'"}, {"name": "housekeeping.expiry.days", "type": "java.lang.String", "description": "A description for 'housekeeping.expiry.days'"}, {"name": "housekeeping.scheduler.cron", "type": "java.lang.String", "description": "A description for 'housekeeping.scheduler.cron'"}, {"name": "housekeeping.scheduler.zone", "type": "java.lang.String", "description": "A description for 'housekeeping.scheduler.zone'"}]}