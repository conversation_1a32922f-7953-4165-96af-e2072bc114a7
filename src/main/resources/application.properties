# Server Config
spring.application.name=fa-pd-app
server.port=8087

# Spring Probe
management.endpoints.web.exposure.include=health,info

# Control Data Model Postgres Connection Config
spring.datasource.url=****************************************************************;
spring.datasource.username=sa
spring.datasource.password=P@ssw0rd
spring.jpa.properties.hibernate.default_schema=fa
spring.jpa.show-sql=false
spring.jpa.generate-ddl=true
spring.jpa.hibernate.ddl-auto=update
spring.jackson.serialization.FAIL_ON_EMPTY_BEANS=false
spring.jpa.hibernate.naming.physical-strategy=com.ibm.tw.thsrc.bsm.core.config.UpperCaseNamingStrategy
spring.jpa.properties.hibernate.use_nationalized_character_data=true
spring.jpa.properties.hibernate.format_sql=true

# BSM Integration MEIG Config
integration.meig.enabled=false
integration.meig.channel=BSM
integration.meig.system=BSM
integration.meig.location=OMC
integration.meig.host=test
integration.meig.client=meig_bpu
integration.meig.secret=d82c5454-117b-40d8-9316-58bf0890838a
integration.meig.access-token-uri=https://meigkc.fatapat.com.tw:8443/auth/realms/MEIG/protocol/openid-connect/token
integration.meig.mock-controller=true
integration.meig.url=http://localhost:${server.port}

# AORS Integration RES Config
integration.res.enabled=true
#integration.res.class-name=com.ibm.tw.thsrc.bsm.res.connection.SocketConnection
integration.res.class-name=com.ibm.tw.thsrc.bsm.res.connection.MockSocketConnection
integration.res.host-ip=***********
integration.res.port=1502
integration.res.connection-pool-size=2
integration.res.connection-timeout=240000
integration.res.session-timeout=240000
integration.res.mock-file-path=screen/
integration.res.retry.till-hhmm=0400
integration.res.retry.interval=60000
integration.res.session-ids=BSMRS001,BSMRS002

# CronJob Intergration config
integration.cronjob.schedule-job-url=http://127.0.0.1:9997/ScheduledJob
integration.endpoint=/bsm/fa
integration.channel.topic=status
integration.service.aop.enabled=true
integration.redis.host=127.0.0.1
integration.redis.port=6377
integration.redis.password=
integration.redis.database=0
integration.redis.timeout=4h
integration.redis.pool.max-active=8
integration.redis.pool.max-wait=-1
integration.redis.pool.max-idle=8
integration.redis.pool.min-idle=0
integration.messaging.redis.host=127.0.0.1
integration.messaging.redis.port=6377
integration.messaging.redis.password=
integration.messaging.redis.database=0
integration.messaging.redis.timeout=1000
integration.messaging.redis.pool.max-active=8
integration.messaging.redis.pool.max-wait=-1
integration.messaging.redis.pool.max-idle=8
integration.messaging.redis.pool.min-idle=0

# email setting
email.service.enabled=true
email.service.from=BSMDEV
spring.mail.host=***********
spring.mail.port=25
#spring.mail.username=<EMAIL>
#spring.mail.password={password}
spring.mail.properties.mail.debug=true
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=true

## email setting
#email.service.enabled=true
#email.service.from=BSMDEV
#spring.mail.host=smtp.gmail.com
#spring.mail.port=587
#spring.mail.username=<EMAIL>
#spring.mail.password=kcevtsdaweemdume
#spring.mail.properties.mail.debug=true
#
#spring.mail.properties.mail.smtp.auth=true
#spring.mail.properties.mail.smtp.starttls.enable=true
#spring.mail.properties.mail.smtp.starttls.required=true

# web server host
bsm.web.host.url=https://devbsmres1.dev.corp

#
# GZip compression, HTTP/2, caching
#
# Enable response compression
server.compression.enabled=true
# The comma-separated list of mime types that should be compressed
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json,application/xml
# Compress the response only if the response size is at least 1KB
server.compression.min-response-size=1024
# Enable HTTP/2 support, if the current environment supports it
server.http2.enabled=true

housekeeping.expiry.days=395
housekeeping.scheduler.cron=0 0 0 * * ?
housekeeping.scheduler.zone=GMT+8

#AORS log size config
aors.log.max.size=12
aors.log.slice.size=128