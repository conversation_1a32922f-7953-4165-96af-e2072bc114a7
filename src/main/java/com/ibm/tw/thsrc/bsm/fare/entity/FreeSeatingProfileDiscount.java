/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.entity;

import com.ibm.tw.thsrc.bsm.core.entity.AggregateRoot;
import java.math.BigDecimal;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
public class FreeSeatingProfileDiscount extends AggregateRoot {

  private BigDecimal discountPct;

  @ManyToOne
  private PassengerProfile passengerProfile;

  @ManyToOne
  private FreeSeatingTicketType freeSeatingTicketType;
}
