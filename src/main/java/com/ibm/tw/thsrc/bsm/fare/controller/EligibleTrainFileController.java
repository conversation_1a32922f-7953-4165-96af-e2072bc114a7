/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.fa.api.EligibleTrainFileApi;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainFileInput;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainFileOutput;
import com.ibm.tw.thsrc.bsm.fare.service.EligibleTrainFileCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.EligibleTrainFileQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/EligibleTrainFiles")
public class EligibleTrainFileController implements EligibleTrainFileApi {

  @Autowired
  private EligibleTrainFileCommandService commandService;

  @Autowired
  private EligibleTrainFileQueryService queryService;

  @Autowired
  private EventStore eventStore;

  @Override
  public Page<EligibleTrainFileOutput> search(Search search) {
    return queryService.search(search);
  }

  @Override
  public EligibleTrainFileOutput create(EligibleTrainFileInput input) {
    eventStore.validateFunctionLock(OperationFunction.ELIGIBLE_TRAIN_FILE);
    Long id = commandService.create(input);
    return queryService.read(id);
  }

  @Override
  public EligibleTrainFileOutput replace(Long id, EligibleTrainFileInput input) {
    eventStore.validateFunctionLock(OperationFunction.ELIGIBLE_TRAIN_FILE);
    commandService.update(id, input);
    return queryService.read(id);
  }
}
