/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.fa.dto.ReissueTicketRefundFeeOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.ReissueTicketRefundFee;
import com.ibm.tw.thsrc.bsm.fare.repository.ReissueTicketRefundFeeRepository;
import com.ibm.tw.thsrc.bsm.res.connection.annotation.RESTransaction;
import java.util.List;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class ReissueTicketRefundFeeQueryService {

  @Autowired
  private ReissueTicketRefundFeeRepository repo;

  @RESTransaction
  public Page<ReissueTicketRefundFeeOutput> search() {

    List<ReissueTicketRefundFee> results = repo.findAll();
    return new PageImpl<>(results.stream().map(this::translate).collect(Collectors.toList()));
  }

  private ReissueTicketRefundFeeOutput translate(ReissueTicketRefundFee entity) {
    ReissueTicketRefundFeeOutput output = new ReissueTicketRefundFeeOutput();
    output.setId(entity.getId());
    output.setDataVersion(entity.getDataVersion());
    output.setEffDate(entity.getEffDate());
    output.setFeePct(entity.getFeePct());
    output.setIsAdditionalCharges(entity.getIsAdditionalCharges());

    return output;
  }
}
