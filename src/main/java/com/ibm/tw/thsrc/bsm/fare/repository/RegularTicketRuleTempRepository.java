/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fare.entity.RegularTicketRuleTemp;
import com.ibm.tw.thsrc.bsm.fare.entity.TicketProductPolicy;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface RegularTicketRuleTempRepository extends
    JpaRepository<RegularTicketRuleTemp, Long> {

  List<RegularTicketRuleTemp> findByTicketProductPolicy(TicketProductPolicy ticketProductPolicy);
}
