/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.translate;

import com.ibm.tw.thsrc.bsm.fa.dto.FreeSeatingProfileDiscountOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.FreeSeatingProfileDiscount;


public class FreeSeatingProfileDiscountTranslator {

  private FreeSeatingProfileDiscountTranslator() {
    throw new IllegalStateException("FreeSeatingProfileDiscountTranslator class");
  }

  public static FreeSeatingProfileDiscountOutput toFreeSeatingProfileDiscountOutput(
      FreeSeatingProfileDiscount aggregateRoot) {

    FreeSeatingProfileDiscountOutput output = new FreeSeatingProfileDiscountOutput();
    output.setId(aggregateRoot.getId());
    output.setDiscountPct(aggregateRoot.getDiscountPct());
    output.setTicketTypeId(aggregateRoot.getFreeSeatingTicketType().getId());
    output.setTicketTypeCode(aggregateRoot.getFreeSeatingTicketType().getCode());
    output.setTicketTypeName(aggregateRoot.getFreeSeatingTicketType().getName());
    output.setProfileCode(aggregateRoot.getPassengerProfile().getCode());
    output.setProfileZhName(aggregateRoot.getPassengerProfile().getZhName());
    output.setProfileDisplayOrder(aggregateRoot.getPassengerProfile().getDisplayOrder());
    output.setDataVersion(aggregateRoot.getDataVersion());
    return output;

  }
}
