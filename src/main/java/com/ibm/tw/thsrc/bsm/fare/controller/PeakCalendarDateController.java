/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.fa.api.PeakCalendarDateApi;
import com.ibm.tw.thsrc.bsm.fa.dto.PeakCalendarDateInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PeakCalendarDateOutput;
import com.ibm.tw.thsrc.bsm.fare.service.PeakCalendarDateCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.PeakCalendarDateQueryService;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/PeakCalenderDates")
public class PeakCalendarDateController implements PeakCalendarDateApi {

  @Autowired
  private PeakCalendarDateCommandService commandService;

  @Autowired
  private PeakCalendarDateQueryService queryService;

  @Autowired
  private EventStore eventStore;

  @Override
  public Page<PeakCalendarDateOutput> search(Search search) {
    return queryService.search(search);
  }

  @Override
  public void dataCreation() {
    commandService.dataCreation();
  }

  @Override
  public List<PeakCalendarDateOutput> upsert(List<PeakCalendarDateInput> changeList) {
    eventStore.validateFunctionLock(OperationFunction.PEAK_CALENDAR_DATES);
    Set<Long> ids = commandService.upsert(changeList);
    return queryService.read(ids);
  }
}
