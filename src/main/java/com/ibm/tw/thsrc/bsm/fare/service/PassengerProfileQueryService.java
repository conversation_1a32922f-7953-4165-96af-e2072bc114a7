/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.PassengerProfileOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerProfile;
import com.ibm.tw.thsrc.bsm.fare.repository.PassengerProfileRepository;
import com.ibm.tw.thsrc.bsm.fare.translate.PassengerProfileTranslator;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class PassengerProfileQueryService extends
    AbstractQueryService<PassengerProfile, PassengerProfileOutput, Void, Void>
    implements PassengerProfileQueryApi {

  @Autowired
  protected PassengerProfileRepository passengerProfileRepository;

  @Override
  protected PassengerProfileOutput translate(PassengerProfile aggregateRoot) {
    return PassengerProfileTranslator.toPassengerProfileOutput(aggregateRoot);
  }

  @Override
  protected Sort translate(Void sortBy, Direction sortDirection) {
    return Sort.sort(PassengerProfile.class)
        .by(PassengerProfile::getDisplayOrder)
        .ascending();
  }

  @Override
  protected Predicate translate(ComparisonExpression<Void> f, Root<PassengerProfile> root,
      CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    return null;
  }

  @Override
  protected Function<Map<String, Boolean>, Void> getSortByTranslator() {
    return data -> null;
  }

  @Override
  protected Function<Map<String, Object>, Void> getFilterAttributeValueTranslator() {
    return data -> null;
  }

  @Override
  public PassengerProfileOutput read(Long id) {
    return passengerProfileRepository
        .findById(id)
        .map(this::translate)
        .orElseThrow(ResourceNotFoundException::new);
  }

  @Override
  public Page<PassengerProfileOutput> search(Search search) {
    PageRequest pr = translateToPageRequest(search);
    Specification<PassengerProfile> spec = translateToSpecification(search);
    return passengerProfileRepository
        .findAll(spec, pr)
        .map(this::translate);
  }

  public Set<PassengerProfileOutput> read(Collection<Long> ids) {
    return passengerProfileRepository
        .findAllById(ids)
        .stream()
        .map(this::translate)
        .collect(Collectors.toSet());
  }

  @Override
  public List<PassengerProfileOutput> getPassengerProfiles() {
    return passengerProfileRepository
        .findAll()
        .stream()
        .map(this::translate)
        .collect(Collectors.toList());
  }
}
