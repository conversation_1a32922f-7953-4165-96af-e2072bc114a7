/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fare.entity.ReissueTicketRefundFee;
import java.time.LocalDate;
import java.util.List;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
@Qualifier("JpaSpecificationExecutor")
public interface ReissueTicketRefundFeeJpaRepository extends
    JpaRepository<ReissueTicketRefundFee, Long>, JpaSpecificationExecutor<ReissueTicketRefundFee> {

  ReissueTicketRefundFee findByEffDate(LocalDate effDate);

  List<ReissueTicketRefundFee> findByUserId(String userId);
}
