/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.api.ProfileDiscountTypeApi;
import com.ibm.tw.thsrc.bsm.fa.dto.ProfileDiscountTypeOutput;
import com.ibm.tw.thsrc.bsm.fare.service.ProfileDiscountTypeQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ProfileDiscountTypes")
public class ProfileDiscountTypeController implements ProfileDiscountTypeApi {

  @Autowired
  private ProfileDiscountTypeQueryService queryService;


  @Override
  public Page<ProfileDiscountTypeOutput> search(Search search) {
    return queryService.search(search);
  }
}
