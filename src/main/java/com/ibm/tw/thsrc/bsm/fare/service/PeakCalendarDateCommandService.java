/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.PeakCalendarDateInput;
import com.ibm.tw.thsrc.bsm.fare.domain.event.BatchPeakCalendarDateReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.PeakCalendarDate;
import com.ibm.tw.thsrc.bsm.fare.entity.PeakTime;
import com.ibm.tw.thsrc.bsm.fare.entity.PeakWeekday;
import com.ibm.tw.thsrc.bsm.fare.repository.PeakCalendarDateRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PeakTimeRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PeakWeekdayRepository;
import com.ibm.tw.thsrc.bsm.message.fare.PeakCalendarDateData;
import java.time.LocalDate;
import java.time.Month;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class PeakCalendarDateCommandService extends
    AbstractCommandService<PeakCalendarDate, PeakCalendarDateInput> {

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private PeakCalendarDateRepository repo;

  @Autowired
  private PeakTimeRepository peakTimeRepo;

  @Autowired
  private PeakWeekdayRepository weekdayRepo;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Override
  public Long create(PeakCalendarDateInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void update(Long id, PeakCalendarDateInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void delete(Long id) {
    throw new UnsupportedOperationException();
  }

  public Set<Long> upsert(List<PeakCalendarDateInput> changeList) {

    List<PeakCalendarDateData> replacedMsgList = new ArrayList<>();
    Set<Long> ids = new HashSet<>();
    changeList.forEach(changeInput -> {

      PeakCalendarDate entity = new PeakCalendarDate();
      // update version
      if (Objects.nonNull(changeInput.getId())) {
        entity = repo.findById(changeInput.getId()).orElseThrow(ResourceNotFoundException::new);
        this.validateConcurrency(entity, changeInput.getDataVersion());
      }

      // create & update version
      this.writeAggregateRootValue(entity, changeInput);

      this.validateInvariants(entity);

      PeakCalendarDate savedEntity = repo.save(entity);

      ids.add(savedEntity.getId());
      replacedMsgList.add(translatePubMsg(savedEntity));
    });

    this.publishBatchReplacedEventMsg(replacedMsgList);
    return ids;
  }

  public void dataCreation() {
    Pair<LocalDate, LocalDate> startEndCalenderDate = findStartEndCalenderDate();

    // 找現在版，找不到就使用今天的日期=>第一天都找不到代表整個月曆設定都沒有該日期區間的現在版
    List<PeakCalendarDate> dates = repo.findByCalendarDate(startEndCalenderDate.getLeft());
    List<LocalDate> currentEffDates = dates.stream().map(PeakCalendarDate::getEffDate)
        .filter(date -> date.compareTo(LocalDate.now()) <= 0).sorted(Collections.reverseOrder())
        .collect(Collectors.toList());
    LocalDate currentEffDate = currentEffDates.isEmpty() ? LocalDate.now() : currentEffDates.get(0);

    List<PeakCalendarDate> createDates = new ArrayList<>();
    // 獲得應新增的起迄日期
    LocalDate calenderDate = startEndCalenderDate.getLeft();
    PeakTime defaultPeakTime = peakTimeRepo.findByCode("0");
    // 從起始日開始一個個檢查
    while (calenderDate.compareTo(startEndCalenderDate.getRight()) <= 0) {
      List<PeakCalendarDate> currentDate = repo.findByEffDateAndCalendarDate(currentEffDate,
          calenderDate);

      // 沒有的日期才新增
      if (currentDate.isEmpty()) {
        PeakCalendarDate peakCalendarDate = new PeakCalendarDate();
        peakCalendarDate.setEffDate(currentEffDate);
        peakCalendarDate.setCalendarDate(calenderDate);
        // 用月曆日期找上一版
        PeakCalendarDate lastVersion = findLastVersion(calenderDate);

        if (Objects.nonNull(lastVersion)) {
          peakCalendarDate.setPeakTime(lastVersion.getPeakTime());
          if (Objects.nonNull(lastVersion.getPeakWeekday())) {
            int dayOfWeek = calenderDate.getDayOfWeek().getValue();
            PeakWeekday weekday = weekdayRepo.findByDayOfWeek(dayOfWeek).orElseThrow(
                () -> FareErrorCode.FA_PEAK_CALENDAR_DATE_CANNOT_ASSOCIATE_PEAK_WEEKDAY.toException(
                    String.valueOf(dayOfWeek)));
            peakCalendarDate.setPeakWeekday(weekday);
          }
          // 沒有上一版，設定預設的全日尖峰
        } else {
          peakCalendarDate.setPeakTime(defaultPeakTime);
        }

        createDates.add(peakCalendarDate);
      }

      calenderDate = calenderDate.plusDays(1);
    }

    repo.saveAll(createDates);
  }

  @Override
  protected void validateInvariants(PeakCalendarDate entity) throws UnprocessableException {
    List<PeakCalendarDate> otherEntity = repo.findByEffDateAndCalendarDate(entity.getEffDate(),
        entity.getCalendarDate());

    if ((entity.getId() == null && !otherEntity.isEmpty()) || (Objects.nonNull(entity.getId())
        && otherEntity.size() > 1)) {
      throw FareErrorCode.FA_PEAK_CALENDAR_DATE_EFF_DATE_AND_CALENDER_DATE_SET_CANNOT_REPEAT.toException(
          "effDate: " + entity.getEffDate().toString(),
          "calenderDate: " + entity.getCalendarDate().toString());
    }
  }

  @Override
  protected void writeAggregateRootValue(PeakCalendarDate entity, PeakCalendarDateInput input) {
    entity.setEffDate(input.getEffDate());
    entity.setCalendarDate(input.getCalendarDate());
    if (Objects.nonNull(input.getPeakTimeId())) {
      PeakTime peakTime = peakTimeRepo.findById(input.getPeakTimeId())
          .orElseThrow(
              () -> FareErrorCode.FA_PEAK_CALENDAR_DATE_CANNOT_ASSOCIATE_PEAK_TIME.toException(
                  String.valueOf(input.getPeakTimeId())));
      entity.setPeakTime(peakTime);
    } else {
      entity.setPeakTime(null);
    }

    if (input.getPeakWeekday()) {
      int dayOfWeek = input.getCalendarDate().getDayOfWeek().getValue();
      PeakWeekday weekday = weekdayRepo.findByDayOfWeek(dayOfWeek).orElseThrow(
          () -> FareErrorCode.FA_PEAK_CALENDAR_DATE_CANNOT_ASSOCIATE_PEAK_WEEKDAY.toException(
              String.valueOf(dayOfWeek)));
      entity.setPeakWeekday(weekday);
    } else {
      entity.setPeakWeekday(null);
    }
  }

  private Pair<LocalDate, LocalDate> findStartEndCalenderDate() {
    LocalDate now = LocalDate.now();
    int currentYear = now.getYear();
    Month currentMonth = now.getMonth();
    LocalDate startDate = LocalDate.of(currentYear, currentMonth, 1);

    LocalDate tempEnd = startDate.plusMonths(11);
    Month endMonth = tempEnd.getMonth();

    int lastDay;
    if (endMonth.equals(Month.APRIL) || endMonth.equals(Month.JUNE) || endMonth.equals(
        Month.SEPTEMBER) || endMonth.equals(Month.NOVEMBER)) {
      lastDay = 30;
    } else if (endMonth.equals(Month.FEBRUARY)) {
      if ((currentYear + 1) % 4 == 0) {
        lastDay = 29;
      } else {
        lastDay = 28;
      }
    } else {
      lastDay = 31;
    }

    LocalDate endDate = LocalDate.of(tempEnd.getYear(), endMonth, lastDay);
    return Pair.of(startDate, endDate);
  }

  private PeakCalendarDate findLastVersion(LocalDate calenderDate) {
    LocalDate lastCalenderDate;
    if (calenderDate.getMonth().equals(Month.FEBRUARY) && calenderDate.getDayOfMonth() == 29) {
      lastCalenderDate = calenderDate.minusYears(4);
    } else {
      lastCalenderDate = calenderDate.minusYears(1);
    }

    // 找最新版的過去日期設定
    List<PeakCalendarDate> historyDates = repo.findByCalendarDate(lastCalenderDate);
    historyDates.sort(
        Comparator.comparing(PeakCalendarDate::getEffDate, Comparator.reverseOrder()));

    return historyDates.isEmpty() ? null : historyDates.get(0);
  }

  private PeakCalendarDateData translatePubMsg(PeakCalendarDate entity) {
    PeakCalendarDateData msg = new PeakCalendarDateData();
    msg.setEffDate(entity.getEffDate());
    msg.setCalendarDate(entity.getCalendarDate());
    msg.setPeakTimeCode(
        Objects.nonNull(entity.getPeakTime()) ? entity.getPeakTime().getCode() : null);
    msg.setPeakWeekday(Objects.nonNull(entity.getPeakWeekday()));
    return msg;
  }

  private void publishBatchReplacedEventMsg(List<PeakCalendarDateData> msgPayload) {
    if (msgPayload.isEmpty()) {
      return;
    }
    BatchPeakCalendarDateReplaced replaced = new BatchPeakCalendarDateReplaced(
        interceptor.getUserId(),
        interceptor.getCorrelationId());
    replaced.setMsgPayload(msgPayload);
    applicationEventPublisher.publishEvent(replaced);
  }
}
