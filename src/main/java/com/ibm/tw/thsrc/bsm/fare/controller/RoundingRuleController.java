/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.event.ExceptionMessages;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.fa.api.RoundingRuleApi;
import com.ibm.tw.thsrc.bsm.fa.dto.RoundingRuleInput;
import com.ibm.tw.thsrc.bsm.fa.dto.RoundingRuleOutput;
import com.ibm.tw.thsrc.bsm.fare.service.RoundingRuleCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.RoundingRuleQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/RoundingRules")
public class RoundingRuleController implements RoundingRuleApi {

  @Autowired
  private RoundingRuleCommandService commandService;
  @Autowired
  private RoundingRuleQueryService queryService;

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private EventStore eventStore;

  @Autowired
  private ExceptionMessages exceptionMessages;

  @Override
  public RoundingRuleOutput create(RoundingRuleInput input) {
    eventStore.validateFunctionLock(OperationFunction.ROUNDING_RULE);
    Long id = commandService.create(input);
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
    return queryService.read(id);
  }

  @Override
  public RoundingRuleOutput replace(Long id, RoundingRuleInput input) {
    eventStore.validateFunctionLock(OperationFunction.ROUNDING_RULE);
    commandService.update(id, input);
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
    return queryService.read(id);
  }

  @Override
  public Page<RoundingRuleOutput> search(Search search) {
    return queryService.search(search);
  }

}
