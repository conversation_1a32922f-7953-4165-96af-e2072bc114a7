/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.SharedErrorCode;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryInput;
import com.ibm.tw.thsrc.bsm.fare.domain.event.PromotionCategoryReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionCategory;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionCategoryMapping;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionCategoryMappingRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionCategoryRepository;
import com.ibm.tw.thsrc.bsm.message.fare.PromotionCategoryData;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class PromotionCategoryCommandService extends
    AbstractCommandService<PromotionCategory, PromotionCategoryInput> {

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private PromotionCategoryRepository repo;

  @Autowired
  private PromotionCategoryMappingRepository promotionMappingRepo;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Override
  public Long create(PromotionCategoryInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void update(Long id, PromotionCategoryInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void delete(Long id) {
    throw new UnsupportedOperationException();
  }

  public Set<Long> patch(CollectionModificationInput<PromotionCategoryInput> changeSet) {
    List<PromotionCategoryData> msgList = new ArrayList<>();

    changeSet.getReplacements().entrySet().stream()//
        .forEach(inputById -> {

          PromotionCategory entity =
              repo.findById(inputById.getKey())
                  .orElseThrow(() -> SharedErrorCode.COLLECTION_PATCH_INPUT_ID_NOT_EXIST
                      .toException(String.valueOf(inputById)));
          this.validateConcurrency(entity, inputById.getValue().getDataVersion());
          this.writeAggregateRootValue(entity, inputById.getValue());
          validateInvariants(entity);

          if (!entity.getName().isEmpty()) {
            msgList.add(translatePubMsg(entity));
          }

          repo.save(entity);
        });

    if (!msgList.isEmpty()) {
      this.publishReplacedEvent(msgList);
    }

    return new HashSet<>();
  }

  @Override
  protected void validateInvariants(PromotionCategory entity) throws UnprocessableException {
    Set<LocalDate> existMapping = promotionMappingRepo.findByCategory(entity).stream()
        .map(PromotionCategoryMapping::getEffDate).collect(Collectors.toSet());

    if ((Objects.isNull(entity.getName()) || entity.getName().isEmpty())
        && !existMapping.isEmpty()) {
      Pair<LocalDate, LocalDate> mappingRecentFuture = getRecentFutureEffDate();
      // 名稱為空不會下傳，若有被現在版/未來版使用到->不能在 MEIG 刪除
      if ((Objects.nonNull(mappingRecentFuture.getRight()) && existMapping.contains(
          mappingRecentFuture.getRight())) || (Objects.nonNull(mappingRecentFuture.getLeft())
          && existMapping.contains(mappingRecentFuture.getLeft()))) {

        throw FareErrorCode.FA_PROMOTION_CATEGORY_CANNOT_DELETE_IN_USED_CATEGORY.toException(
            entity.getDisplayOrder().toString(), entity.getName());
      }
    }
  }

  @Override
  protected void writeAggregateRootValue(PromotionCategory entity, PromotionCategoryInput input) {
    entity.setName(input.getName());
    entity.setDisplayOrder(input.getDisplayOrder());
  }

  private PromotionCategoryData translatePubMsg(PromotionCategory entity) {
    PromotionCategoryData msg = new PromotionCategoryData();
    msg.setName(entity.getName());
    msg.setDisplayOrder(entity.getDisplayOrder());
    return msg;
  }

  private void publishReplacedEvent(List<PromotionCategoryData> msgPayload) {
    PromotionCategoryReplaced replaced = new PromotionCategoryReplaced(interceptor.getUserId(),
        interceptor.getCorrelationId());
    replaced.setMsgPayload(msgPayload);
    applicationEventPublisher.publishEvent(replaced);
  }

  private Pair<LocalDate, LocalDate> getRecentFutureEffDate() {
    LocalDate futureDate = null;
    LocalDate recentDate = null;

    Set<LocalDate> effDates = promotionMappingRepo.findAll().stream()
        .map(PromotionCategoryMapping::getEffDate).collect(Collectors.toSet());
    LocalDate newestDate = Collections.max(effDates);

    if (newestDate.isAfter(LocalDate.now())) {
      futureDate = newestDate;
    } else {
      recentDate = newestDate;
    }

    if (Objects.nonNull(futureDate)) {
      effDates.remove(newestDate);
      LocalDate secondNewDate = Collections.max(effDates);
      recentDate = secondNewDate;
    }

    return Pair.of(recentDate, futureDate);
  }
}
