/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.api.PeakTimeNameApi;
import com.ibm.tw.thsrc.bsm.fa.dto.PeakTimeOutput;
import com.ibm.tw.thsrc.bsm.fare.service.PeakTimeNameQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/PeakTimeNames")
public class PeakTimeNameController implements PeakTimeNameApi {

  @Autowired
  private PeakTimeNameQueryService queryServices;

  @Override
  public Page<PeakTimeOutput> search(Search search) {
    return queryServices.search(search);
  }
}
