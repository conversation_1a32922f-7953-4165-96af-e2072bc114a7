/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.fa.api.PromotionCategoryApi;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryOutput;
import com.ibm.tw.thsrc.bsm.fare.service.PromotionCategoryCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.PromotionCategoryQueryService;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/PromotionCategories")
public class PromotionCategoryController implements PromotionCategoryApi {

  @Autowired
  private PromotionCategoryCommandService commandService;

  @Autowired
  private PromotionCategoryQueryService queryService;

  @Autowired
  private EventStore eventStore;

  @Override
  public Set<PromotionCategoryOutput> modify(
      CollectionModificationInput<PromotionCategoryInput> changeSet) {
    eventStore.validateFunctionLock(OperationFunction.PROMOTION_CATEGORY);
    Set<Long> ids = commandService.patch(changeSet);
    ids.addAll(changeSet.getReplacements().keySet());
    return queryService.read(ids);
  }

  @Override
  public Page<PromotionCategoryOutput> search(Search search) {
    return queryService.search(search);
  }
}
