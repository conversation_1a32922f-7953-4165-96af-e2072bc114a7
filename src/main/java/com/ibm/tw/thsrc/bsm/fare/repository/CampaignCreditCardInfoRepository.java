/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fa.enums.CreditCardCampaignType;
import com.ibm.tw.thsrc.bsm.fare.entity.CampaignCreditCardInfo;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface CampaignCreditCardInfoRepository extends
JpaRepository<CampaignCreditCardInfo, Long>,
JpaSpecificationExecutor<CampaignCreditCardInfo> {

  Optional<CampaignCreditCardInfo> findByCardNoAndEffDateAndDscDateAndEffSaleDateAndDscSaleDateAndCreditCardCampaignType(
      String cardNo, LocalDate effDate, LocalDate dscDate, LocalDate effSaleDate, LocalDate dscSaleDate, CreditCardCampaignType creditCardCampaignType);

  Optional<List<CampaignCreditCardInfo>> findByCardNoAndCreditCardCampaignType(
      String cardNo, CreditCardCampaignType creditCardCampaignType);
  
  Optional<List<CampaignCreditCardInfo>> findByCreditCardCampaignType(CreditCardCampaignType creditCardCampaignType);
}
