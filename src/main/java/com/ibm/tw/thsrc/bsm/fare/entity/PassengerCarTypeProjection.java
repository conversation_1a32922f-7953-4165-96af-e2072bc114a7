/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.entity;

import com.ibm.tw.thsrc.bsm.core.entity.BaseEntity;
import com.ibm.tw.thsrc.bsm.sc.enums.CompartmentType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.Getter;
import lombok.ToString;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.Subselect;
import org.hibernate.annotations.Synchronize;

@Getter
@ToString(callSuper = true)
@Entity
@Subselect(
    "SELECT p.ID,"
        + " p.CODE,"
        + " p.EN_NAME,"
        + " p.ZH_NAME,"
        + " p.COMPARTMENT_TYPE,"
        + " p.CREATE_TIMESTAMP,"
        + " p.UPDATE_TIMESTAMP"
        + " FROM sc.PASSENGER_CAR_TYPE p")
@Synchronize({"sc.PASSENGER_CAR_TYPE"})
@Immutable
public class PassengerCarTypeProjection extends BaseEntity {

  private String code;

  private String enName;

  @Column(length = 255)
  private String zhName;

  @Enumerated(EnumType.STRING)
  private CompartmentType compartmentType;

}
