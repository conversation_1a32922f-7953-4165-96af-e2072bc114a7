/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionCategory;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionCategory_;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionCategoryRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.PromotionCategoryFilter;
import com.ibm.tw.thsrc.bsm.fare.vo.PromotionCategorySort;
import com.ibm.tw.thsrc.bsm.util.StringUtils;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class PromotionCategoryQueryService extends
    AbstractQueryService<PromotionCategory, PromotionCategoryOutput, PromotionCategoryFilter, PromotionCategorySort>
    implements PromotionCategoryQueryApi {

  @Autowired
  private ObjectMapper objectMapper;

  @Autowired
  private PromotionCategoryRepository repo;

  @Override
  public PromotionCategoryOutput read(Long id) {
    throw new UnsupportedOperationException();
  }


  public Set<PromotionCategoryOutput> read(Set<Long> ids) {
    return repo.findAllById(ids).stream().map(this::translate).collect(Collectors.toSet());
  }

  @Override
  public Page<PromotionCategoryOutput> search(Search searchRequest) {
    PageRequest pr = translateToPageRequest(searchRequest);
    Specification<PromotionCategory> spec = translateToSpecification(searchRequest);
    Page<PromotionCategory> page = repo.findAll(spec, pr);
    return page.map(this::translate);
  }

  @Override
  public PromotionCategoryOutput translate(PromotionCategory entity) {
    PromotionCategoryOutput response = new PromotionCategoryOutput();
    response.setId(entity.getId());
    response.setName(entity.getName());
    response.setDisplayOrder(entity.getDisplayOrder());
    response.setDataVersion(entity.getDataVersion());

    return response;
  }

  @Override
  protected Sort translate(PromotionCategorySort sortBy, Direction sortDirection) {
    return Sort.unsorted();
  }

  @Override
  protected Predicate translate(ComparisonExpression<PromotionCategoryFilter> expression,
      Root<PromotionCategory> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {

    if (expression.getAttributeValue().getHasName() != null) {
      Path<String> path = root.get(PromotionCategory_.NAME);
      boolean value = expression.getAttributeValue().getHasName();
      if (value) {
        return translate(path, ComparisonOperator.NE, StringUtils.EMPTY, criteriaBuilder);

      } else {
        return null;
      }

    } else {
      return null;
    }
  }

  @Override
  protected Function<Map<String, Boolean>, PromotionCategorySort> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, PromotionCategorySort.class);
  }

  @Override
  protected Function<Map<String, Object>, PromotionCategoryFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, PromotionCategoryFilter.class);
  }

  @Override
  public List<PromotionCategoryOutput> getPromotionCategory() {
    // 有名稱才下傳
    return repo.findAll().stream().filter(e -> !e.getName().isEmpty()).map(this::translate)
        .collect(Collectors.toList());
  }
}
