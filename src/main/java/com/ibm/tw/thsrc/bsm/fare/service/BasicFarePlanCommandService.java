/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source
 * code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.DateRangeDto;
import com.ibm.tw.thsrc.bsm.core.entity.DateRange;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.exception.SharedErrorCode;
import com.ibm.tw.thsrc.bsm.fa.dto.BasicFarePlanInput;
import com.ibm.tw.thsrc.bsm.fa.enums.EligiblePassengerProfileType;
import com.ibm.tw.thsrc.bsm.fa.enums.FareRule;
import com.ibm.tw.thsrc.bsm.fare.domain.event.BasicFarePlanCreated;
import com.ibm.tw.thsrc.bsm.fare.domain.event.BasicFarePlanDeleted;
import com.ibm.tw.thsrc.bsm.fare.domain.event.BasicFarePlanReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePlan;
import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePrice;
import com.ibm.tw.thsrc.bsm.fare.entity.ClassProjection;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrain;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProjectMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.FareRuleSetting;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerCarTypeProjection;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerProfile;
import com.ibm.tw.thsrc.bsm.fare.repository.BasicFarePlanRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.BasicFarePriceRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.ClassProjectionRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.EligibleTrainRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.FareProjectMappingRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PassengerCarTypeProjectionRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PassengerProfileRepository;
import com.ibm.tw.thsrc.bsm.message.DateRangeData;
import com.ibm.tw.thsrc.bsm.message.fare.BasicFarePlanData;
import com.ibm.tw.thsrc.bsm.message.fare.FareRuleSettingData;
import com.ibm.tw.thsrc.bsm.message.fare.PassengerProfileData;
import com.ibm.tw.thsrc.bsm.message.schedule.ClassData;
import com.ibm.tw.thsrc.bsm.message.schedule.PassengerCarTypeData;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional
@Service
public class BasicFarePlanCommandService
    extends AbstractCommandService<BasicFarePlan, BasicFarePlanInput> {

  private static final String OPEN_DATE = "#";

  @Autowired
  private BasicFarePlanRepository repo;
  @Autowired
  private PassengerCarTypeProjectionRepository carTypeRepo;
  @Autowired
  private PassengerProfileRepository profileRepo;
  @Autowired
  private ClassProjectionRepository classRepo;
  @Autowired
  private EligibleTrainRepository eligibleTrainRepo;
  @Autowired
  private FareProjectMappingRepository projectMappingRepo;
  @Autowired
  private BasicFarePriceRepository basicFarePriceRepo;
  @Autowired
  private HttpHeaderInterceptor interceptor;
  @Autowired
  private EventStore eventStore;


  @Override
  public Long create(BasicFarePlanInput input) {
    if (repo.findByCode(input.getCode()).isPresent()) {
      throw FareErrorCode.FA_FARE_PLAN_CODE_CANNOT_REPEAT.toException(input.getCode());
    }
    BasicFarePlan entity = new BasicFarePlan();
    entity.setEligiblePassengerProfileType(EligiblePassengerProfileType.VALID);
    this.writeAggregateRootValue(entity, input);
    this.validateInvariants(entity);

    entity = repo.save(entity);

    BasicFarePlanCreated event = new BasicFarePlanCreated(this.translateMsgData(entity),
        interceptor.getUserId(), interceptor.getCorrelationId());
    event.setOperationId(OperationFunction.BASIC_FARE_PLAN.name());
    event.setAssociationId(interceptor.getCorrelationId());
    eventStore.publishEvent(event);

    return entity.getId();

  }

  @Override
  public void update(Long id, BasicFarePlanInput input) {
    BasicFarePlan entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    BasicFarePlanData previousData = translateMsgData(entity);

    if (!StringUtils.equals(input.getCode(), entity.getCode())) {
      throw FareErrorCode.FA_FARE_PLAN_CODE_UPDATE_NOT_ALLOWED.toException();
    }
    this.validateConcurrency(entity, input.getDataVersion());
    this.writeAggregateRootValue(entity, input);
    this.validateInvariants(entity);

    BasicFarePlanReplaced event = new BasicFarePlanReplaced(this.translateMsgData(entity),
        previousData, interceptor.getUserId(), interceptor.getCorrelationId());
    event.setOperationId(OperationFunction.BASIC_FARE_PLAN.name());
    event.setAssociationId(interceptor.getCorrelationId());
    eventStore.publishEvent(event);
  }

  @Override
  public void delete(Long id) {
    BasicFarePlan entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);

    List<BasicFarePrice> basicFarePrices = basicFarePriceRepo.findByBasicFarePlan(entity,
        Sort.sort(BasicFarePrice.class).by(BasicFarePrice::getEffDate).descending());

    if (!basicFarePrices.isEmpty()) {
      throw FareErrorCode.FA_BASIC_FARE_PLAN_CANNOT_BE_DELETED_WHEN_PRICES_EXIST.toException();
    }

    List<EligibleTrain> eligibleTrainUsingPromotion = eligibleTrainRepo.findByFarePlan(entity);
    if (!eligibleTrainUsingPromotion.isEmpty()) {
      throw FareErrorCode.FA_BASIC_FARE_PLAN_CANNOT_DELETE_PROMOTION_WHEN_HAS_ELIGIBLE_TRAIN_SETTING
          .toException();
    }

    List<FareProjectMapping> projectMappingUsingPromotion =
        projectMappingRepo.findByBasicFarePlan(entity);
    if (!projectMappingUsingPromotion.isEmpty()) {
      throw FareErrorCode.FA_BASIC_FARE_PLAN_CANNOT_DELETE_PROMOTION_WHEN_HAS_PROJECT_MAPPING_SETTING
          .toException();
    }

    BasicFarePlanDeleted event = new BasicFarePlanDeleted(this.translateMsgData(entity),
        interceptor.getUserId(), interceptor.getCorrelationId());
    event.setOperationId(OperationFunction.BASIC_FARE_PLAN.name());
    event.setAssociationId(interceptor.getCorrelationId());
    eventStore.publishEvent(event);

    repo.delete(entity);
  }

  private BasicFarePlanData translateMsgData(BasicFarePlan entity) {
    BasicFarePlanData data = new BasicFarePlanData();
    data.setAllClassesValid(entity.isAllClassesValid());
    data.setCode(entity.getCode());
    data.setDisplayCode(entity.getDisplayCode());
    data.setDscDate(entity.getDscDate());
    data.setEffDate(entity.getEffDate());
    data.setEligiblePassengerProfileType(String.valueOf(entity.getEligiblePassengerProfileType()));
    data.setFareType(String.valueOf(entity.getFareType()));
    data.setName(entity.getName());
    data.setServiceType(String.valueOf(entity.getServiceType()));
    data.setType(String.valueOf(entity.getType()));

    PassengerCarTypeData passengerCarTypeData =
        PassengerCarTypeData.builder().code(entity.getPassengerCarType().getCode())//
            .compartmentType(String.valueOf(entity.getPassengerCarType().getCompartmentType()))//
            .enName(entity.getPassengerCarType().getEnName())//
            .zhName(entity.getPassengerCarType().getZhName())//
            .build();

    data.setPassengerCarType(passengerCarTypeData);

    entity.getFareRuleSettings()
        .forEach(e -> data.getFareRuleSettings().add(FareRuleSettingData.builder()//
            .fareRule(String.valueOf(e.getFareRule()))//
            .setting(e.getSetting())//
            .build()));

    entity.getInvalidDateRanges()
        .forEach(e -> data.getInvalidDateRanges().add(DateRangeData.builder()//
            .beginDate(e.getBeginDate())//
            .endDate(e.getEndDate())//
            .build()));

    entity.getPassengerProfiles()
        .forEach(e -> data.getPassengerProfiles().add(PassengerProfileData.builder()//
            .code(e.getCode())//
            .enName(e.getEnName())//
            .enPrintName(e.getEnPrintName())//
            .zhName(e.getZhName())//
            .zhPrintName(e.getZhPrintName())//
            .tbLightColor(e.getTbLightColor())//
            .id(e.getId())//
            .build()));

    entity.getValidClasses().forEach(e -> data.getValidClasses().add(ClassData.builder()//
        .baseClass(e.getBaseClass().getCode())//
        .code(e.getCode())//
        .build()));

    return data;
  }


  @Override
  protected void validateInvariants(BasicFarePlan entity) {
    if (Objects.isNull(entity.getEffDate()) && Objects.nonNull(entity.getDscDate())) {
      throw SharedErrorCode.EFF_DATE_NOT_ALLOW_EMPTY_WHEN_DSC_DATE_NOT_EMPTY.toException();
    }

    checkInvalidDatesOverlap(entity.getInvalidDateRanges());
  }

  private void checkInvalidDatesOverlap(List<DateRange> invalidDateRanges) {

    int startDateOverlaps = invalidDateRanges.stream().map(DateRange::getBeginDate)
        .mapToInt(date -> overlap(date, invalidDateRanges)).max().orElse(0);
    int endDateOverlaps = invalidDateRanges.stream().map(DateRange::getEndDate)
        .mapToInt(date -> overlap(date, invalidDateRanges)).max().orElse(0);
    int maxOverlaps = Integer.max(startDateOverlaps, endDateOverlaps);
    if (maxOverlaps > 0) {
      throw SharedErrorCode.DATE_RANGE_OVERLAP.toException();
    }
  }

  private Integer overlap(LocalDate date, List<DateRange> dateRanges) {
    return dateRanges.stream().mapToInt(dateRange -> (!(date.isBefore(dateRange.getBeginDate())
        || date.isAfter(dateRange.getEndDate()))) ? 1 : 0).sum() - 1;
  }

  @Override
  protected void writeAggregateRootValue(BasicFarePlan entity, BasicFarePlanInput input) {

    PassengerCarTypeProjection carType =
        carTypeRepo.findById(input.getPassengerCarTypeId()).orElseThrow(
            FareErrorCode.FA_BASIC_FARE_PLAN_CANNOT_ASSOCIATE_TO_THE_PASSENGER_CAR_TYPE::toException);
    entity.setPassengerCarType(carType);
    entity.setDisplayCode(input.getDisplayCode());
    
    // 在translate 中會比對原本發車起迄日，比對完在清除entity 資料
    List<FareRuleSetting> settings = translate(input, entity);
    entity.getFareRuleSettings().clear();
    entity.getFareRuleSettings().addAll(settings);

    entity.setCode(input.getCode());
    entity.setName(input.getName());
    entity.setEffDate(input.getEffDate());
    entity.setDscDate(input.getDscDate());
    entity.setType(input.getType());
    entity.setServiceType(input.getServiceType());
    entity.setFareType(input.getFareType());
    entity.setType(input.getType());

    List<ClassProjection> validClasses = entity.getValidClasses();
    validClasses.clear();

    input.getValidClassIds().forEach(id -> {
      ClassProjection classProjection = classRepo.findById(id).orElseThrow(
          FareErrorCode.FA_FARE_PLAN_CANNOT_ASSOCIATE_TO_CLASS_PROJECTION::toException);
      validClasses.add(classProjection);
    });

    List<PassengerProfile> profiles = entity.getPassengerProfiles();
    profiles.clear();

    if (EligiblePassengerProfileType.INVALID.equals(entity.getEligiblePassengerProfileType())) {
      profiles.addAll(profileRepo.findByIdNotIn(input.getValidPassengerProfileIds()));
    } else if (!EligiblePassengerProfileType.ALL.equals(entity.getEligiblePassengerProfileType())) {
      input.getValidPassengerProfileIds().forEach(id -> {
        PassengerProfile profile = profileRepo.findById(id).orElseThrow(
            FareErrorCode.FA_FARE_PLAN_CANNOT_ASSOCIATE_TO_PASSENGER_PROFILE::toException);
        profiles.add(profile);
      });
    }

    List<DateRange> invalidDateRange = entity.getInvalidDateRanges();
    invalidDateRange.clear();

    input.getInvalidDateRanges().sort(Comparator.comparing(DateRangeDto::getBeginDate));
    input.getInvalidDateRanges().forEach(inputRange -> {
      DateRange dateRange = new DateRange(inputRange.getBeginDate(), inputRange.getEndDate());
      invalidDateRange.add(dateRange);
    });

    // 只有修改一般票價維護時，需要檢核票價的起訖日需被方案的是籲起訖日涵括
    if (entity.getId() != null) {
      this.validateFareEffDateRange(entity);
    }

  }

  private void validateFareEffDateRange(BasicFarePlan farePlan) {
    List<BasicFarePrice> prices = new ArrayList<>(basicFarePriceRepo.findByBasicFarePlan(farePlan));

    LocalDate planEffDate = farePlan.getEffDate() == null ? LocalDate.MIN : farePlan.getEffDate();
    LocalDate planDscDate = farePlan.getDscDate() == null ? LocalDate.MAX : farePlan.getDscDate();

    for (BasicFarePrice price : prices) {

      LocalDate priceEffDate = price.getEffDate() == null ? LocalDate.MIN : price.getEffDate();
      LocalDate priceDscDate = price.getDscDate() == null ? LocalDate.MAX : price.getDscDate();

      if (priceEffDate.isBefore(planEffDate) || priceDscDate.isAfter(planDscDate)) {
        throw FareErrorCode.FA_BASIC_FARE_PLAN_EFF_DATE_INVALIDATES_ITS_FARE_PRICE.toException(
            this.getDateText(price.getEffDate()), this.getDateText(price.getDscDate()));

      }
    }
  }

  private String getDateText(LocalDate date) {
    return date == null ? "#" : date.toString();
  }

  private List<FareRuleSetting> translate(BasicFarePlanInput input, BasicFarePlan entity) {
    List<FareRuleSetting> settings = new ArrayList<>();
    DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    // R08
    FareRuleSetting r08setting = new FareRuleSetting(FareRule.R08_CHANGE_RULE,
        String.valueOf(input.getModifiable()), entity);
    settings.add(r08setting);

    // R12
    FareRuleSetting r12setting =
        new FareRuleSetting(FareRule.R12_VALID_WEEKDAY, input.getFrequency(), entity);
    settings.add(r12setting);

    // R16
    FareRuleSetting r16Setting = new FareRuleSetting(FareRule.R16_IMMEDIATE_PAYMENT,
        String.valueOf(input.getImmediatePayment()), entity);

    settings.add(r16Setting);

    // R20
    FareRuleSetting r20Setting;
    if (Objects.nonNull(input.getContent()) && StringUtils.isNotBlank(input.getContent())) {
      r20Setting = new FareRuleSetting(FareRule.R20_CONTENT, input.getContent(), entity);

    } else {
      r20Setting = new FareRuleSetting(FareRule.R20_CONTENT, StringUtils.EMPTY, entity);
    }
    settings.add(r20Setting);

    // R21
    FareRuleSetting r21Setting;
    if (Objects.nonNull(input.getDescription()) && StringUtils.isNotBlank(input.getDescription())) {
      r21Setting = new FareRuleSetting(FareRule.R21_DESCRIPTION, input.getDescription(), entity);

    } else {
      r21Setting = new FareRuleSetting(FareRule.R21_DESCRIPTION, StringUtils.EMPTY, entity);
    }
    settings.add(r21Setting);

    // R34
    FareRuleSetting r34Setting = new FareRuleSetting(FareRule.R34_ELIGIBLE_TRAIN,
        String.valueOf(input.getEligibleTrainType()), entity);
    settings.add(r34Setting);

    // R27
    FareRuleSetting r27setting = new FareRuleSetting(FareRule.R27_DSC_DATE, entity);
    if (Objects.nonNull(input.getDscDate())) {
      r27setting.setSetting(input.getDscDate().format(dtf));
    } else {
      r27setting.setSetting(OPEN_DATE);
    }

    // R26
    FareRuleSetting r26setting = new FareRuleSetting(FareRule.R26_EFF_DATE, entity);
    r26setting.setSetting(input.getEffDate().format(dtf));

    // R09
    FareRuleSetting r09setting = new FareRuleSetting(FareRule.R09_DSC_SALE_DATE, entity);
    if (Objects.nonNull(input.getDscSaleDate())) {
      r09setting.setSetting(input.getDscSaleDate().format(dtf));
    } else {
      r09setting.setSetting(OPEN_DATE);
    }
    // R51
    FareRuleSetting r51setting = new FareRuleSetting(FareRule.R51_EFF_SALE_DATE, entity);
    if (Objects.nonNull(input.getEffSaleDate())) {
      r51setting.setSetting(input.getEffSaleDate().format(dtf));
    } else {
      r51setting.setSetting(OPEN_DATE);
    }

    Optional<FareRuleSetting> effSaleDateOption = entity.getFareRuleSettings().stream()
        .filter(e -> FareRule.R51_EFF_SALE_DATE.equals(e.getFareRule())).findFirst();

    LocalDate effSaleDate = null;
    if (effSaleDateOption.isPresent() && !OPEN_DATE.equals(effSaleDateOption.get().getSetting())) {
      effSaleDate = LocalDate.parse(effSaleDateOption.get().getSetting(), dtf);
    }

    // 需判斷要先下那一個日期，除了日期往前外，一律先下迄日再下起日
    if (Objects.nonNull(entity.getEffDate()) &&
        Objects.nonNull(input.getDscDate()) &&
        input.getDscDate().isBefore(entity.getEffDate())) {
      settings.add(r26setting);
      settings.add(r27setting);
    } else {
      settings.add(r27setting);
      settings.add(r26setting);
    }

    if (Objects.nonNull(effSaleDate) &&
        Objects.nonNull(input.getDscSaleDate()) &&
        input.getDscSaleDate().isBefore(effSaleDate)) {
      settings.add(r51setting);
      settings.add(r09setting);
    } else {
      settings.add(r09setting);
      settings.add(r51setting);
    }

    // R61
    FareRuleSetting r61setting = new FareRuleSetting(FareRule.R61_CHANGE_RULE,
        String.valueOf(input.getModifiable()), entity);
    settings.add(r61setting);

    // R74
    FareRuleSetting r74setting = new FareRuleSetting(FareRule.R74_EARLYBIRD_BOOKING_ONLY,
        String.valueOf(input.getEarlyBirdBookingOnly()), entity);
    settings.add(r74setting);

    return settings;
  }
}
