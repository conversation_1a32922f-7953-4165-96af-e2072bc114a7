/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.RefundFeeRuleLevelOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.RefundFeeRuleOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.RegularTicketRuleOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketProductPolicyOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.RefundFeeCategory;
import com.ibm.tw.thsrc.bsm.fare.entity.RefundFeeRuleLevel;
import com.ibm.tw.thsrc.bsm.fare.entity.RefundFeeRuleLevel_;
import com.ibm.tw.thsrc.bsm.fare.entity.TicketProductPolicy;
import com.ibm.tw.thsrc.bsm.fare.repository.RefundFeeRuleLevelRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.TicketProductPolicyRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.RegularTicketRuleFilter;
import com.ibm.tw.thsrc.bsm.fare.vo.RegularTicketRuleSort;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Transactional
@Service
public class RegularTicketRuleQueryService extends
    AbstractQueryService<RefundFeeRuleLevel, RegularTicketRuleOutput, RegularTicketRuleFilter, RegularTicketRuleSort> {

  @Autowired
  private ObjectMapper objectMapper;
  @Autowired
  private RefundFeeRuleLevelRepository ruleLevelRepo;

  @Autowired
  private TicketProductPolicyRepository policyRepo;

  @Override
  public RegularTicketRuleOutput read(Long id) {
    throw new UnsupportedOperationException();
  }

  public RegularTicketRuleOutput read(Pair<List<Long>, Set<Long>> ids) {
    List<RefundFeeRuleLevel> levels = new ArrayList<>();
    TicketProductPolicy policy = policyRepo.findById(ids.getLeft().get(0))
        .orElseThrow(ResourceNotFoundException::new);
    ids.getRight().forEach(levelId -> levels.add(ruleLevelRepo.findById(levelId)
        .orElseThrow(ResourceNotFoundException::new))
    );

    return translate(levels, policy);
  }


  @Override
  public Page<RegularTicketRuleOutput> search(Search searchRequest) {
    PageRequest pr = translateToPageRequest(searchRequest);
    Specification<RefundFeeRuleLevel> spec = translateToSpecification(searchRequest);
    List<RefundFeeRuleLevel> ruleLevels = ruleLevelRepo.findAll(spec, pr).getContent();

    if (ruleLevels.isEmpty()) {
      return new PageImpl<>(new ArrayList<>());

    } else {
      TicketProductPolicy policy = policyRepo.findByEffDate(ruleLevels.get(0).getEffDate());

      return new PageImpl<>(
          Collections.singletonList(translate(ruleLevels, policy)));
    }
  }

  protected RegularTicketRuleOutput translate(List<RefundFeeRuleLevel> levelEntities,
      TicketProductPolicy policyEntity) {
    RegularTicketRuleOutput result = new RegularTicketRuleOutput();
    TicketProductPolicyOutput policyOutput = new TicketProductPolicyOutput();
    policyOutput.setDataVersion(policyEntity.getDataVersion());
    policyOutput.setId(policyEntity.getId());
    policyOutput.setEffDate(policyEntity.getEffDate());
    policyOutput.setIsPlatFormTktActivated(policyEntity.getIsPlatFormTktActivated());
    policyOutput.setPlatformTktUnitPrice(policyEntity.getPlatformTktUnitPrice());
    policyOutput.setRoundTripTktOutboundValidDays(
        policyEntity.getRoundTripTktOutboundValidDays());
    policyOutput.setRoundTripTktInboundValidDays(policyEntity.getRoundTripTktInboundValidDays());
    policyOutput.setOnewayTktRefundFeeAmt(policyEntity.getOnewayTktRefundFeeAmt());
    policyOutput.setOnewayTktValidDays(policyEntity.getOnewayTktValidDays());
    policyOutput.setReservedSeatTktValidDays(policyEntity.getReservedSeatTktValidDays());
    policyOutput.setCscCardMarketingFeeAmt(policyEntity.getCscCardMarketingFeeAmt());
    policyOutput.setCscCardDepositAmt(policyEntity.getCscCardDepositAmt());

    List<RefundFeeRuleOutput> ruleOutputs = new ArrayList<>();
    levelEntities.forEach(ruleLevel -> {
      RefundFeeRuleOutput output = new RefundFeeRuleOutput();
      if (ruleLevel.getRefundFeeRule().getCategory()
          .equals(RefundFeeCategory.RESERVED_SEAT_INDIVIDUAL)) {
        while (ruleOutputs.isEmpty()) {
          ruleOutputs.add(new RefundFeeRuleOutput());
        }
        output = ruleOutputs.get(0);
      } else if (ruleLevel.getRefundFeeRule().getCategory()
          .equals(RefundFeeCategory.RESERVED_SEAT_GROUP)) {
        while (ruleOutputs.size() < 2) {
          ruleOutputs.add(new RefundFeeRuleOutput());
        }
        output = ruleOutputs.get(1);
      }

      if (output.getId() == null) {
        output.setId(ruleLevel.getRefundFeeRule().getId());
        output.setType(ruleLevel.getRefundFeeRule().getType());
        output.setCategory(ruleLevel.getRefundFeeRule().getCategory());
        output.setDataVersion(ruleLevel.getRefundFeeRule().getDataVersion());
      }

      RefundFeeRuleLevelOutput levelOutput = new RefundFeeRuleLevelOutput();
      levelOutput.setId(ruleLevel.getId());
      levelOutput.setEffDate(ruleLevel.getEffDate());
      levelOutput.setFeePct(ruleLevel.getFeePct());
      levelOutput.setFeeAmt(ruleLevel.getFeeAmt());
      levelOutput.setMinPreDepDays(ruleLevel.getMinPreDepDays());
      levelOutput.setMaxPreDepDays(ruleLevel.getMaxPreDepDays());

      output.getRuleLevels().add(levelOutput);

      if (ruleLevel.getRefundFeeRule().getCategory()
          .equals(RefundFeeCategory.RESERVED_SEAT_INDIVIDUAL)) {
        ruleOutputs.set(0, output);
      } else if (ruleLevel.getRefundFeeRule().getCategory()
          .equals(RefundFeeCategory.RESERVED_SEAT_GROUP)) {
        ruleOutputs.set(1, output);
      }
    });

    result.setTicketProductPolicy(policyOutput);
    result.setRefundFeeRules(ruleOutputs);
    return result;
  }

  @Override
  protected RegularTicketRuleOutput translate(RefundFeeRuleLevel aggregateRoot) {
    return null;
  }

  @Override
  protected Sort translate(RegularTicketRuleSort sortBy, Direction direction) {
    return Sort.sort(RefundFeeRuleLevel.class);
  }

  @Override
  protected Predicate translate(ComparisonExpression<RegularTicketRuleFilter> expression,
      Root<RefundFeeRuleLevel> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {

    if (expression.getAttributeValue().getIsFuture() != null) {
      Path<LocalDate> path = root.get(RefundFeeRuleLevel_.EFF_DATE);
      boolean isFuture = expression.getAttributeValue().getIsFuture();

      ComparisonOperator op;
      LocalDate value = LocalDate.now();

      if (isFuture) {
        op = ComparisonOperator.GT;

      } else {
        op = ComparisonOperator.EQ;
        Optional<RefundFeeRuleLevel> newest = ruleLevelRepo.findAll().stream()
            .filter(e -> !e.getEffDate().isAfter(LocalDate.now()))
            .max(Comparator.comparing(RefundFeeRuleLevel::getEffDate));
        if (newest.isPresent()) {
          value = newest.get().getEffDate();
        }
      }

      return translate(path, op, value, criteriaBuilder);

    } else {
      return null;
    }
  }

  @Override
  protected Function<Map<String, Boolean>, RegularTicketRuleSort> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, RegularTicketRuleSort.class);
  }

  @Override
  protected Function<Map<String, Object>, RegularTicketRuleFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, RegularTicketRuleFilter.class);
  }
}
