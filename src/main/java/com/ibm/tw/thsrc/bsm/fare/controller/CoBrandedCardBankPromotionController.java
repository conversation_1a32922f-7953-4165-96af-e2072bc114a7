/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.fa.api.CoBrandedCardBankPromotionApi;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardBankPromotionInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardBankPromotionOutput;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardBankPromotionCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardBankPromotionQueryService;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/CoBrandedCardBankPromotions")
public class CoBrandedCardBankPromotionController implements CoBrandedCardBankPromotionApi {

  @Autowired
  private CoBrandedCardBankPromotionCommandService commandService;
  @Autowired
  private CoBrandedCardBankPromotionQueryService queryService;

  @Autowired
  private EventStore eventStore;

  @Override
  public Page<CoBrandedCardBankPromotionOutput> search(Search search) {
    return queryService.search(search);
  }

  @Override
  public Set<CoBrandedCardBankPromotionOutput> modify(
      CollectionModificationInput<CoBrandedCardBankPromotionInput> changeSet) {
    eventStore.validateFunctionLock(OperationFunction.CO_BRANDED_CARD_BANK_PROMOTION);
    Set<Long> ids = commandService.patch(changeSet);
    ids.addAll(changeSet.getReplacements().keySet());
    return queryService.read(ids);
  }
}
