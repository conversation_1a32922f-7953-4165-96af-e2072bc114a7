/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.translate;

import com.ibm.tw.thsrc.bsm.fa.dto.FreeSeatingTicketTypeOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.FreeSeatingTicketType;

public class FreeSeatingTicketTypeTranslator {

  private FreeSeatingTicketTypeTranslator() {
    throw new IllegalStateException("FreeSeatingTicketTypeTranslator class");
  }

  public static FreeSeatingTicketTypeOutput toFreeSeatingTicketTypeOutput(
      FreeSeatingTicketType aggregateRoot) {
    FreeSeatingTicketTypeOutput output = new FreeSeatingTicketTypeOutput();
    output.setId(aggregateRoot.getId());
    output.setCode(aggregateRoot.getCode());
    output.setName(aggregateRoot.getName());
    output.setDataVersion(aggregateRoot.getDataVersion());
    return output;
  }
}
