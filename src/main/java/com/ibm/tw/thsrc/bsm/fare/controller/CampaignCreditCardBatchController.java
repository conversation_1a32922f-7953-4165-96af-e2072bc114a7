/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationOutput;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.fa.api.CampaignCreditCardBatchApi;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardBatchInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardBatchOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.CardInfoRemovalInput;
import com.ibm.tw.thsrc.bsm.fare.service.CampaignCreditCardBatchCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.CampaignCreditCardBatchQueryService;
import java.time.LocalDate;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/CampaignCreditCardBatches")
public class CampaignCreditCardBatchController implements CampaignCreditCardBatchApi {

  @Autowired
  private CampaignCreditCardBatchCommandService commandService;
  @Autowired
  private CampaignCreditCardBatchQueryService queryService;
  @Autowired
  private HttpHeaderInterceptor interceptor;
  @Autowired
  private EventStore eventStore;

  /**
   * 主畫面 檔案匯入(含疏運期自動拆分), 刪除多筆
   */
  @Override
  public CollectionModificationOutput bulk(
      CollectionModificationInput<CampaignCreditCardBatchInput> changeSet) {
    OperationFunction opFunction = null;
    if (!changeSet.getCreations().isEmpty()) {
      opFunction = queryService.getOpFunction(changeSet.getCreations().get(0));
    } else if (!changeSet.getDeletions().isEmpty()) {
      CampaignCreditCardBatchOutput out =
          queryService.read(changeSet.getDeletions().iterator().next());
      opFunction = queryService.getOpFunction(out);
    }

    if (Objects.nonNull(opFunction)) {
      eventStore.validateFunctionLock(opFunction);
    }

    return commandService.bulk(changeSet);
  }

  /**
   * 主畫面 查詢列表 by bankName 刪除單一卡號 查詢列表 by cardNo
   */
  @Override
  public Page<CampaignCreditCardBatchOutput> search(Search search) {
    return queryService.search(search);
  }

  /**
   * 主畫面 查詢單筆
   */
  @Override
  public CampaignCreditCardBatchOutput read(Long id) {
    return queryService.read(id);
  }

  /**
   * 刪除單筆卡號 (若遇到為只剩一筆卡號之批次，則刪除該批次)
   */
  @Override
  public CollectionModificationOutput cardInfoRemovals(CardInfoRemovalInput input) {
    // 可手動鎖功能，所以controller 要留著
    OperationFunction opFunction = queryService.getOpFunction(input.getDeletions().get(0));
    eventStore.validateFunctionLock(opFunction);
    // modify to bulk operation
    return commandService.cardInfoRemovals(input);
  }

  public void clean(LocalDate expiryDate) {
    commandService.clean(expiryDate);
  }
}
