/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fa.enums.FareProjectType;
import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePlan;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProjectMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountPlan;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface FareProjectMappingRepository extends JpaRepository<FareProjectMapping, Long>,
    JpaSpecificationExecutor<FareProjectMapping> {

  Optional<FareProjectMapping> findByFareProjectAndBasicFarePlanAndPromotionPlanAndProfileDiscountPlan(
      FareProject fareProject, BasicFarePlan basicFarePlan, PromotionPlan promotionPlan,
      ProfileDiscountPlan profileDiscountPlan);

  List<FareProjectMapping> findByBasicFarePlanAndPromotionPlan(BasicFarePlan basicFarePlan,
      PromotionPlan promotionPlan);

  List<FareProjectMapping> findByBasicFarePlanAndPromotionPlanAndProfileDiscountPlan(
      BasicFarePlan basicFarePlan, PromotionPlan promotionPlan,
      ProfileDiscountPlan profileDiscountPlan);

  List<FareProjectMapping> findByPromotionPlanAndFareProject_Type(PromotionPlan promotionPlan,
      FareProjectType projectType);

  List<FareProjectMapping> findByPromotionPlan(PromotionPlan promotionPlan);

  List<FareProjectMapping> findByBasicFarePlan(BasicFarePlan basicFarePlan);

  List<FareProjectMapping> findByFareProject(FareProject fareProject);
}
