/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.StoredValueTicketInput;
import com.ibm.tw.thsrc.bsm.fare.domain.event.StoredValueTicketReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.StoredValueTicket;
import com.ibm.tw.thsrc.bsm.fare.entity.StoredValueTicketOption;
import com.ibm.tw.thsrc.bsm.fare.repository.StoredValueTicketRepository;
import com.ibm.tw.thsrc.bsm.message.fare.StoredValueTicketData;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Transactional
@Service
public class StoredValueTicketCommandService extends
    AbstractCommandService<StoredValueTicket, StoredValueTicketInput> {

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private StoredValueTicketRepository repo;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Override
  public Long create(StoredValueTicketInput input) {
    return null;
  }

  @Override
  public void delete(Long id) {
    // method not allowed
  }

  @Override
  public void update(Long id, StoredValueTicketInput input) {
    StoredValueTicket entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    this.validateConcurrency(entity, input.getDataVersion());
    writeAggregateRootValue(entity, input);
    validateInvariants(entity);

    repo.save(entity);

    StoredValueTicketReplaced event = new StoredValueTicketReplaced(
        interceptor.getUserId(),
        interceptor.getCorrelationId());
    StoredValueTicketData msgPayload = translateMsg(entity);
    event.setMsgPayload(msgPayload);

    this.publishEvent(event);
  }

  @Override
  protected void validateInvariants(StoredValueTicket entity) throws UnprocessableException {
    // not needed for the moment
  }

  @Override
  protected void writeAggregateRootValue(StoredValueTicket entity, StoredValueTicketInput input) {
    entity.setExpiryType(input.getExpiryType());
    entity.setRefundFeeAmt(input.getRefundFeeAmt());
    entity.setDiscountPct(input.getDiscountPct());
    entity.setMinAmt(input.getMinAmt());
    entity.setMaxAmt(input.getMaxAmt());
    entity.setMagnetTktValidDays(input.getMagnetTktValidDays());
    entity.setMaxAmtPerLoad(input.getMaxAmtPerLoad());
    entity.setMaxAmtPerTxn(input.getMaxAmtPerTxn());

    List<StoredValueTicketOption> loadAmt = new ArrayList<>();
    input.getLoadAmount().forEach(amount -> {
      StoredValueTicketOption option = new StoredValueTicketOption();
      option.setStoredValueTicket(entity);
      option.setLoadAmt(BigDecimal.valueOf(amount));
      loadAmt.add(option);
    });

    entity.getLoadAmount().clear();
    entity.getLoadAmount().addAll(loadAmt);
  }

  private StoredValueTicketData translateMsg(StoredValueTicket entity) {
    StoredValueTicketData msgPayload = new StoredValueTicketData();
    msgPayload.setExpiryType(entity.getExpiryType());
    msgPayload.setRefundFeeAmt(entity.getRefundFeeAmt());
    msgPayload.setDiscountPct(entity.getDiscountPct());
    msgPayload.setMinAmt(entity.getMinAmt());
    msgPayload.setMaxAmt(entity.getMaxAmt());
    msgPayload.setMagnetTktValidDays(entity.getMagnetTktValidDays());
    msgPayload.setMaxAmtPerLoad(entity.getMaxAmtPerLoad());
    msgPayload.setMaxAmtPerTxn(entity.getMaxAmtPerTxn());
    List<Integer> loadAmt = new ArrayList<>();
    entity.getLoadAmount().forEach(amount -> loadAmt.add(amount.getLoadAmt().intValue()));
    msgPayload.setLoadAmount(loadAmt);

    return msgPayload;
  }

  private void publishEvent(Object event) {
    applicationEventPublisher.publishEvent(event);
  }
}

