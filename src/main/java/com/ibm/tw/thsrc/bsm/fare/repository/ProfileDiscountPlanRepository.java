/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountPlan;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface ProfileDiscountPlanRepository extends JpaRepository<ProfileDiscountPlan, Long>,
    JpaSpecificationExecutor<ProfileDiscountPlan> {

  public List<ProfileDiscountPlan> findByPassengerProfile_CodeInOrderByPassengerProfile_Code(
      List<String> codes);
}
