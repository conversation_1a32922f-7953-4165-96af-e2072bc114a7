/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.SharedErrorCode;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketProductInput;
import com.ibm.tw.thsrc.bsm.fare.domain.event.TicketProductReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.TicketProduct;
import com.ibm.tw.thsrc.bsm.fare.repository.TicketProductRepository;
import com.ibm.tw.thsrc.bsm.message.fare.TicketProductData;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Transactional
@Service
public class TicketProductCommandService extends
    AbstractCommandService<TicketProduct, TicketProductInput> {

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private TicketProductRepository repo;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Override
  public Long create(TicketProductInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void update(Long id, TicketProductInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void delete(Long id) {
    throw new UnsupportedOperationException();
  }

  public Set<Long> patch(CollectionModificationInput<TicketProductInput> changeSet) {

    List<TicketProductData> msgList = new ArrayList<>();

    changeSet.getReplacements().entrySet().forEach(inputById -> {

      TicketProduct entity =
          repo.findById(inputById.getKey())
              .orElseThrow(() -> SharedErrorCode.COLLECTION_PATCH_INPUT_ID_NOT_EXIST
                  .toException(String.valueOf(inputById)));
      this.validateConcurrency(entity, inputById.getValue().getDataVersion());
      this.writeAggregateRootValue(entity, inputById.getValue());
      this.validateInvariants(entity);
      repo.save(entity);

      if (!entity.getEnPrintName().equals(StringUtils.EMPTY)) {
        msgList.add(translateMsg(entity));
      }
    });

    Set<Long> createdIds = changeSet.getCreations().stream().map(input -> {
      TicketProduct newEntity = new TicketProduct();
      this.writeAggregateRootValue(newEntity, input);
      this.validateInvariants(newEntity);
      TicketProduct entity = repo.save(newEntity);

      if (!entity.getEnPrintName().equals(StringUtils.EMPTY)) {
        msgList.add(translateMsg(entity));
      }
      return entity.getId();
    }).collect(Collectors.toSet());

    TicketProductReplaced event = new TicketProductReplaced(
        interceptor.getUserId(),
        interceptor.getCorrelationId());
    event.setMsgPayload(msgList);

    this.publishEvent(event);

    return createdIds;
  }

  @Override
  protected void validateInvariants(TicketProduct entity) throws UnprocessableException {
    if (entity.getTicketId() <= 17 &&
        (Objects.isNull(entity.getEnPrintName()) || entity.getEnPrintName().isEmpty()
            || Objects.isNull(entity.getEnName()) || entity.getEnName().isEmpty()
            || Objects.isNull(entity.getZhPrintName()) || entity.getZhPrintName().isEmpty())) {
      throw FareErrorCode.FA_TICKET_PRODUCT_FIRST_SEVENTEEN_PRODUCTS_CANNOT_BE_EMPTY.toException(
          String.valueOf(entity.getTicketId()));
    }
  }

  @Override
  protected void writeAggregateRootValue(TicketProduct entity, TicketProductInput input) {
    entity.setTicketId(input.getTicketId());
    entity.setEffDate(input.getEffDate());
    entity.setEnName(input.getEnName());
    entity.setEnPrintName(input.getEnPrintName());
    entity.setZhPrintName(input.getZhPrintName());

    String zhName = input.getZhName();
    if (Objects.nonNull(input.getEnPrintName()) && (!input.getEnPrintName().isEmpty())
        && Objects.isNull(zhName)) {
      zhName = StringUtils.EMPTY;
    }
    entity.setZhName(zhName);
  }

  private TicketProductData translateMsg(TicketProduct entity) {
    TicketProductData msg = new TicketProductData();
    msg.setId(entity.getTicketId());
    msg.setEffDate(entity.getEffDate());
    msg.setEnName(entity.getEnName());
    msg.setZhName(entity.getZhName());
    msg.setEnPrintName(entity.getEnPrintName());
    msg.setZhPrintName(entity.getZhPrintName());

    return msg;
  }

  private void publishEvent(Object event) {
    applicationEventPublisher.publishEvent(event);
  }

}
