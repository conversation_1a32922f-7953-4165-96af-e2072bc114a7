/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.event.ExceptionMessages;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.fa.api.PromotionApi;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionOutput;
import com.ibm.tw.thsrc.bsm.fare.service.PromotionPlanCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.PromotionPlanQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/Promotions")
public class PromotionPlanController implements PromotionApi {

  @Autowired
  private PromotionPlanCommandService commandService;

  @Autowired
  private PromotionPlanQueryService queryService;

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private EventStore eventStore;

  @Autowired
  private ExceptionMessages exceptionMessages;

  @Override
  public Page<PromotionOutput> search(Search search) {
    return queryService.search(search);
  }

  @Override
  public PromotionOutput create(PromotionInput input) {
    eventStore.validateFunctionLock(OperationFunction.PROMOTION);
    Long id = commandService.create(input);
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
    return queryService.read(id);
  }

  @Override
  public void delete(Long id) {
    eventStore.validateFunctionLock(OperationFunction.PROMOTION);
    commandService.delete(id);
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
  }

  @Override
  public PromotionOutput replace(Long id, PromotionInput input) {
    eventStore.validateFunctionLock(OperationFunction.PROMOTION);
    commandService.update(id, input);
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
    return queryService.read(id);
  }

  @Override
  public PromotionOutput read(Long id) {
    return queryService.read(id);
  }
}
