/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrain;
import com.ibm.tw.thsrc.bsm.fare.entity.FarePlan;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject;
import java.time.LocalDate;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface EligibleTrainRepository extends JpaRepository<EligibleTrain, Long>,
    JpaSpecificationExecutor<EligibleTrain> {

  List<EligibleTrain> findByProject(FareProject project);

  List<EligibleTrain> findByFarePlan(FarePlan farePlan);

  List<EligibleTrain> findByFarePlanAndEffSaleDateAndEffDateAndDscDate(FarePlan farePlan,
      LocalDate effSaleDate, LocalDate effDate, LocalDate dscDate);

  List<EligibleTrain> findByProjectAndEffSaleDateAndEffDateAndDscDate(FareProject fareProject,
      LocalDate effSaleDate, LocalDate effDate, LocalDate dscDate);

  List<EligibleTrain> findByFarePlan_CodeAndEffSaleDateBeforeAndIsAllTrainEligibleFalse(
      String farePlan, LocalDate effSaleDate);

  List<EligibleTrain> findByFarePlanAndEffSaleDateIsAndIsAllTrainEligibleFalse(FarePlan farePlan,
      LocalDate effSaleDate);

  List<EligibleTrain> findByFarePlan_CodeAndEffSaleDateIsAndIsAllTrainEligibleFalse(String farePlan,
      LocalDate effSaleDate);

  List<EligibleTrain> findByFarePlanAndEffSaleDateAndEffDateAndDscDateAndIsAllTrainEligibleFalse(
      FarePlan farePlan, LocalDate effSaleDate, LocalDate effDate, LocalDate dscDate);
}
