/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.CardIssuingBankOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.ElectronicMoneyType;
import com.ibm.tw.thsrc.bsm.fare.entity.CardIssuingBank;
import com.ibm.tw.thsrc.bsm.fare.entity.CardIssuingBank_;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCard_;
import com.ibm.tw.thsrc.bsm.fare.repository.CardIssuingBankRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.CardIssuingBankFilter;
import com.ibm.tw.thsrc.bsm.fare.vo.CardIssuingBankSort;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.TypedSort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Transactional
@Service
public class CardIssuingBankQueryService extends
    AbstractQueryService<CardIssuingBank, CardIssuingBankOutput, CardIssuingBankFilter, CardIssuingBankSort> {

  @Autowired
  private ObjectMapper objectMapper;
  @Autowired
  private CardIssuingBankRepository repo;

  public CardIssuingBankOutput read(Long id) {
    CardIssuingBank entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    return translate(entity);
  }

  public Set<CardIssuingBankOutput> read(Set<Long> ids) {
    List<CardIssuingBank> results = repo.findAllById(ids);
    return results.stream().map(this::translate).collect(Collectors.toSet());
  }

  public Page<CardIssuingBankOutput> search(Search searchRequest) {
    PageRequest pr = translateToPageRequest(searchRequest);
    Specification<CardIssuingBank> spec = translateToSpecification(searchRequest);

    Page<CardIssuingBank> results = repo.findAll(spec, pr);
    return results.map(this::translate);
  }


  protected CardIssuingBankOutput translate(CardIssuingBank entity) {
    CardIssuingBankOutput response = new CardIssuingBankOutput();
    response.setId(entity.getId());
    response.setCode(entity.getCode());
    response.setName(entity.getName());
    response.setCardType(entity.getCoBrandedCard().getElectronicMoneyType());

    return response;
  }


  @Override
  protected Sort translate(CardIssuingBankSort sortBy, Direction direction) {
    TypedSort<CardIssuingBank> typedSort = Sort.sort(CardIssuingBank.class);
    if (sortBy.isCode()) {
      typedSort//
          .by(CardIssuingBank::getCode);

    }

    if (typedSort.isSorted()) {
      if (Sort.Direction.DESC == direction) {
        return typedSort.descending();
      } else {
        return typedSort.ascending();
      }
    } else {
      return typedSort;
    }
  }

  @Override
  protected Predicate translate(ComparisonExpression<CardIssuingBankFilter> expression,
      Root<CardIssuingBank> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {

    ComparisonOperator op = expression.getOperator();

    if (expression.getAttributeValue().getType() != null) {

      Path<ElectronicMoneyType> type = root.join(CardIssuingBank_.CO_BRANDED_CARD)
          .get(CoBrandedCard_.ELECTRONIC_MONEY_TYPE);
      ElectronicMoneyType value = expression.getAttributeValue().getType();
      return translate(type, op, value, criteriaBuilder);

    } else {
      return null;
    }
  }

  @Override
  protected Function<Map<String, Boolean>, CardIssuingBankSort> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, CardIssuingBankSort.class);
  }

  @Override
  protected Function<Map<String, Object>, CardIssuingBankFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, CardIssuingBankFilter.class);
  }
}
