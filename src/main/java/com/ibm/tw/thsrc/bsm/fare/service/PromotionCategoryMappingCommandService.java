/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source
 * code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.SharedErrorCode;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryMappingInput;
import com.ibm.tw.thsrc.bsm.fare.domain.event.PromotionCategoryMappingReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProjectMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionCategory;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionCategoryMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan;
import com.ibm.tw.thsrc.bsm.fare.repository.FareProjectMappingRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionCategoryMappingRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionCategoryRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionPlanRepository;
import com.ibm.tw.thsrc.bsm.message.fare.PromotionCategoryMappingData;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class PromotionCategoryMappingCommandService
    extends AbstractCommandService<PromotionCategoryMapping, PromotionCategoryMappingInput> {

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private PromotionCategoryMappingRepository repo;

  @Autowired
  private PromotionPlanRepository promotionPlanRepo;

  @Autowired
  private PromotionCategoryRepository categoryRepo;

  @Autowired
  private FareProjectMappingRepository projectMappingRepo;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Override
  public Long create(PromotionCategoryMappingInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void update(Long id, PromotionCategoryMappingInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void delete(Long id) {
    throw new UnsupportedOperationException();
  }

  public Set<Long> patch(CollectionModificationInput<PromotionCategoryMappingInput> changeSet) {
    List<PromotionCategoryMappingData> msgList = new ArrayList<>();

    changeSet.getReplacements().entrySet().stream()//
        .forEach(inputById -> {

          PromotionCategoryMapping entity = repo.findById(inputById.getKey())
              .orElseThrow(() -> SharedErrorCode.COLLECTION_PATCH_INPUT_ID_NOT_EXIST
                  .toException(String.valueOf(inputById)));
          this.validateConcurrency(entity, inputById.getValue().getDataVersion());
          this.writeAggregateRootValue(entity, inputById.getValue());
          validateInvariants(entity);
          msgList.add(translatePubMsg(entity));
          repo.save(entity);
        });

    changeSet.getDeletions().forEach(inputById -> {
      PromotionCategoryMapping entity = repo.findById(inputById)
          .orElseThrow(() -> SharedErrorCode.COLLECTION_PATCH_INPUT_ID_NOT_EXIST
              .toException(String.valueOf(inputById)));
      msgList.add(translatePubMsg(entity));
      repo.delete(entity);
    });

    Set<Long> createdIds = changeSet.getCreations().stream().map(input -> {
      PromotionCategoryMapping newEntity = new PromotionCategoryMapping();
      this.writeAggregateRootValue(newEntity, input);
      this.validateInvariants(newEntity);
      msgList.add(translatePubMsg(newEntity));
      PromotionCategoryMapping entity = repo.save(newEntity);
      return entity.getId();
    }).collect(Collectors.toSet());

    if (!msgList.isEmpty()) {
      this.publishReplacedEvent(msgList);
    }

    return createdIds;
  }

  @Override
  protected void validateInvariants(PromotionCategoryMapping entity) throws UnprocessableException {
    List<PromotionCategoryMapping> matchEntities =
        repo.findByEffDateAndPromotionPlan(entity.getEffDate(), entity.getPromotionPlan());

    if ((Objects.nonNull(entity.getId()) && matchEntities.size() > 1)
        || (entity.getId() == null && !matchEntities.isEmpty())) {
      throw FareErrorCode.FA_PROMOTION_CATEGORY_MAPPING_ALREADY_EXIST_PROMOTION_SETTING
          .toException(entity.getPromotionPlan().getCode());
    }
  }

  @Override
  protected void writeAggregateRootValue(PromotionCategoryMapping entity,
      PromotionCategoryMappingInput input) {
    entity.setEffDate(input.getEffDate());
    entity.setIsTweDisplay(input.getIsTweDisplay());
    entity.setIsEcpsNeed(input.getIsEcpsNeed());
    PromotionCategory category = categoryRepo.findById(input.getPromotionCategoryId()).orElseThrow(
        () -> FareErrorCode.FA_PROMOTION_CATEGORY_MAPPING_CANNOT_ASSOCIATE_TO_THE_CATEGORY
            .toException(input.getPromotionCategoryId().toString()));
    entity.setCategory(category);
    if (Objects.isNull(entity.getId())) {
      PromotionPlan promotionPlan = promotionPlanRepo.findById(input.getPromotionId()).orElseThrow(
          () -> FareErrorCode.FA_PROMOTION_CATEGORY_MAPPING_CANNOT_ASSOCIATE_TO_THE_PROMOTION_PLAN
              .toException(input.getEffDate().toString(), input.getPromotionId().toString()));
      entity.setPromotionPlan(promotionPlan);
    }
  }

  private PromotionCategoryMappingData translatePubMsg(PromotionCategoryMapping entity) {
    PromotionCategoryMappingData msg = new PromotionCategoryMappingData();
    msg.setEffDate(entity.getEffDate());
    msg.setIsTweDisplay(entity.getIsTweDisplay());
    msg.setIsEcpsNeed(entity.getIsEcpsNeed());
    msg.setPromotionType(entity.getCategory().getName());
    msg.setPromotionCode(entity.getPromotionPlan().getCode());
    if (Objects.nonNull(entity.getPromotionPlan().getDscDate())) {
      msg.setExpireDate(entity.getPromotionPlan().getDscDate().toString());
    }

    if (Objects.nonNull(entity.getPromotionPlan().getDiscountPct())) {
      msg.setDiscountPct(entity.getPromotionPlan().getDiscountPct().intValue());
    }
    if (Objects.nonNull(entity.getPromotionPlan().getDiscountAmt())) {
      msg.setDiscountAmt(entity.getPromotionPlan().getDiscountAmt().intValue());
    }

    List<String> projects = new ArrayList<>();
    List<FareProjectMapping> projectMappings =
        projectMappingRepo.findByPromotionPlan(entity.getPromotionPlan());
    projectMappings.forEach(projectMapping -> {
      FareProject project = projectMapping.getFareProject();
      if (!projects.contains(project.getCode())) {
        projects.add(project.getCode());
      }
    });

    msg.setProjectCodes(projects);

    return msg;
  }

  private void publishReplacedEvent(List<PromotionCategoryMappingData> msgPayload) {
    PromotionCategoryMappingReplaced replaced = new PromotionCategoryMappingReplaced(
        interceptor.getUserId(), interceptor.getCorrelationId());
    replaced.setMsgPayload(msgPayload);
    applicationEventPublisher.publishEvent(replaced);
  }
}
