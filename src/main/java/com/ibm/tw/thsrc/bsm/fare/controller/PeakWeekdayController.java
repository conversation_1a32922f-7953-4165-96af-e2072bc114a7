/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.fa.api.PeakWeekdayApi;
import com.ibm.tw.thsrc.bsm.fa.dto.PeakWeekdayInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PeakWeekdayOutput;
import com.ibm.tw.thsrc.bsm.fare.service.PeakWeekdayCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.PeakWeekdayDetailQueryService;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/PeakWeekdays")
public class PeakWeekdayController implements PeakWeekdayApi {


  @Autowired
  private PeakWeekdayCommandService commandService;

  @Autowired
  private PeakWeekdayDetailQueryService queryService;

  @Autowired
  private EventStore eventStore;

  @Override
  public List<PeakWeekdayOutput> upsert(List<PeakWeekdayInput> changeList) {
    eventStore.validateFunctionLock(OperationFunction.PEAK_WEEKDAYS);
    Set<Long> ids = commandService.upsert(changeList);
    return queryService.read(ids);
  }
}
