/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.event.ExceptionMessages;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.fa.api.BasicFarePriceApi;
import com.ibm.tw.thsrc.bsm.fa.dto.BasicFarePriceInput;
import com.ibm.tw.thsrc.bsm.fa.dto.BasicFarePriceOutput;
import com.ibm.tw.thsrc.bsm.fare.service.BasicFarePriceCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.BasicFarePriceQueryService;
import java.util.LinkedHashSet;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/BasicFarePrices")
public class BasicFarePriceController implements BasicFarePriceApi {

  @Autowired
  private BasicFarePriceCommandService commandService;

  @Autowired
  private BasicFarePriceQueryService queryService;

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private EventStore eventStore;

  @Autowired
  private ExceptionMessages exceptionMessages;

  @Override
  public Page<BasicFarePriceOutput> search(Search searchRequest) {
    return queryService.search(searchRequest);
  }

  @Override
  public BasicFarePriceOutput read(Long id) {
    return queryService.read(id);
  }

  @Override
  public BasicFarePriceOutput create(BasicFarePriceInput dataObject) {
    Long id = commandService.create(dataObject);
    return queryService.read(id);
  }


  @Override
  public BasicFarePriceOutput replace(Long id, BasicFarePriceInput dataObject) {
    commandService.update(id, dataObject);
    return queryService.read(id);
  }

  @Override
  public void delete(Long id) {
    commandService.delete(id);
  }

  @Override
  public Set<BasicFarePriceOutput> modify(
      CollectionModificationInput<BasicFarePriceInput> changeSet) {
    eventStore.validateFunctionLock(OperationFunction.BASIC_FARE_PRICE);
    Set<Long> ids = new LinkedHashSet<>();
    ids.addAll(commandService.patch(changeSet));
    ids.addAll(changeSet.getReplacements().keySet());
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
    return queryService.read(ids);
  }
}
