/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.api.ProfileDiscountPlanApi;
import com.ibm.tw.thsrc.bsm.fa.dto.ProfileDiscountPlanOutput;
import com.ibm.tw.thsrc.bsm.fare.service.ProfileDiscountPlanQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ProfileDiscountPlans")
public class ProfileDiscountPlanController implements ProfileDiscountPlanApi {

  @Autowired
  private ProfileDiscountPlanQueryService queryService;


  @Override
  public Page<ProfileDiscountPlanOutput> search(Search search) {
    return queryService.search(search);
  }
}
