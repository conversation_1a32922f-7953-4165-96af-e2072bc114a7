/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.PeriodicTicketInput;
import com.ibm.tw.thsrc.bsm.fare.domain.event.PeriodicTicketReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.PeriodicTicket;
import com.ibm.tw.thsrc.bsm.fare.entity.PeriodicTicketOption;
import com.ibm.tw.thsrc.bsm.fare.repository.PeriodicTicketRepository;
import com.ibm.tw.thsrc.bsm.message.fare.PeriodicTicketData;
import com.ibm.tw.thsrc.bsm.message.fare.PeriodicTicketOptionData;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 定期票 Command Service
 */
@Transactional
@Service
public class PeriodicTicketCommandService
    extends AbstractCommandService<PeriodicTicket, PeriodicTicketInput> {

  private final Logger log = LoggerFactory.getLogger(this.getClass());

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Autowired
  PeriodicTicketRepository repo;

  @Override
  public Long create(PeriodicTicketInput input) {
    throw new UnsupportedOperationException("create not supported");
  }

  @Override
  public void update(Long id, PeriodicTicketInput input) {
    PeriodicTicket entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    this.validateConcurrency(entity, input.getDataVersion());
    this.writeAggregateRootValue(entity, input);
    this.validateInvariants(entity);
    repo.save(entity);

    // 發布MEIG事件
    PeriodicTicketReplaced event =
        new PeriodicTicketReplaced(interceptor.getUserId(), interceptor.getCorrelationId());
    PeriodicTicketData msgPayload = this.toMsgPayload(entity);
    event.setMsgPayload(msgPayload);
    applicationEventPublisher.publishEvent(event);

    log.info("update - end");
  }

  @Override
  public void delete(Long id) {
    throw new UnsupportedOperationException("delete not supported");
  }

  @Override
  protected void validateInvariants(PeriodicTicket aggregateRoot) throws UnprocessableException {
    aggregateRoot.getPeriodicTicketOptions().forEach(option -> {
      if (Boolean.TRUE.equals(option.getIsAppSale())
          && (option.getAppEffSaleDate() == null || option.getAppDscSaleDate() == null)) {
        throw FareErrorCode.FA_TICKET_OPTION_INVALID_APP_SALE_DATE.toException();
      }
    });
  }

  @Override
  protected void writeAggregateRootValue(PeriodicTicket aggregateRoot, PeriodicTicketInput input) {

    aggregateRoot.setRefundFeeAmt(input.getRefundFeeAmt());
    aggregateRoot.setExpiryType(input.getExpiryType());

    List<PeriodicTicketOption> options = new ArrayList<>();
    input.getOptions().stream().forEach(o -> {

      PeriodicTicketOption option = new PeriodicTicketOption();
      // fix : all required fields should exist
      if (o.getValidDays() != null &&
          o.getDiscountPct() != null &&
          o.getIsAppSale() != null) {
        option.setDiscountPct(o.getDiscountPct());
        option.setIsAppSale(o.getIsAppSale());
        option.setAppEffSaleDate(o.getAppEffSaleDate());
        option.setAppDscSaleDate(o.getAppDscSaleDate());
        option.setValidDays(o.getValidDays());
      }
      option.setPeriodicTicket(aggregateRoot);
      options.add(option);
    });

    if (aggregateRoot.getPeriodicTicketOptions() != null) {
      aggregateRoot.getPeriodicTicketOptions().clear();
      aggregateRoot.getPeriodicTicketOptions().addAll(options);
    } else {
      aggregateRoot.setPeriodicTicketOptions(options);
    }
  }

  private PeriodicTicketData toMsgPayload(PeriodicTicket entity) {
    PeriodicTicketData msgPayload = new PeriodicTicketData();
    msgPayload.setExpiryType(entity.getExpiryType());
    msgPayload.setRefundFeeAmt(entity.getRefundFeeAmt());

    List<PeriodicTicketOptionData> options = new ArrayList<>();
    entity.getPeriodicTicketOptions().stream().forEach(o -> {
      if (o.getValidDays() != null &&
          o.getDiscountPct() != null &&
          o.getIsAppSale() != null) {
        PeriodicTicketOptionData option = new PeriodicTicketOptionData();
        option.setDiscountPct(o.getDiscountPct());
        option.setIsAppSale(o.getIsAppSale());
        option.setAppEffSaleDate(o.getAppEffSaleDate());
        option.setAppDscSaleDate(o.getAppDscSaleDate());
        option.setValidDays(o.getValidDays());
        options.add(option);
      }
    });
    msgPayload.setOptions(options);

    return msgPayload;
  }
}
