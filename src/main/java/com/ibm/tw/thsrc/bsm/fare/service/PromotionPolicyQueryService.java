/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source
 * code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.fa.dto.ECouponEligibleTrainFareOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.ECouponEligibleTrainOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.ECouponFarePlanPolicyOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionPolicyQueryByChannelCodeInput;
import com.ibm.tw.thsrc.bsm.fa.enums.PromotionPolicyChannelCode;
import com.ibm.tw.thsrc.bsm.core.dto.DateRangeDto;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.ECouponPassengerProfileOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.ECouponProjectMappingOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.ECouponProjectOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.ECouponPromotionOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainDetailDto;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionPolicyOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.EligiblePassengerProfileType;
import com.ibm.tw.thsrc.bsm.fa.enums.EligibleTrainType;
import com.ibm.tw.thsrc.bsm.fa.enums.FarePlanType;
import com.ibm.tw.thsrc.bsm.fa.enums.FareRule;
import com.ibm.tw.thsrc.bsm.fa.enums.PromotionServiceType;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrain;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainDetailTrain;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProjectMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.FareRuleSetting;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerProfile;
import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountPlan;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionCategoryMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan;
import com.ibm.tw.thsrc.bsm.fare.entity.TicketEndorsement;
import com.ibm.tw.thsrc.bsm.fare.repository.EligibleTrainRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.FareProjectMappingRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.FareProjectRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PassengerProfileRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionCategoryMappingRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionPlanRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.TicketEndorsementRepository;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Transactional
@Service
public class PromotionPolicyQueryService {

  private static final String OPEN_DATE = "#";

  @Autowired
  private EligibleTrainRepository eligibleTrainRepo;
  @Autowired
  private FareProjectRepository projectRepo;
  @Autowired
  private FareProjectMappingRepository mappingRepo;
  @Autowired
  private PromotionCategoryMappingRepository promotionMappingRepo;
  @Autowired
  private PromotionPlanRepository promotionRepo;
  @Autowired
  private PassengerProfileRepository profileRepo;
  @Autowired
  private TicketEndorsementRepository ticketEndorsementRepo;

  public PromotionPolicyOutput queryByChannelCode(PromotionPolicyQueryByChannelCodeInput input) {
    String channelCode = input.getChannelCode().toUpperCase();

    try {
      if (PromotionPolicyChannelCode.ECPS.equals(PromotionPolicyChannelCode.valueOf(channelCode))) {
        return this.searchEcpsData();
      } else {
        return new PromotionPolicyOutput();
      }
    } catch (IllegalArgumentException e) {
      throw new ResourceNotFoundException();
    }
  }

  private PromotionPolicyOutput searchEcpsData() {
    PromotionPolicyOutput result = new PromotionPolicyOutput();

    // Get all required and available data.
    List<PromotionCategoryMapping> promotionMappings = promotionMappingRepo.findAll().stream()
        .filter(e -> !e.getEffDate().isAfter(LocalDate.now()) && Objects.nonNull(e.getIsEcpsNeed())
            && e.getIsEcpsNeed()).collect(Collectors.toList());
    Set<LocalDate> promotionAllCurrent = promotionMappings.stream()
        .map(PromotionCategoryMapping::getEffDate).collect(Collectors.toSet());
    LocalDate promotionRecentEffDate =
        promotionAllCurrent.isEmpty() ? null : Collections.max(promotionAllCurrent);
    promotionMappings = Objects.isNull(promotionRecentEffDate) ? new ArrayList<>()
        : promotionMappings.stream().filter(e -> e.getEffDate().isEqual(promotionRecentEffDate))
            .collect(Collectors.toList());
    List<String> promotionCodes = promotionMappings.stream().filter(
            e -> Objects.isNull(e.getPromotionPlan().getDiscountAmt())
                || new BigDecimal("0.00").compareTo(e.getPromotionPlan().getDiscountAmt()) < 0)
        .map(e -> e.getPromotionPlan().getCode()).collect(Collectors.toList());

    // Prepare the eligible train data.
    List<EligibleTrain> eligibleTrains = eligibleTrainRepo.findAll();
    this.setEligibleTrains(result, eligibleTrains, promotionCodes);

    // Find the unique list that just contains required reserved promotion codes, only show if is reserved service type, that needs to be contained eligible train setting.
    Set<String> eCouponReservedPromotionCodes = result.getEligibleTrains().stream()
        .map(t -> t.getPromotion().getCode()).collect(Collectors.toSet());

    // Prepare the promotion data.
    List<PromotionPlan> promotions = promotionRepo.findAll().stream().filter(
            e -> (eCouponReservedPromotionCodes.contains(e.getCode())
                || PromotionServiceType.FREE_SEATING.equals(e.getServiceType())))
        .collect(Collectors.toList());
    List<PassengerProfile> profiles = profileRepo.findAll();
    this.setPromotions(result, promotions, profiles);

    // Prepare the fare project mapping data.
    Set<String> eCouponPromotionCodes = result.getPromotions().stream()
        .map(ECouponPromotionOutput::getCode).collect(Collectors.toSet());
    List<FareProjectMapping> mappings = mappingRepo.findAll().stream().filter(
        m -> Objects.nonNull(m.getPromotionPlan()) && eCouponPromotionCodes.contains(
            m.getPromotionPlan().getCode())).collect(Collectors.toList());
    this.setProjectMappings(result, mappings, promotionCodes);

    // Prepare the fare project data.
    Set<String> eCouponProjectCodes = result.getProjectMappings().stream()
        .map(ECouponProjectMappingOutput::getFareProjectCode).collect(Collectors.toSet());
    List<FareProject> projects = projectRepo.findAll().stream()
        .filter(p -> eCouponProjectCodes.contains(p.getCode())).collect(Collectors.toList());
    List<TicketEndorsement> ticketEndorsements = ticketEndorsementRepo.findAll().stream()
        .filter(e -> !e.getEffDate().isAfter(LocalDate.now())).collect(Collectors.toList());
    Set<LocalDate> allCurrent = ticketEndorsements.stream().map(TicketEndorsement::getEffDate)
        .collect(Collectors.toSet());
    LocalDate recentEffDate = allCurrent.isEmpty() ? null : Collections.max(allCurrent);
    ticketEndorsements = Objects.isNull(recentEffDate) ? new ArrayList<>()
        : ticketEndorsements.stream().filter(e -> e.getEffDate().isEqual(recentEffDate))
            .collect(Collectors.toList());
    this.setProjects(result, projects, ticketEndorsements);

    return result;
  }

  private void setPromotions(PromotionPolicyOutput result, List<PromotionPlan> promotions,
      List<PassengerProfile> profiles) {

    List<ECouponPromotionOutput> eCouponPromotions = new ArrayList<>();

    promotions.forEach(promotion -> {

      if (this.isPromotionPlanEffective(promotion)) {
        ECouponPromotionOutput output = new ECouponPromotionOutput();
        output.setId(promotion.getId());
        output.setCode(promotion.getCode());
        output.setName(promotion.getName());

        output.setServiceType(promotion.getServiceType());
        output.setType(promotion.getType());

        ECouponFarePlanPolicyOutput farePlanPolicy = new ECouponFarePlanPolicyOutput();

        farePlanPolicy.setEffDate(promotion.getEffDate());
        farePlanPolicy.setDscDate(promotion.getDscDate());
        farePlanPolicy.setDiscountAmt(promotion.getDiscountAmt());
        farePlanPolicy.setDiscountPct(promotion.getDiscountPct());

        // 適用廂等警提供廂等代碼
        promotion.getValidClasses().forEach(e -> farePlanPolicy.getValidClasses().add(e.getCode()));

        List<PassengerProfile> validPassengerProfiles;

        if (EligiblePassengerProfileType.INVALID.equals(
            promotion.getEligiblePassengerProfileType())) {
          validPassengerProfiles = new ArrayList<>(profiles);
          validPassengerProfiles.removeAll(promotion.getPassengerProfiles());
        } else if (EligiblePassengerProfileType.ALL.equals(
            promotion.getEligiblePassengerProfileType())) {
          validPassengerProfiles = profiles;
        } else {
          validPassengerProfiles = promotion.getPassengerProfiles();
        }

        // 適用身份別僅提供代碼與中文列印名稱，不需身份別疊加折扣
        validPassengerProfiles.forEach(p -> {
          ECouponPassengerProfileOutput profileOutput = new ECouponPassengerProfileOutput();
          profileOutput.setCode(p.getCode());
          profileOutput.setName(p.getZhPrintName());
          farePlanPolicy.getValidPassengerProfiles().add(profileOutput);
        });

        promotion.getInvalidDateRanges().forEach(d -> {
          DateRangeDto dto = DateRangeDto.builder()//
              .beginDate(d.getBeginDate())//
              .endDate(d.getEndDate())//
              .build();
          farePlanPolicy.getInvalidDateRanges().add(dto);
        });

        this.translateFareRuleSetting(output, farePlanPolicy, promotion.getFareRuleSettings());

        output.getFarePlanPolicies().add(farePlanPolicy);
        eCouponPromotions.add(output);
      }
    });

    result.setPromotions(eCouponPromotions);
  }

  private void translateFareRuleSetting(ECouponPromotionOutput response,
      ECouponFarePlanPolicyOutput farePlanPolicy, List<FareRuleSetting> settings) {

    DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    settings.forEach(s -> {
      if (FareRule.R08_CHANGE_RULE.equals(s.getFareRule()) || FareRule.R61_CHANGE_RULE.equals(
          s.getFareRule())) {
        boolean isChangeRuleActive = Boolean.parseBoolean(s.getSetting());
        farePlanPolicy.setModifiable(isChangeRuleActive);
        farePlanPolicy.setRefundable(isChangeRuleActive);
      } else if (FareRule.R09_DSC_SALE_DATE.equals(s.getFareRule())) {
        setR09Response(farePlanPolicy, dtf, s);
      } else if (FareRule.R12_VALID_WEEKDAY.equals(s.getFareRule())) {
        farePlanPolicy.setFrequency(s.getSetting());
      } else if (FareRule.R20_CONTENT.equals(s.getFareRule())) {
        response.setContent(s.getSetting());
      } else if (FareRule.R21_DESCRIPTION.equals(s.getFareRule())) {
        response.setDescription(s.getSetting());
      } else if (FareRule.R34_ELIGIBLE_TRAIN.equals(s.getFareRule())) {
        EligibleTrainType eligibleTrainType = EligibleTrainType.valueOf(s.getSetting());

        if (EligibleTrainType.YES.equals(eligibleTrainType) || EligibleTrainType.SG.equals(
            eligibleTrainType)) {
          farePlanPolicy.setIsAllTrainEligible(true);
        } else {
          farePlanPolicy.setIsAllTrainEligible(false);
        }
      } else if (FareRule.R51_EFF_SALE_DATE.equals(s.getFareRule())) {
        setR51Response(farePlanPolicy, dtf, s);
      }
    });
  }

  private void setR51Response(ECouponFarePlanPolicyOutput farePlanPolicy, DateTimeFormatter dtf,
      FareRuleSetting s) {
    farePlanPolicy.setEffSaleDate(
        s.getSetting().equals(OPEN_DATE) ? null : LocalDate.parse(s.getSetting(), dtf));
  }

  private void setR09Response(ECouponFarePlanPolicyOutput farePlanPolicy, DateTimeFormatter dtf,
      FareRuleSetting s) {
    farePlanPolicy.setDscSaleDate(
        s.getSetting().equals(OPEN_DATE) ? null : LocalDate.parse(s.getSetting(), dtf));
  }

  private void setProjects(PromotionPolicyOutput result, List<FareProject> projects,
      List<TicketEndorsement> ticketEndorsements) {

    List<ECouponProjectOutput> eCouponProjects = new ArrayList<>();

    projects.forEach(project -> {
      ECouponProjectOutput output = new ECouponProjectOutput();
      output.setId(project.getId());
      output.setCode(project.getCode());
      output.setName(project.getName());
      output.setEnName(project.getEnName());
      output.setZhName(project.getZhName());
      output.setType(project.getType());

      // 直接設定票務註記中文名稱
      if (Objects.nonNull(project.getTicketEndorsementType()) && !ticketEndorsements.isEmpty()) {
        TicketEndorsement endorsement = ticketEndorsements.stream()
            .filter(e -> e.getType().equals(project.getTicketEndorsementType())).findAny()
            .orElse(null);
        String endorsementNumber = project.getTicketEndorsementType().toString().split("_")[1];
        String ticketEndorsementString = Objects.isNull(endorsement) ? endorsementNumber
            : endorsementNumber + "_" + endorsement.getZhName();
        output.setTicketEndorsement(ticketEndorsementString);
      }

      eCouponProjects.add(output);
    });

    result.setProjects(eCouponProjects);
  }

  private void setProjectMappings(PromotionPolicyOutput result, List<FareProjectMapping> mappings,
      List<String> promotionCodes) {

    List<ECouponProjectMappingOutput> eCouponProjectMappings = new ArrayList<>();

    mappings.forEach(mapping -> {
      ECouponProjectMappingOutput output = new ECouponProjectMappingOutput();
      output.setId(mapping.getId());
      if (Objects.nonNull(mapping.getPromotionPlan()) && (this.isPromotionPlanEffective(
          mapping.getPromotionPlan())) && (
          promotionCodes.contains(mapping.getPromotionPlan().getCode())
              || PromotionServiceType.FREE_SEATING.equals(
              mapping.getPromotionPlan().getServiceType()))) {
        
        output.setFareProjectId(mapping.getFareProject().getId());
        output.setFareProjectCode(mapping.getFareProject().getCode());
        output.setPromotionPlanId(mapping.getPromotionPlan().getId());
        output.setPromotionPlanCode(mapping.getPromotionPlan().getCode());

        if (Objects.nonNull(mapping.getProfileDiscountPlan())) {
          ProfileDiscountPlan profileDiscountPlan = mapping.getProfileDiscountPlan();
          output.setPassengerProfileName(
              profileDiscountPlan.getPassengerProfile().getZhPrintName());
          output.setPassengerProfileCode(profileDiscountPlan.getPassengerProfile().getCode());
          output.setProfileDiscountTypeCode(profileDiscountPlan.getProfileDiscountType().getCode());
        }

        eCouponProjectMappings.add(output);
      }
    });

    result.setProjectMappings(eCouponProjectMappings);
  }

  private void setEligibleTrains(PromotionPolicyOutput result, List<EligibleTrain> eligibleTrains,
      List<String> promotionCodes) {

    List<ECouponEligibleTrainOutput> eCouponEligibleTrains = new ArrayList<>();

    eligibleTrains.forEach(eligibleTrain -> {

      if (eligibleTrain.getFarePlan().getType().equals(FarePlanType.PROMOTION)
          && !eligibleTrain.getDscDate().isBefore(LocalDate.now()) && this.isPromotionPlanEffective(
          (PromotionPlan) eligibleTrain.getFarePlan()) && promotionCodes.contains(
          eligibleTrain.getFarePlan().getCode())) {

        ECouponEligibleTrainOutput output = new ECouponEligibleTrainOutput();
        output.setId(eligibleTrain.getId());
        output.setEffDate(eligibleTrain.getEffDate());
        output.setDscDate(eligibleTrain.getDscDate());
        output.setEffSaleDate(eligibleTrain.getEffSaleDate());
        output.setDscSaleDate(eligibleTrain.getDscDate());
        output.setIsAllTrainEligible(eligibleTrain.getIsAllTrainEligible());

        ECouponEligibleTrainFareOutput promotion = new ECouponEligibleTrainFareOutput();
        promotion.setId(eligibleTrain.getFarePlan().getId());
        promotion.setCode(eligibleTrain.getFarePlan().getCode());
        promotion.setName(eligibleTrain.getFarePlan().getName());

        output.setPromotion(promotion);

        List<EligibleTrainDetailDto> details = new ArrayList<>();
        eligibleTrain.getDetails().forEach(detail -> {

          if (!(detail.getTrains().isEmpty() && !Boolean.TRUE.equals(
              eligibleTrain.getIsAllTrainEligible()))) {
            EligibleTrainDetailDto detailDto = new EligibleTrainDetailDto();
            detailDto.setDayOfWeek(detail.getDayOfWeek());
            List<String> trains = detail.getTrains().stream()
                .map(EligibleTrainDetailTrain::getTrainNum).collect(Collectors.toList());
            detailDto.setTrains(trains);
            details.add(detailDto);
          }
        });

        if (!details.isEmpty()) {
          output.setDetails(details);
        }
        eCouponEligibleTrains.add(output);
      }

    });

    result.setEligibleTrains(eCouponEligibleTrains);
  }

  private boolean isPromotionPlanEffective(PromotionPlan promotion) {
    return Objects.isNull(promotion.getDscDate()) || !promotion.getDscDate()
        .isBefore(LocalDate.now());
  }
}
