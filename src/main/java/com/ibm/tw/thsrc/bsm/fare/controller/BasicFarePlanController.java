/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.event.ExceptionMessages;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.fa.api.BasicFarePlanApi;
import com.ibm.tw.thsrc.bsm.fa.dto.BasicFarePlanInput;
import com.ibm.tw.thsrc.bsm.fa.dto.BasicFarePlanOutput;
import com.ibm.tw.thsrc.bsm.fare.service.BasicFarePlanCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.BasicFarePlanQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/BasicFarePlans")
public class BasicFarePlanController implements BasicFarePlanApi {

  @Autowired
  private BasicFarePlanQueryService queryService;

  @Autowired
  private BasicFarePlanCommandService commandService;

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private EventStore eventStore;

  @Autowired
  private ExceptionMessages exceptionMessages;


  @Override
  public Page<BasicFarePlanOutput> search(Search searchRequest) {
    return queryService.search(searchRequest);
  }

  @Override
  public BasicFarePlanOutput read(Long id) {
    return queryService.read(id);
  }


  @Override
  public BasicFarePlanOutput create(BasicFarePlanInput dataObject) {
    eventStore.validateFunctionLock(OperationFunction.BASIC_FARE_PLAN);
    Long id = commandService.create(dataObject);

    eventStore.throwIfFailedOutboundOperationWithoutLock(
        interceptor.getCorrelationId()
    );

    return queryService.read(id);
  }

  @Override
  public BasicFarePlanOutput replace(Long id, BasicFarePlanInput dataObject) {
    eventStore.validateFunctionLock(OperationFunction.BASIC_FARE_PLAN);
    commandService.update(id, dataObject);

    eventStore.throwIfFailedOutboundOperationWithoutLock(
        interceptor.getCorrelationId()
    );

    return queryService.read(id);
  }

  @Override
  public void delete(Long id) {
    eventStore.validateFunctionLock(OperationFunction.BASIC_FARE_PLAN);
    commandService.delete(id);
    eventStore.throwIfFailedOutboundOperationWithoutLock(
        interceptor.getCorrelationId());

  }


}
