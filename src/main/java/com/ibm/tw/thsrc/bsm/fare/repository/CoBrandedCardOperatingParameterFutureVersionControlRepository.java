/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.cronjob.enums.FutureVersionControlStatus;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardOperatingParameterFutureVersionControl;
import java.time.LocalDate;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface CoBrandedCardOperatingParameterFutureVersionControlRepository extends
    JpaRepository<CoBrandedCardOperatingParameterFutureVersionControl, Long>,
    JpaSpecificationExecutor<CoBrandedCardOperatingParameterFutureVersionControl> {

  List<CoBrandedCardOperatingParameterFutureVersionControl> findByEffDateAndStatusIn(
      LocalDate effDate, List<FutureVersionControlStatus> statuses);

  List<CoBrandedCardOperatingParameterFutureVersionControl> findByEffDateAndStatus(
      LocalDate effDate, FutureVersionControlStatus status);

  List<CoBrandedCardOperatingParameterFutureVersionControl> findByEffDateAndStatusInAndDeleted(
      LocalDate effDate, List<FutureVersionControlStatus> statuses, boolean deleted);

}
