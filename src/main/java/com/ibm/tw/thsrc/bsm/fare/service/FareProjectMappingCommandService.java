/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.exception.SharedErrorCode;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.FareProjectMappingInput;
import com.ibm.tw.thsrc.bsm.fa.enums.PromotionServiceType;
import com.ibm.tw.thsrc.bsm.fare.domain.event.FareProjectMappingCreated;
import com.ibm.tw.thsrc.bsm.fare.domain.event.FareProjectMappingDeleted;
import com.ibm.tw.thsrc.bsm.fare.domain.event.MeigFareProjectMappingCreated;
import com.ibm.tw.thsrc.bsm.fare.domain.event.MeigFareProjectMappingDeleted;
import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePlan;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProjectMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountPlan;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionCategoryMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan;
import com.ibm.tw.thsrc.bsm.fare.repository.BasicFarePlanRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.FareProjectMappingRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.FareProjectRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.ProfileDiscountPlanRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionCategoryMappingRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionPlanRepository;
import com.ibm.tw.thsrc.bsm.message.fare.FareProjectMappingData;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.transaction.Transactional;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class FareProjectMappingCommandService extends
    AbstractCommandService<FareProjectMapping, FareProjectMappingInput> {

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private FareProjectMappingRepository repo;

  @Autowired
  private FareProjectRepository projectRepo;

  @Autowired
  private BasicFarePlanRepository basicFareRepo;

  @Autowired
  private PromotionPlanRepository promotionRepo;

  @Autowired
  private ProfileDiscountPlanRepository profileRepo;

  @Autowired
  private PromotionCategoryMappingRepository promotionMappingRepo;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;
  
  @Autowired
  private EventStore eventStore;

  @Override
  public Long create(FareProjectMappingInput input) {
    FareProjectMapping newEntity = new FareProjectMapping();
    writeAggregateRootValue(newEntity, input);
    validateInvariants(newEntity);
    FareProjectMapping entity = repo.save(newEntity);
    
    if (entity.getPromotionPlan() == null || PromotionServiceType.RESERVED.equals(entity.getPromotionPlan().getServiceType()) ) {
      publishCreatedEvent(translatePubMsg(newEntity));
    } else {
      publishMeigCreateEvent(translatePubMsg(entity));
    }
   
    return entity.getId();
  }

  @Override
  public void update(Long id, FareProjectMappingInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void delete(Long id) {
    FareProjectMapping entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);

    if (Objects.nonNull(entity.getPromotionPlan())) {
      List<PromotionCategoryMapping> promotionCategoryMappings = promotionMappingRepo.findByPromotionPlan(
          entity.getPromotionPlan());
      Pair<LocalDate, LocalDate> promotionRecentFutureEffDate = this.getPromotionRecentFutureEffDate();

      if (!promotionCategoryMappings.isEmpty() && (
          (Objects.nonNull(promotionRecentFutureEffDate.getRight())
              && (promotionCategoryMappings.stream()
              .anyMatch(e -> e.getEffDate().isEqual(promotionRecentFutureEffDate.getRight())))) || (
              Objects.nonNull(promotionRecentFutureEffDate.getLeft())
                  && (promotionCategoryMappings.stream().anyMatch(
                  e -> e.getEffDate().isEqual(promotionRecentFutureEffDate.getLeft())))))) {
        throw FareErrorCode.FA_FARE_PROJECT_MAPPING_CANNOT_BE_DELETE_WHEN_PROMOTION_PLAN_USED_BY_PROMOTION_CATEGORY_MAPPING.toException();
      }
    }
    
    repo.delete(entity);

    if (entity.getPromotionPlan() == null || PromotionServiceType.RESERVED.equals(entity.getPromotionPlan().getServiceType()) ) {
      publishDeletedEvent(translatePubMsg(entity));
    } else {
      publishMeigDeleteEvent(translatePubMsg(entity));
    }
  
  }

  @Override
  protected void validateInvariants(FareProjectMapping entity) throws UnprocessableException {
    // 其他條件重複 farePlan+promotion+profileDiscount
    List<FareProjectMapping> mappings = repo.findByBasicFarePlanAndPromotionPlanAndProfileDiscountPlan(
        entity.getBasicFarePlan(), entity.getPromotionPlan(), entity.getProfileDiscountPlan());

    // 同種類型專案只能有一筆一樣的
    if (Objects.nonNull(mappings) && !mappings.isEmpty()) {
      mappings.forEach(e -> {
        if (e.getFareProject().getType().equals(entity.getFareProject().getType())
            && Objects.equals(e.getProfileDiscountPlan(), entity.getProfileDiscountPlan())) {
          throw SharedErrorCode.FARE_PROJECT_MAPPING_BASIC_FARE_PLAN_OR_PROMOTION_PLAN_CANNOT_MAP_TO_MORE_THAN_ONE_STANDARD_OR_BUSINESS_PROJECT_WITHOUT_PROFILE_DISCOUNT_PLAN.toException();
        }
      });
    }
  }

  @Override
  protected void writeAggregateRootValue(FareProjectMapping entity, FareProjectMappingInput input) {

    if (Objects.isNull(input.getBasicFarePlanId()) && Objects.isNull(input.getPromotionPlanId())
        && Objects.isNull(input.getProfileDiscountPlanId())) {
      throw FareErrorCode.FA_FARE_PROJECT_MAPPING_BASIC_FARE_PLAN_AND_PROMOTION_AND_PROFILE_CANNOT_ALL_BE_NULL.toException();
    }

    FareProject fareProject = projectRepo.findById(input.getFareProjectId()).orElseThrow(
        () -> FareErrorCode.FA_FARE_PROJECT_MAPPING_CANNOT_ASSOCIATE_TO_THE_FARE_PROJECT.toException(
            input.getFareProjectId().toString()));
    entity.setFareProject(fareProject);

    if (Objects.nonNull(input.getBasicFarePlanId())) {
      BasicFarePlan basicFarePlan = basicFareRepo.findById(input.getBasicFarePlanId()).orElseThrow(
          () -> FareErrorCode.FA_FARE_PROJECT_MAPPING_CANNOT_ASSOCIATE_TO_THE_BASIC_FARE_PLAN.toException(
              input.getBasicFarePlanId().toString()));
      entity.setBasicFarePlan(basicFarePlan);
    }

    if (Objects.nonNull(input.getPromotionPlanId())) {
      PromotionPlan promotionPlan = promotionRepo.findById(input.getPromotionPlanId()).orElseThrow(
          () -> FareErrorCode.FA_FARE_PROJECT_MAPPING_CANNOT_ASSOCIATE_TO_THE_PROMOTION_PLAN.toException(
              input.getPromotionPlanId().toString()));
      entity.setPromotionPlan(promotionPlan);
    }

    if (Objects.nonNull(input.getProfileDiscountPlanId())) {
      ProfileDiscountPlan profile = profileRepo.findById(input.getProfileDiscountPlanId())
          .orElseThrow(
              () -> FareErrorCode.FA_FARE_PROJECT_MAPPING_CANNOT_ASSOCIATE_TO_THE_PROFILE_DISCOUNT_PLAN.toException(
                  input.getProfileDiscountPlanId().toString()));
      entity.setProfileDiscountPlan(profile);
    }
  }

  private Pair<LocalDate, LocalDate> getPromotionRecentFutureEffDate() {
    List<PromotionCategoryMapping> products = promotionMappingRepo.findAll();
    LocalDate futureDate = null;
    LocalDate recentDate = null;
    Optional<PromotionCategoryMapping> recent = products.stream()
        .filter(e -> !e.getEffDate().isAfter(LocalDate.now()))
        .max(Comparator.comparing(PromotionCategoryMapping::getEffDate));

    if (recent.isPresent()) {
      recentDate = recent.get().getEffDate();
    }
    Optional<PromotionCategoryMapping> future = products.stream()
        .filter(e -> e.getEffDate().isAfter(LocalDate.now()))
        .max(Comparator.comparing(PromotionCategoryMapping::getEffDate));

    if (future.isPresent()) {
      futureDate = future.get().getEffDate();
    }

    return Pair.of(recentDate, futureDate);
  }

  private FareProjectMappingData translatePubMsg(FareProjectMapping entity) {
    FareProjectMappingData msg = new FareProjectMappingData();
    msg.setProjectCode(entity.getFareProject().getCode());
    if (Objects.nonNull(entity.getBasicFarePlan())) {
      msg.setBasicFarePlanCode(entity.getBasicFarePlan().getCode());
    }
    if (Objects.nonNull(entity.getPromotionPlan())) {
      msg.setPromotionPlanCode(entity.getPromotionPlan().getCode());
      msg.setPromotionServiceType(entity.getPromotionPlan().getServiceType());
    }
    if (Objects.nonNull(entity.getProfileDiscountPlan())) {
      msg.setProfileCode(entity.getProfileDiscountPlan().getPassengerProfile().getCode()
          + entity.getProfileDiscountPlan().getProfileDiscountType().getCode());
    }

    return msg;
  }

  private void publishCreatedEvent(FareProjectMappingData msgPayload) {
    FareProjectMappingCreated event = new FareProjectMappingCreated(interceptor.getUserId(),
        interceptor.getCorrelationId());
    event.setMsgPayload(msgPayload);
    event.setOperationId(OperationFunction.FARE_PROJECT_MAPPING.name());
    event.setAssociationId(interceptor.getCorrelationId());
    applicationEventPublisher.publishEvent(event);
  }

  private void publishDeletedEvent(FareProjectMappingData msgPayload) {
    FareProjectMappingDeleted event = new FareProjectMappingDeleted(interceptor.getUserId(),
        interceptor.getCorrelationId());
    event.setMsgPayload(msgPayload);
    event.setOperationId(OperationFunction.FARE_PROJECT_MAPPING.name());
    event.setAssociationId(interceptor.getCorrelationId());
    applicationEventPublisher.publishEvent(event);
  }
  
  
  private void publishMeigCreateEvent(FareProjectMappingData msgPayload) {
    MeigFareProjectMappingCreated event = new MeigFareProjectMappingCreated(interceptor.getUserId(),
    interceptor.getCorrelationId());
    event.setMsgPayload(msgPayload);
    event.setOperationId(OperationFunction.FARE_PROJECT_MAPPING.name());
    event.setAssociationId(interceptor.getCorrelationId());
    eventStore.publishEvent(event);
  }
  
  private void publishMeigDeleteEvent(FareProjectMappingData msgPayload) {
    MeigFareProjectMappingDeleted event = new MeigFareProjectMappingDeleted(interceptor.getUserId(),
        interceptor.getCorrelationId());
    event.setMsgPayload(msgPayload);
    event.setOperationId(OperationFunction.FARE_PROJECT_MAPPING.name());
    event.setAssociationId(interceptor.getCorrelationId());
    eventStore.publishEvent(event);
  }
}
