/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.fa.api.MultiRideTicketApi;
import com.ibm.tw.thsrc.bsm.fa.dto.MultiRideTicketInput;
import com.ibm.tw.thsrc.bsm.fa.dto.MultiRideTicketOutput;
import com.ibm.tw.thsrc.bsm.fare.service.MultiRideTicketCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.MultiRideTicketQueryService;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 回數票 Controller
 */
@RestController
@RequestMapping(value = "/MultiRideTickets")
public class MultiRideTicketApiController implements MultiRideTicketApi {

  @Autowired
  MultiRideTicketCommandService commandService;

  @Autowired
  MultiRideTicketQueryService queryService;

  @Autowired
  private EventStore eventStore;

  @Override
  public Page<MultiRideTicketOutput> search(@Valid Search search) {
    return queryService.search(search);
  }

  @Override
  public MultiRideTicketOutput read(Long id) {
    return queryService.read(id);
  }

  @Override
  public MultiRideTicketOutput replace(Long id, @Valid MultiRideTicketInput input) {
    eventStore.validateFunctionLock(OperationFunction.MULTIRIDE_TICKET);
    commandService.update(id, input);
    return queryService.read(id);
  }

}
