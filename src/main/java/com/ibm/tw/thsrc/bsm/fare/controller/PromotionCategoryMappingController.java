/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.api.PromotionCategoryMappingApi;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryMappingInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryMappingOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryValidationInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryValidationOutput;
import com.ibm.tw.thsrc.bsm.fare.service.PromotionCategoryMappingCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.PromotionCategoryMappingQueryService;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/PromotionCategoryMapping")
public class PromotionCategoryMappingController implements PromotionCategoryMappingApi {

  @Autowired
  private PromotionCategoryMappingCommandService commandService;

  @Autowired
  private PromotionCategoryMappingQueryService queryService;

  @Override
  public Set<PromotionCategoryMappingOutput> modify(
      CollectionModificationInput<PromotionCategoryMappingInput> changeSet) {
    Set<Long> ids = commandService.patch(changeSet);
    ids.addAll(changeSet.getReplacements().keySet());
    return queryService.read(ids);
  }

  @Override
  public Page<PromotionCategoryMappingOutput> search(Search search) {
    return queryService.search(search);
  }

  @Override
  public PromotionCategoryMappingOutput read(Long id) {
    return queryService.read(id);
  }

  @Override
  public PromotionCategoryValidationOutput validate(
      PromotionCategoryValidationInput validationInput) {

    return queryService.validate(validationInput);
  }
}
