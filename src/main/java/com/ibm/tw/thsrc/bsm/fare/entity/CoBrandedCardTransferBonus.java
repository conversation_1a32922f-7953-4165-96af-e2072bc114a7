/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.entity;

import com.ibm.tw.thsrc.bsm.core.entity.AggregateRoot;
import com.ibm.tw.thsrc.bsm.fa.enums.CoBrandedCardTransportMode;
import java.math.BigDecimal;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity
public class CoBrandedCardTransferBonus extends AggregateRoot {

  @ManyToOne
  @JoinColumn
  private CoBrandedCard coBrandedCard;

  @ManyToOne
  @JoinColumn
  private CoBrandedCardProfile profile;

  @Enumerated(value = EnumType.STRING)
  private CoBrandedCardTransportMode transportMode;

  private BigDecimal discountAmt;
}
