/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.api.CoBrandedCardProfileApi;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardProfileOutput;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardProfileQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/CoBrandedCardProfiles")
public class CoBrandedCardProfileController implements CoBrandedCardProfileApi {

  @Autowired
  private CoBrandedCardProfileQueryService queryService;

  @Override
  public Page<CoBrandedCardProfileOutput> search(Search search) {
    return queryService.search(search);
  }
}
