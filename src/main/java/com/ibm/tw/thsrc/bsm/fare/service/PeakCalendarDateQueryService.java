/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.fa.dto.PeakCalendarDateOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.PeakCalendarDate;
import com.ibm.tw.thsrc.bsm.fare.entity.PeakCalendarDate_;
import com.ibm.tw.thsrc.bsm.fare.repository.PeakCalendarDateRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.PeakCalendarDateFilter;
import com.ibm.tw.thsrc.bsm.fare.vo.PeakCalendarDateSort;
import java.time.LocalDate;
import java.time.Month;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class PeakCalendarDateQueryService extends
    AbstractQueryService<PeakCalendarDate, PeakCalendarDateOutput, PeakCalendarDateFilter, PeakCalendarDateSort> {

  @Autowired
  private PeakCalendarDateRepository repo;

  @Autowired
  private ObjectMapper objectMapper;

  @Override
  public PeakCalendarDateOutput read(Long id) {
    throw new UnsupportedOperationException();
  }

  public List<PeakCalendarDateOutput> read(Set<Long> ids) {
    return repo.findAllById(ids).stream().map(this::translate).collect(Collectors.toList());
  }

  @Override
  public Page<PeakCalendarDateOutput> search(Search search) {
    PageRequest pr = translateToPageRequest(search);
    Specification<PeakCalendarDate> spec = translateToSpecification(search);
    Page<PeakCalendarDate> page = repo.findAll(spec, pr);

    return page.map(this::translate);
  }

  @Override
  protected PeakCalendarDateOutput translate(PeakCalendarDate entity) {
    PeakCalendarDateOutput response = new PeakCalendarDateOutput();
    response.setId(entity.getId());
    response.setDataVersion(entity.getDataVersion());
    response.setEffDate(entity.getEffDate());
    response.setCalendarDate(entity.getCalendarDate());
    response.setPeakWeekday(Objects.nonNull(entity.getPeakWeekday()));
    response.setPeakTimeId(
        Objects.nonNull(entity.getPeakTime()) ? entity.getPeakTime().getId() : null);

    return response;
  }

  @Override
  protected Sort translate(PeakCalendarDateSort sortBy, Direction sortDirection) {
    return Sort.unsorted();
  }

  @Override
  protected Predicate translate(ComparisonExpression<PeakCalendarDateFilter> expression,
      Root<PeakCalendarDate> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    if (expression.getAttributeValue().getIsFuture() != null) {
      Path<LocalDate> effDate = root.get(PeakCalendarDate_.EFF_DATE);
      boolean isFuture = expression.getAttributeValue().getIsFuture();
      ComparisonOperator op;
      LocalDate value = LocalDate.now();

      if (isFuture) {
        op = ComparisonOperator.GT;

      } else {
        op = ComparisonOperator.EQ;
        Optional<PeakCalendarDate> newest = repo.findAll().stream()
            .filter(e -> !e.getEffDate().isAfter(LocalDate.now()))
            .max(Comparator.comparing(PeakCalendarDate::getEffDate));
        if (newest.isPresent()) {
          value = newest.get().getEffDate();
        }
      }

      Path<LocalDate> calenderDate = root.get(PeakCalendarDate_.CALENDAR_DATE);
      Pair<LocalDate, LocalDate> startEndDate = findStartEndCalenderDate();
      return criteriaBuilder.and(translate(effDate, op, value, criteriaBuilder),
          translate(calenderDate, ComparisonOperator.GE, startEndDate.getLeft(), criteriaBuilder),
          translate(calenderDate, ComparisonOperator.LE, startEndDate.getRight(), criteriaBuilder));

    } else {
      return null;
    }
  }

  @Override
  protected Function<Map<String, Boolean>, PeakCalendarDateSort> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, PeakCalendarDateSort.class);
  }

  @Override
  protected Function<Map<String, Object>, PeakCalendarDateFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, PeakCalendarDateFilter.class);
  }

  private Pair<LocalDate, LocalDate> findStartEndCalenderDate() {
    LocalDate now = LocalDate.now();
    int currentYear = now.getYear();
    Month currentMonth = now.getMonth();
    LocalDate startDate = LocalDate.of(currentYear, currentMonth, 1);

    Month endMonth = startDate.minusMonths(1).getMonth();

    int lastDay = 0;
    if (endMonth.equals(Month.APRIL) || endMonth.equals(Month.JUNE) || endMonth.equals(
        Month.SEPTEMBER) || endMonth.equals(Month.NOVEMBER)) {
      lastDay = 30;
    } else if (endMonth.equals(Month.FEBRUARY)) {
      if ((currentYear + 1) % 4 == 0) {
        lastDay = 29;
      } else {
        lastDay = 28;
      }
    } else {
      lastDay = 31;
    }

    LocalDate endDate = LocalDate.of(currentYear + 1, endMonth, lastDay);
    return Pair.of(startDate, endDate);
  }
}
