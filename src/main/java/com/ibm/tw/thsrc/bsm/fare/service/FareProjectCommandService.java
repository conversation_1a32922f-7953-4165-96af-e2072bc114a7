/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source
 * code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.ColorInput;
import com.ibm.tw.thsrc.bsm.fa.dto.FareProjectInput;
import com.ibm.tw.thsrc.bsm.fa.dto.ProjectSalesChannelFlagSettingInput;
import com.ibm.tw.thsrc.bsm.fa.enums.ProjectFlag;
import com.ibm.tw.thsrc.bsm.fa.enums.ProjectSalesChannelFlag;
import com.ibm.tw.thsrc.bsm.fare.domain.event.FareProjectCreated;
import com.ibm.tw.thsrc.bsm.fare.domain.event.FareProjectDeleted;
import com.ibm.tw.thsrc.bsm.fare.domain.event.FareProjectReplaced;
import com.ibm.tw.thsrc.bsm.fare.domain.event.ProjectSalesChannelFlagSettingDeleted;
import com.ibm.tw.thsrc.bsm.fare.domain.event.ProjectSalesChannelFlagSettingReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject;
import com.ibm.tw.thsrc.bsm.fare.entity.ProjectFlagSetting;
import com.ibm.tw.thsrc.bsm.fare.entity.ProjectSalesChannelFlagSetting;
import com.ibm.tw.thsrc.bsm.fare.repository.FareProjectMappingRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.FareProjectRepository;
import com.ibm.tw.thsrc.bsm.message.fare.FareProjectData;
import com.ibm.tw.thsrc.bsm.message.fare.ProjectSalesChannelFlagSettingData;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class FareProjectCommandService
    extends AbstractCommandService<FareProject, FareProjectInput> {

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private FareProjectRepository repo;

  @Autowired
  private FareProjectMappingRepository mappingRepo;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Override
  public Long create(FareProjectInput input) {

    if (repo.findByCode(input.getCode()).isPresent()) {
      throw FareErrorCode.FA_FARE_PROJECT_CODE_CANNOT_REPEAT.toException(input.getCode());
    }
    FareProject newEntity = new FareProject();
    writeAggregateRootValue(newEntity, input);
    validateInvariants(newEntity);
    FareProject entity = repo.save(newEntity);
    publishCreatedEvent(translateFareProjectMsg(newEntity));

    return entity.getId();
  }

  @Override
  public void update(Long id, FareProjectInput input) {

    FareProject entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    validateConcurrency(entity, input.getDataVersion());
    if (!entity.getCode().equals(input.getCode())) {
      throw FareErrorCode.FA_FARE_PROJECT_CODE_UPDATE_NOT_ALLOWED.toException();
    }
    writeAggregateRootValue(entity, input);
    validateInvariants(entity);
    repo.save(entity);
    publishReplacedEvent(translateFareProjectMsg(entity));
  }

  @Override
  public void delete(Long id) {
    FareProject entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    if (!mappingRepo.findByFareProject(entity).isEmpty()) {
      throw FareErrorCode.FA_FARE_PROJECT_CANNOT_BE_DELETED_WHEN_ASSOCIATED_BY_FARE_PROJECT_MAPPING
          .toException();
    }

    repo.delete(entity);
    publishDeletedEvent(translateFareProjectMsg(entity));
  }

  public void upsertSalesChannelFlagSettings(Long id, ProjectSalesChannelFlagSettingInput input) {

    FareProject entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    validateConcurrency(entity, input.getDataVersion());
    writeAggregateRootValue(entity, input);
    validateInvariants(entity);
    repo.save(entity);
    publishReplacedEvent(translateProjectSalesFlagMsg(entity));
  }

  public void deleteSalesChannelFlagSettings(Long id) {
    FareProject entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    entity.getSalesChannelFlagSettings().clear();
    repo.save(entity);
    publishDeletedEvent(translateProjectSalesFlagMsg(entity));
  }

  @Override
  protected void validateInvariants(FareProject entity) throws UnprocessableException {

    if (Objects.nonNull(entity.getAiValue())) {
      List<FareProject> foundEntities = repo.findByAiValue(entity.getAiValue());

      if ((Objects.isNull(entity.getId()) && !foundEntities.isEmpty())
          || (Objects.nonNull(entity.getId()) && foundEntities.size() > 1)) {
        throw FareErrorCode.FA_FARE_PROJECT_AI_VALUE_CANNOT_REPEAT.toException(entity.getAiValue());
      }
    }
  }

  @Override
  protected void writeAggregateRootValue(FareProject entity, FareProjectInput input) {
    entity.setType(input.getType());
    entity.setCode(input.getCode());
    entity.setName(input.getName());
    entity.setEnName(input.getEnName());
    entity.setZhName(input.getZhName());
    entity.setAiValue(input.getAiValue());
    entity.setTicketEndorsementType(input.getTicketEndorsementType());

    if (input.getBackgroundColor() != null) {
      String backgroundColorText = convertColorInputToText(input.getBackgroundColor());
      entity.setBackgroundColor(backgroundColorText);
    } else {
      entity.setBackgroundColor(null);
    }

    if (input.getTextColor() != null) {
      String textColorText = convertColorInputToText(input.getTextColor());
      entity.setTextColor(textColorText);
    } else {
      entity.setTextColor(null);
    }

    entity.setRefundRule(input.getRefundRule());
    entity.setChangeRule(input.getChangeRule());

    entity.getFlagSettings().clear();
    entity.getFlagSettings().addAll(this.translate(input, entity));
  }

  protected String convertColorInputToText(ColorInput input) {
    StringBuilder colorText = new StringBuilder();
    colorText.append(input.getRed().toString()).append(",");
    colorText.append(input.getGreen().toString()).append(",");
    colorText.append(input.getBlue().toString());
    return colorText.toString();
  }

  protected void writeAggregateRootValue(FareProject entity,
      ProjectSalesChannelFlagSettingInput input) {
    entity.getSalesChannelFlagSettings().clear();
    entity.getSalesChannelFlagSettings().addAll(this.translate(input, entity));
  }

  private List<ProjectFlagSetting> translate(FareProjectInput input, FareProject entity) {
    List<ProjectFlagSetting> settings = new ArrayList<>();

    ProjectFlagSetting idSetting = new ProjectFlagSetting(ProjectFlag.ID_DIGIT_DISPLAY_P,
        String.valueOf(input.getIdDigitDisplay()), entity);
    settings.add(idSetting);

    ProjectFlagSetting smisSetting = new ProjectFlagSetting(ProjectFlag.SMIS_CHECK_S,
        String.valueOf(input.getSmisCheck()), entity);
    settings.add(smisSetting);

    ProjectFlagSetting tbSetting = new ProjectFlagSetting(ProjectFlag.TICKET_BOOTH_LIGHT_T,
        String.valueOf(input.getTbLight()), entity);
    settings.add(tbSetting);

    ProjectFlagSetting noReissueSetting = new ProjectFlagSetting(ProjectFlag.NO_REISSUE_N,
        String.valueOf(input.getNoReissue()), entity);
    settings.add(noReissueSetting);

    return settings;
  }

  private List<ProjectSalesChannelFlagSetting> translate(ProjectSalesChannelFlagSettingInput input,
      FareProject entity) {
    List<ProjectSalesChannelFlagSetting> settings = new ArrayList<>();

    ProjectSalesChannelFlagSetting couponSetting = new ProjectSalesChannelFlagSetting(
        ProjectSalesChannelFlag.TWE_COUPON_CODE_REQ, input.getCouponRequired(), entity);
    settings.add(couponSetting);

    ProjectSalesChannelFlagSetting memPointSetting = new ProjectSalesChannelFlagSetting(
        ProjectSalesChannelFlag.TWE_MEMBER_POINT_GIVEN, input.getMemberPointGiven(), entity);
    settings.add(memPointSetting);

    ProjectSalesChannelFlagSetting cprMemSetting = new ProjectSalesChannelFlagSetting(
        ProjectSalesChannelFlag.TWE_CPR_MEMBER_PTS_GIVEN, input.getCprMemberPointGiven(), entity);
    settings.add(cprMemSetting);

    ProjectSalesChannelFlagSetting odSetting = new ProjectSalesChannelFlagSetting(
        ProjectSalesChannelFlag.TWE_OD_UNMODIFIABLE, input.getOdUnmodified(), entity);
    settings.add(odSetting);

    ProjectSalesChannelFlagSetting seatSetting = new ProjectSalesChannelFlagSetting(
        ProjectSalesChannelFlag.TWE_SEAT_MODIFIABLE, input.getSeatModifiable(), entity);
    settings.add(seatSetting);

    ProjectSalesChannelFlagSetting idSetting = new ProjectSalesChannelFlagSetting(
        ProjectSalesChannelFlag.TWE_ADULT_CHILD_ID_REQ, input.getAdultChildIdRequired(), entity);
    settings.add(idSetting);

    ProjectSalesChannelFlagSetting manualSetting = new ProjectSalesChannelFlagSetting(
        ProjectSalesChannelFlag.TWE_MANUAL_SEATING_ONLY, input.getManualSeatOnly(), entity);
    settings.add(manualSetting);

    ProjectSalesChannelFlagSetting groupSetting = new ProjectSalesChannelFlagSetting(
        ProjectSalesChannelFlag.TWE_GROUP_CANCELABLE, input.getGroupCancelable(), entity);
    settings.add(groupSetting);

    ProjectSalesChannelFlagSetting printSetting = new ProjectSalesChannelFlagSetting(
        ProjectSalesChannelFlag.TWE_SIGN_PRINT_REMINDER, input.getSignPrintReminder(), entity);
    settings.add(printSetting);

    ProjectSalesChannelFlagSetting currentDaySetting = new ProjectSalesChannelFlagSetting(
        ProjectSalesChannelFlag.TWE_CURRENT_DAY_TRAIN_ONLY, input.getCurrentDayTrainOnly(), entity);
    settings.add(currentDaySetting);

    ProjectSalesChannelFlagSetting promotionModifiable = new ProjectSalesChannelFlagSetting(
        ProjectSalesChannelFlag.TWE_PROMOTION_MODIFIABLE, input.getPromotionModifiable(), entity);
    settings.add(promotionModifiable);

    ProjectSalesChannelFlagSetting carTypeModifiable = new ProjectSalesChannelFlagSetting(
        ProjectSalesChannelFlag.TWE_CAR_TYPE_MODIFIABLE, input.getCarTypeModifiable(), entity);
    settings.add(carTypeModifiable);

    ProjectSalesChannelFlagSetting roundTripAvailable = new ProjectSalesChannelFlagSetting(
        ProjectSalesChannelFlag.TWE_ROUND_TRIP_AVAILABLE, input.getRoundTripAvailable(), entity);
    settings.add(roundTripAvailable);

    return settings;
  }

  private FareProjectData translateFareProjectMsg(FareProject entity) {
    FareProjectData msg = new FareProjectData();
    msg.setCode(entity.getCode());
    msg.setName(entity.getName());
    msg.setEnName(entity.getEnName());
    msg.setZhName(entity.getZhName());
    msg.setAiValue(entity.getAiValue());
    msg.setTicketEndorsement(
        Objects.nonNull(entity.getTicketEndorsementType()) ? entity.getTicketEndorsementType().code
            : null);
    if (Objects.nonNull(entity.getRefundRule())) {
      String[] flagNames = entity.getRefundRule().projectFlag.toString().split("_");
      String refundCode = flagNames[flagNames.length - 1];
      msg.setRefundRule(refundCode);
    }
    if (Objects.nonNull(entity.getChangeRule())) {
      String[] flagNames = entity.getChangeRule().projectFlag.toString().split("_");
      String changeCode = flagNames[flagNames.length - 1];
      msg.setChangeRule(changeCode);
    }
    if (Objects.nonNull(entity.getTextColor())) {
      msg.setTextColor(Arrays.asList(entity.getTextColor().split(",")));
    }
    if (Objects.nonNull(entity.getBackgroundColor())) {
      msg.setTextColor(Arrays.asList(entity.getBackgroundColor().split(",")));
    }

    this.translateFlagSetting(msg, entity);

    return msg;
  }

  private ProjectSalesChannelFlagSettingData translateProjectSalesFlagMsg(FareProject entity) {
    ProjectSalesChannelFlagSettingData msg = new ProjectSalesChannelFlagSettingData();
    msg.setCode(entity.getCode());
    msg.setName(entity.getName());

    this.translateSalesFlagSetting(msg, entity);

    return msg;
  }

  private void translateFlagSetting(FareProjectData msg, FareProject entity) {
    List<ProjectFlagSetting> settings = entity.getFlagSettings();

    settings.forEach(s -> {
      String[] flagNames = s.getFlag().toString().split("_");
      String flagCode = flagNames[flagNames.length - 1];
      boolean setting = Boolean.parseBoolean(s.getSetting());

      if (ProjectFlag.ID_DIGIT_DISPLAY_P.equals(s.getFlag()) && setting) {
        msg.setIdDigitDisplay(flagCode);
      } else if (ProjectFlag.SMIS_CHECK_S.equals(s.getFlag()) && setting) {
        msg.setSmisCheck(flagCode);
      } else if (ProjectFlag.TICKET_BOOTH_LIGHT_T.equals(s.getFlag()) && setting) {
        msg.setTbLight(flagCode);
      } else if (ProjectFlag.NO_REISSUE_N.equals(s.getFlag()) && setting) {
        msg.setNoReissue(flagCode);
      }
    });
  }

  private void translateSalesFlagSetting(ProjectSalesChannelFlagSettingData msg,
      FareProject entity) {
    List<ProjectSalesChannelFlagSetting> settings = entity.getSalesChannelFlagSettings();

    settings.forEach(s -> {
      if (ProjectSalesChannelFlag.TWE_COUPON_CODE_REQ.equals(s.getFlag())) {
        msg.setCouponRequired(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_MEMBER_POINT_GIVEN.equals(s.getFlag())) {
        msg.setMemberPointGiven(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_CPR_MEMBER_PTS_GIVEN.equals(s.getFlag())) {
        msg.setCprMemberPointGiven(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_OD_UNMODIFIABLE.equals(s.getFlag())) {
        msg.setOdUnmodified(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_SEAT_MODIFIABLE.equals(s.getFlag())) {
        msg.setSeatModifiable(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_ADULT_CHILD_ID_REQ.equals(s.getFlag())) {
        msg.setAdultChildIdRequired(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_MANUAL_SEATING_ONLY.equals(s.getFlag())) {
        msg.setManualSeatOnly(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_GROUP_CANCELABLE.equals(s.getFlag())) {
        msg.setGroupCancelable(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_SIGN_PRINT_REMINDER.equals(s.getFlag())) {
        msg.setSignPrintReminder(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_CURRENT_DAY_TRAIN_ONLY.equals(s.getFlag())) {
        msg.setCurrentDayTrainOnly(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_PROMOTION_MODIFIABLE.equals(s.getFlag())) {
        msg.setPromotionModifiable(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_CAR_TYPE_MODIFIABLE.equals(s.getFlag())) {
        msg.setCarTypeModifiable(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_ROUND_TRIP_AVAILABLE.equals(s.getFlag())) {
        msg.setRoundTripAvailable(s.isSetting());
      }
    });
  }

  private void publishCreatedEvent(FareProjectData msgPayload) {
    FareProjectCreated event =
        new FareProjectCreated(interceptor.getUserId(), interceptor.getCorrelationId());
    event.setMsgPayload(msgPayload);
    event.setOperationId(OperationFunction.FARE_PROJECT.name());
    event.setAssociationId(interceptor.getCorrelationId());
    applicationEventPublisher.publishEvent(event);
  }

  private void publishReplacedEvent(FareProjectData msgPayload) {
    FareProjectReplaced event =
        new FareProjectReplaced(interceptor.getUserId(), interceptor.getCorrelationId());
    event.setMsgPayload(msgPayload);
    event.setOperationId(OperationFunction.FARE_PROJECT.name());
    event.setAssociationId(interceptor.getCorrelationId());
    applicationEventPublisher.publishEvent(event);
  }

  private void publishDeletedEvent(FareProjectData msgPayload) {
    FareProjectDeleted event =
        new FareProjectDeleted(interceptor.getUserId(), interceptor.getCorrelationId());
    event.setMsgPayload(msgPayload);
    event.setOperationId(OperationFunction.FARE_PROJECT.name());
    event.setAssociationId(interceptor.getCorrelationId());
    applicationEventPublisher.publishEvent(event);
  }

  private void publishReplacedEvent(ProjectSalesChannelFlagSettingData msgPayload) {
    ProjectSalesChannelFlagSettingReplaced replaced = new ProjectSalesChannelFlagSettingReplaced(
        interceptor.getUserId(), interceptor.getCorrelationId());
    replaced.setMsgPayload(msgPayload);
    applicationEventPublisher.publishEvent(replaced);
  }

  private void publishDeletedEvent(ProjectSalesChannelFlagSettingData msgPayload) {
    ProjectSalesChannelFlagSettingDeleted deleted = new ProjectSalesChannelFlagSettingDeleted(
        interceptor.getUserId(), interceptor.getCorrelationId());
    deleted.setMsgPayload(msgPayload);
    applicationEventPublisher.publishEvent(deleted);
  }
}
