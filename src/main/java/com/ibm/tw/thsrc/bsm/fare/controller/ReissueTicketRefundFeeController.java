/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.api.ReissueTicketRefundFeeApi;
import com.ibm.tw.thsrc.bsm.fa.dto.ReissueTicketRefundFeeInput;
import com.ibm.tw.thsrc.bsm.fa.dto.ReissueTicketRefundFeeOutput;
import com.ibm.tw.thsrc.bsm.fare.service.ReissueTicketRefundFeeCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.ReissueTicketRefundFeeQueryService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ReissueTicketRefundFees")
public class ReissueTicketRefundFeeController implements ReissueTicketRefundFeeApi {

  @Autowired
  private ReissueTicketRefundFeeCommandService commandService;
  @Autowired
  private ReissueTicketRefundFeeQueryService queryService;

  @Override
  public Page<ReissueTicketRefundFeeOutput> search(Search search) {
    return queryService.search();
  }

  @Override
  public List<ReissueTicketRefundFeeOutput> upsert(ReissueTicketRefundFeeInput changeInput) {
    commandService.upsert(changeInput);
    return queryService.search().getContent();
  }
}
