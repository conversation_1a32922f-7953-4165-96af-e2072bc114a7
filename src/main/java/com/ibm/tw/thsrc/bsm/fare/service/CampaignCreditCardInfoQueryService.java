/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardBatchOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardInfoOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.CreditCardWeekdayPromotionDto;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionDto;
import com.ibm.tw.thsrc.bsm.fa.enums.CreditCardCampaignType;
import com.ibm.tw.thsrc.bsm.fare.entity.CampaignCreditCardInfo;
import com.ibm.tw.thsrc.bsm.fare.entity.CampaignCreditCardInfo_;
import com.ibm.tw.thsrc.bsm.fare.entity.CreditCardWeekdayPromotion;
import com.ibm.tw.thsrc.bsm.fare.repository.CampaignCreditCardBatchRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CampaignCreditCardInfoRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.CampaignCreditCardBatchFilter;
import com.ibm.tw.thsrc.bsm.util.BeanUtils;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class CampaignCreditCardInfoQueryService  extends
AbstractQueryService<CampaignCreditCardInfo, CampaignCreditCardInfoOutput, CampaignCreditCardBatchFilter, Void> {

  @Autowired
  protected CampaignCreditCardBatchRepository batchRepo;

  @Autowired
  protected CampaignCreditCardInfoRepository infoRepo;

  @Autowired
  private ObjectMapper objectMapper;

  @Override
  public CampaignCreditCardInfoOutput read(Long id) {
    return infoRepo.findById(id)
        .map(this::translate)
        .orElseThrow(ResourceNotFoundException::new);
  }

  @Override
  public Page<CampaignCreditCardInfoOutput> search(Search search) {
    PageRequest pr = translateToPageRequest(search);
    Specification<CampaignCreditCardInfo> spec = translateToSpecification(search);
    return infoRepo
        .findAll(spec, pr)
        .map(this::translate);
  }

  @Override
  protected CampaignCreditCardInfoOutput translate(CampaignCreditCardInfo aggregateRoot) {

    CampaignCreditCardInfoOutput output = new CampaignCreditCardInfoOutput();

    output.setId(aggregateRoot.getId());
    output.setDataVersion(aggregateRoot.getDataVersion());

    output.setBankName(aggregateRoot.getBatch().getBankName());
    output.setImportDate(aggregateRoot.getBatch().getImportDate());

    output.setCardNo(aggregateRoot.getCardNo());
    output.setDscDate(aggregateRoot.getDscDate());
    output.setEffDate(aggregateRoot.getEffDate());
    output.setDscSaleDate(aggregateRoot.getDscSaleDate());
    output.setEffSaleDate(aggregateRoot.getEffSaleDate());

    output.setCreditCardCampaignType(aggregateRoot.getCreditCardCampaignType());

    CampaignCreditCardBatchOutput batch = new CampaignCreditCardBatchOutput();
    BeanUtils.copyProperties(batch, aggregateRoot.getBatch());

    List<CreditCardWeekdayPromotionDto> promotionDtos = new ArrayList<>();

    for (CreditCardWeekdayPromotion promotion : aggregateRoot.getPromotions()) {
      CreditCardWeekdayPromotionDto creditCardWeekdayPromotionDto = new CreditCardWeekdayPromotionDto();
      // 若未查到PromotionPlan不予處理
      if (promotion.getPromotionPlan() == null) {
        continue;
      }
      creditCardWeekdayPromotionDto.setId(promotion.getId());
      creditCardWeekdayPromotionDto.setDayOfWeek(promotion.getDayOfWeek());
      creditCardWeekdayPromotionDto.setPromotionId(promotion.getPromotionPlan().getId());

      PromotionDto promotionDto = new PromotionDto();
      promotionDto.setId(promotion.getPromotionPlan().getId());
      promotionDto.setCode(promotion.getPromotionPlan().getCode());
      promotionDto.setName(promotion.getPromotionPlan().getName());

      creditCardWeekdayPromotionDto.setPromotion(promotionDto);

      promotionDtos.add(creditCardWeekdayPromotionDto);
    }
    output.setCreditCardWeekdayPromotion(promotionDtos);

    return output;
  }

  @Override
  protected Sort translate(Void sortBy, Direction sortDirection) {
    return Sort.sort(CampaignCreditCardInfo.class)
        .by(CampaignCreditCardInfo::getId)
        .ascending();
  }

  @Override
  protected Predicate translate(ComparisonExpression<CampaignCreditCardBatchFilter> expression,
      Root<CampaignCreditCardInfo> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {

    ComparisonOperator op = expression.getOperator();

    if (expression.getAttributeValue().getCardNo() != null) {

      Path<String> cardNoPath = root.get(CampaignCreditCardInfo_.CARD_NO);
      
      return this.withFixedCriteria(root, criteriaBuilder
          , translate(cardNoPath, op, expression.getAttributeValue().getCardNo(), criteriaBuilder));

    } else if (expression.getAttributeValue().getCreditCardCampaignType() != null) {

      Path<CreditCardCampaignType> path = root.get(CampaignCreditCardInfo_.CREDIT_CARD_CAMPAIGN_TYPE);
      
      return this.withFixedCriteria(root, criteriaBuilder, translate(path, op
          ,  CreditCardCampaignType.valueOf(expression.getAttributeValue().getCreditCardCampaignType()), criteriaBuilder));

    } else {
      return this.withFixedCriteria(root, criteriaBuilder, null);
    }
  }

  @Override
  protected Function<Map<String, Boolean>, Void> getSortByTranslator() {
    return data -> null;
  }

  @Override
  protected Function<Map<String, Object>, CampaignCreditCardBatchFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, CampaignCreditCardBatchFilter.class);
  }
  
  /**
   * 加入固定查訊條件
   * @param root
   * @param criteriaBuilder
   * @param predicate
   * @return
   */
  Predicate withFixedCriteria(Root<CampaignCreditCardInfo> root, CriteriaBuilder criteriaBuilder, Predicate predicate) {
    // 排除已過期卡號
    Path<LocalDate> dscDatePath = root.get(CampaignCreditCardInfo_.DSC_DATE);
    Path<LocalDate> dscSaleDatePath = root.get(CampaignCreditCardInfo_.DSC_SALE_DATE);
    
    Predicate dscDatePredicate = criteriaBuilder.greaterThanOrEqualTo(dscDatePath, LocalDate.now());
    Predicate dscSaleDatePredicate = criteriaBuilder.greaterThanOrEqualTo(dscSaleDatePath, LocalDate.now());
    // dscDate null代表無限期
    Predicate dscDatePathNull = criteriaBuilder.isNull(dscDatePath);
    Predicate dscSaleDatePathNull = criteriaBuilder.isNull(dscSaleDatePath);
    
    Predicate fixedPredicate = criteriaBuilder.or(dscDatePredicate, dscSaleDatePredicate, dscDatePathNull, dscSaleDatePathNull);
    
    if (predicate == null) {
      return fixedPredicate;
    }
    return criteriaBuilder.and(predicate, fixedPredicate);
  }
}
