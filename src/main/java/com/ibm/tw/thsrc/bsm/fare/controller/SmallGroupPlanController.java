/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.fa.api.SmallGroupPlanApi;
import com.ibm.tw.thsrc.bsm.fa.dto.SmallGroupPlanInput;
import com.ibm.tw.thsrc.bsm.fa.dto.SmallGroupPlanOutput;
import com.ibm.tw.thsrc.bsm.fare.service.SmallGroupPlanCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.SmallGroupPlanQueryService;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小團體 RestController
 */
@RestController
@RequestMapping(value = "/SmallGroupPlans")
public class SmallGroupPlanController implements SmallGroupPlanApi {

  @Autowired
  private SmallGroupPlanCommandService commandService;

  @Autowired
  private SmallGroupPlanQueryService queryService;

  @Autowired
  private EventStore eventStore;

  /**
   * 查詢列表
   */
  @Override
  public Page<SmallGroupPlanOutput> search(@Valid Search search) {
    return queryService.search(search);
  }

  /**
   * 新增單筆
   */
  @Override
  public SmallGroupPlanOutput create(@Valid SmallGroupPlanInput input) {
    eventStore.validateFunctionLock(OperationFunction.SMALL_GROUP_PLAN);
    Long id = commandService.create(input);
    return queryService.read(id);
  }

  /**
   * 查詢單筆
   */
  @Override
  public SmallGroupPlanOutput read(Long id) {
    return queryService.read(id);
  }

  /**
   * 修改單筆
   */
  @Override
  public SmallGroupPlanOutput replace(Long id, @Valid SmallGroupPlanInput input) {
    eventStore.validateFunctionLock(OperationFunction.SMALL_GROUP_PLAN);
    commandService.update(id, input);
    return queryService.read(id);
  }

  /**
   * 刪除單筆
   */
  @Override
  public void delete(Long id) {
    eventStore.validateFunctionLock(OperationFunction.SMALL_GROUP_PLAN);
    commandService.delete(id);
  }

}
