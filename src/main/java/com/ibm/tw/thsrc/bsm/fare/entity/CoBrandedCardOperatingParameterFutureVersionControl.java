/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.entity;

import com.ibm.tw.thsrc.bsm.cronjob.entity.FutureVersionControl;
import java.time.LocalDate;
import javax.persistence.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
public class CoBrandedCardOperatingParameterFutureVersionControl extends FutureVersionControl {

  private String correlationId;
  private String createUser;
  private LocalDate effDate;
}
