/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.cronjob.enums.FutureVersionControlStatus;
import com.ibm.tw.thsrc.bsm.cronjob.service.impl.FutureVersionControlService;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.exception.SharedErrorCode;
import com.ibm.tw.thsrc.bsm.fa.dto.BasicFarePriceDetailInput;
import com.ibm.tw.thsrc.bsm.fa.dto.BasicFarePriceInput;
import com.ibm.tw.thsrc.bsm.fa.enums.ServiceType;
import com.ibm.tw.thsrc.bsm.fare.domain.event.BasicFarePriceCreated;
import com.ibm.tw.thsrc.bsm.fare.domain.event.BasicFarePriceDeleted;
import com.ibm.tw.thsrc.bsm.fare.domain.event.BasicFarePriceReplaced;
import com.ibm.tw.thsrc.bsm.fare.domain.event.BatchBasicFarePriceCreated;
import com.ibm.tw.thsrc.bsm.fare.domain.event.BatchBasicFarePriceReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePlan;
import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePrice;
import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePriceDetail;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardOperatingParameterFutureVersionControl;
import com.ibm.tw.thsrc.bsm.fare.entity.RoundingRule;
import com.ibm.tw.thsrc.bsm.fare.entity.StationPairProjection;
import com.ibm.tw.thsrc.bsm.fare.repository.BasicFarePlanRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.BasicFarePriceRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardOperatingParameterFutureVersionControlRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.RoundingRuleRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.StationPairProjectionRepository;
import com.ibm.tw.thsrc.bsm.message.fare.BasicFarePriceData;
import com.ibm.tw.thsrc.bsm.message.fare.BasicFarePriceDataDetail;
import com.ibm.tw.thsrc.bsm.res.connection.annotation.RESTransaction;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.ibm.tw.thsrc.bsm.res.connection.annotation.RESTransaction;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional
@Service
public class BasicFarePriceCommandService
    extends AbstractCommandService<BasicFarePrice, BasicFarePriceInput> {

  public static final String JOB_BSM_CO_BRANDED_CARD_OPERATING_PARAMETER = "coBrandedCardOperatingParameterJob";
  private static final Logger log = LoggerFactory.getLogger(BasicFarePriceCommandService.class);
  private static final String COLUMN_EFF_DATE = "effDate";
  private static final String FREE_SEATING_FARE_PLAN = "MEIG3";

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private BasicFarePriceRepository repo;

  @Autowired
  private BasicFarePlanRepository basicFarePlanRepo;

  @Autowired
  private StationPairProjectionRepository stationPairRepo;

  @Autowired
  private CoBrandedCardOperatingParameterFutureVersionControlRepository futureVersionRepo;

  @Autowired
  private FutureVersionControlService<CoBrandedCardOperatingParameterFutureVersionControl> futureVersionControlService;

  @Autowired
  private RoundingRuleRepository roundingRuleRepo;

  @Autowired
  private EventStore eventStore;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @RESTransaction
  @Override
  public synchronized void delete(Long id) {
    BasicFarePrice entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    this.publishDeletedEventMsg(translateCommandMsg(entity));
    repo.delete(entity);
  }

  @RESTransaction
  @Override
  public synchronized Long create(BasicFarePriceInput input) {
    BasicFarePrice newEntity = new BasicFarePrice();
    this.writeAggregateRootValue(newEntity, input);
    BasicFarePrice addedEntity = repo.save(newEntity);

    this.validateInvariants(addedEntity);
    this.parallelUpdate(input);

    this.publishCreatedEventMsg(translateCommandMsg(addedEntity));
    return addedEntity.getId();
  }

  @RESTransaction
  @Override
  public synchronized void update(Long id, BasicFarePriceInput input) {
    BasicFarePrice entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);

    this.validateConcurrency(entity, input.getDataVersion());

    BasicFarePrice copied = deepCopyBasicFarePrice(entity);
    this.writeAggregateRootValue(entity, input);
    this.validateInvariants(entity);
    this.parallelUpdate(input);
    this.publishReplacedEventMsg(Pair.of(translateCommandMsg(copied), translateCommandMsg(entity)));
  }

  @RESTransaction
  public synchronized Set<Long> patch(CollectionModificationInput<BasicFarePriceInput> changeSet) {
    // 卡片營運參數未來版票價生效用
    LocalDate[] preEffDate = new LocalDate[1];
    LocalDate[] nextEffDate = new LocalDate[1];

    List<BasicFarePriceData> createdMsgList = new ArrayList<>();
    List<BasicFarePriceData> beforePrices = new ArrayList<>();
    List<BasicFarePriceData> afterPrices = new ArrayList<>();

    changeSet.getReplacements().entrySet().stream()//
        .forEach(inputById -> {

          BasicFarePrice entity = repo.findById(inputById.getKey())
              .orElseThrow(() -> SharedErrorCode.COLLECTION_PATCH_INPUT_ID_NOT_EXIST
                  .toException(String.valueOf(inputById)));
          this.validateConcurrency(entity, inputById.getValue().getDataVersion());
          BasicFarePrice copied = deepCopyBasicFarePrice(entity);
          this.writeAggregateRootValue(entity, inputById.getValue());
          repo.save(entity);
          validateInvariants(entity);
          this.parallelUpdate(inputById.getValue());

          BasicFarePriceData beforePrice = this.translateCommandMsg(copied);
          BasicFarePriceData afterPrice = this.translateCommandMsg(entity);
          beforePrices.add(beforePrice);
          afterPrices.add(afterPrice);

          // 自由座票價設定
          if (entity.getBasicFarePlan().getCode().equals(FREE_SEATING_FARE_PLAN)) {
            preEffDate[0] = copied.getEffDate();
            nextEffDate[0] = entity.getEffDate();
          }
        });

    Set<Long> createdIds = changeSet.getCreations().stream().map(input -> {
      BasicFarePrice newEntity = new BasicFarePrice();
      this.writeAggregateRootValue(newEntity, input);
      BasicFarePrice entity = repo.save(newEntity);
      this.validateInvariants(newEntity);
      this.parallelUpdate(input);
      createdMsgList.add(translateCommandMsg(newEntity));

      // 卡片票價未來版
      if (entity.getBasicFarePlan().getCode().equals(FREE_SEATING_FARE_PLAN)) {
        nextEffDate[0] = entity.getEffDate();
      }
      return entity.getId();
    }).collect(Collectors.toSet());

    // 驗證是否有卡片排程正在執行
    if (Objects.nonNull(preEffDate[0])) {
      validateFutureVersionControlStepRunning(preEffDate[0]);
    }

    this.publishBatchCreatedEventMsg(createdMsgList);
    this.publishBatchReplacedEventMsg(beforePrices, afterPrices);

    // 有送自由座票價要放排程
    if (Objects.nonNull(nextEffDate[0])) {
      // 新增未來版時->新增排程
      if (Objects.isNull(preEffDate[0])) {
        createFutureVersionControl(nextEffDate[0]);

        // 修改未來版時->delete-insert排程
      } else {
        deleteFutureVersionControl(preEffDate[0]);
        createFutureVersionControl(nextEffDate[0]);
      }
    }

    return createdIds;
  }

  private void publishDeletedEventMsg(BasicFarePriceData payload) {
    BasicFarePriceDeleted event =
        new BasicFarePriceDeleted(interceptor.getUserId(), interceptor.getCorrelationId());
    event.setPriceData(payload);
    applicationEventPublisher.publishEvent(event);
  }

  private void publishCreatedEventMsg(BasicFarePriceData payload) {
    BasicFarePriceCreated event =
        new BasicFarePriceCreated(interceptor.getUserId(), interceptor.getCorrelationId());
    event.setPriceData(payload);
    applicationEventPublisher.publishEvent(event);
  }

  private void publishReplacedEventMsg(Pair<BasicFarePriceData, BasicFarePriceData> payload) {
    BasicFarePriceReplaced event =
        new BasicFarePriceReplaced(interceptor.getUserId(), interceptor.getCorrelationId());
    event.setPriceData(payload);
    applicationEventPublisher.publishEvent(event);
  }

  private void publishBatchCreatedEventMsg(List<BasicFarePriceData> payloads) {
    if (payloads.isEmpty()) {
      return;
    }
    BatchBasicFarePriceCreated event = new BatchBasicFarePriceCreated(interceptor.getUserId(),
        interceptor.getCorrelationId(), payloads);
    event.setOperationId(OperationFunction.BASIC_FARE_PRICE.name());
    event.setAssociationId(interceptor.getCorrelationId());
    applicationEventPublisher.publishEvent(event);

  }

  private void publishBatchReplacedEventMsg(
      List<BasicFarePriceData> beforePrices, List<BasicFarePriceData> afterPrices) {
    if (beforePrices.isEmpty() || afterPrices.isEmpty()) {
      return;
    }
    BatchBasicFarePriceReplaced event = new BatchBasicFarePriceReplaced(interceptor.getUserId(),
        interceptor.getCorrelationId(), beforePrices, afterPrices);
    event.setOperationId(OperationFunction.BASIC_FARE_PRICE.name());
    event.setAssociationId(interceptor.getCorrelationId());
    applicationEventPublisher.publishEvent(event);
  }

  private BasicFarePrice deepCopyBasicFarePrice(BasicFarePrice entity) {
    BasicFarePrice copied = new BasicFarePrice();
    copied.setBasicFarePlan(entity.getBasicFarePlan());
    copied.setEffDate(entity.getEffDate());
    copied.setDscDate(entity.getDscDate());
    List<BasicFarePriceDetail> details = new ArrayList<>();
    for (BasicFarePriceDetail inputDetail : entity.getDetails()) {
      BasicFarePriceDetail detail = new BasicFarePriceDetail();
      detail.setStationPairProjection(inputDetail.getStationPairProjection());
      detail.setUnitPrice(inputDetail.getUnitPrice());
      detail.setBasicFarePrice(inputDetail.getBasicFarePrice());
      details.add(detail);
    }
    copied.setDetails(details);
    return copied;
  }

  private BasicFarePriceData translateCommandMsg(BasicFarePrice entity) {

    BasicFarePriceData msg = new BasicFarePriceData();
    msg.setResFarePlanCode(entity.getBasicFarePlan().getCode());

    Boolean isMeig = entity.getBasicFarePlan().getServiceType().equals(ServiceType.FREE_SEATING);
    msg.setIsServiceTypeFreeSeating(isMeig);
    if (Boolean.TRUE.equals(isMeig)) {
      msg.setResFarePlanCode(entity.getBasicFarePlan().getDisplayCode());
    }
    msg.setEffDate(entity.getEffDate());
    msg.setDscDate(entity.getDscDate());

    List<String> cityPairs = new ArrayList<>();
    List<BasicFarePriceDataDetail> msgDetails = new ArrayList<>();
    for (BasicFarePriceDetail detail : entity.getDetails()) {
      String departure = detail.getStationPairProjection().getDepartureCode();
      String arrival = detail.getStationPairProjection().getArrivalCode();
      String cityPair = departure + arrival;
      cityPairs.add(cityPair);
      if (cityPairs.indexOf(arrival + departure) >= 0) { // ignore reverse city pairs
        continue;
      }
      BasicFarePriceDataDetail msgDetail = new BasicFarePriceDataDetail();
      msgDetail.setArrival(arrival);
      msgDetail.setDeparture(departure);
      msgDetail.setUnitPrice(detail.getUnitPrice());
      msgDetails.add(msgDetail);
    }
    msg.setDetails(msgDetails);
    return msg;
  }

  @Override
  protected void writeAggregateRootValue(BasicFarePrice entity, BasicFarePriceInput input) {
    BasicFarePlan farePlanEntity = basicFarePlanRepo.findById(input.getFarePlanId()).orElseThrow(
        FareErrorCode.FA_BASIC_FARE_PRICE_CANNOT_ASSOCIATE_TO_THE_BASIC_FARE_PLAN::toException);

    entity.setBasicFarePlan(farePlanEntity);
    entity.setEffDate(input.getEffDate());
    entity.setDscDate(input.getDscDate());

    List<BasicFarePriceDetail> details = new ArrayList<>();
    for (BasicFarePriceDetailInput detailInput : input.getBasicFareMatrix()) {
      BasicFarePriceDetail detailEntity = new BasicFarePriceDetail();
      detailEntity.setUnitPrice(detailInput.getUnitPrice());
      StationPairProjection pairEntity =
          stationPairRepo.findById(detailInput.getStationPairId()).orElseThrow(
              FareErrorCode.FA_BASIC_FARE_PRICE_DETAIL_CANNOT_ASSOCIATE_TO_THE_STATION_PAIR::toException);
      detailEntity.setStationPairProjection(pairEntity);
      detailEntity.setBasicFarePrice(entity);
      details.add(detailEntity);
    }

    if (entity.getDetails() != null) {
      entity.getDetails().clear();
      entity.getDetails().addAll(details);
    } else {
      entity.setDetails(details);
    }
  }

  @Override
  protected void validateInvariants(BasicFarePrice newPrice) {

    validatePriceAmtIfRoundingRuleIsFive(newPrice);

    BasicFarePlan basicFarePlan = newPrice.getBasicFarePlan();
    if (validatedFreeSeating(newPrice, basicFarePlan)) {
      return;
    }

    if (newPrice.getDetails().stream()
        .anyMatch(detail -> detail.getUnitPrice().equals(BigDecimal.ZERO)
            || detail.getUnitPrice().compareTo(BigDecimal.valueOf(9999)) > 0)) {
      throw FareErrorCode.FA_RES_BASIC_FARE_PRICE_AMT_INVALID.toException();
    }

    LocalDate planDscDate = newPrice.getBasicFarePlan().getDscDate() == null ? LocalDate.MAX
        : newPrice.getBasicFarePlan().getDscDate();
    LocalDate priceDscDate = newPrice.getDscDate() == null ? LocalDate.MAX : newPrice.getDscDate();

    if (planDscDate.isBefore(priceDscDate)) {
      throw FareErrorCode.FA_BASIC_FARE_PRICE_DSC_DATE_CANNOT_BE_AFTER_BASIC_FARE_PLAN_DSC_DATE
          .toException();
    }

    validateOverlap(newPrice, basicFarePlan);
  }


  private void validatePriceAmtIfRoundingRuleIsFive(BasicFarePrice newPrice) {

    LocalDate priceEffDate = newPrice.getEffDate() == null ? LocalDate.MIN : newPrice.getEffDate();

    Optional<RoundingRule> applicableRule = roundingRuleRepo.findAll().stream().filter(
        rule -> !rule.getEffDate().isAfter(priceEffDate)
    ).sorted(Comparator.comparing(RoundingRule::getEffDate).reversed()).findFirst();

    RoundingRule currentRule = roundingRuleRepo.findAll().stream().filter(
            rule -> !rule.getEffDate().isAfter(LocalDate.now())
        ).sorted(Comparator.comparing(RoundingRule::getEffDate).reversed()).findFirst()
        .orElseThrow(FareErrorCode.FA_ROUNDING_RULE_NOT_FOUND::toException);

    RoundingRule targetRule = applicableRule.isPresent() ? applicableRule.get() : currentRule;

    Integer integer5 = Integer.valueOf(5);
    BigDecimal bigDecimal5 = BigDecimal.valueOf(5);
    BigDecimal bigDecimal0 = BigDecimal.valueOf(0);

    if (integer5.equals(targetRule.getMultiple()) && //  5退位時，須確認金額數字皆為5的倍數
        !newPrice.getDetails().stream()
            .allMatch(
                detail -> detail.getUnitPrice().remainder(bigDecimal5).equals(bigDecimal0))) {
      throw FareErrorCode.FA_BASIC_FARE_PRICE_AMT_SHOULD_BE_MULTIPLES_OF_FIVE.toException();
    }

  }

  private void validateOverlap(BasicFarePrice newPrice, BasicFarePlan basicFarePlan) {

    List<BasicFarePrice> entities = repo.findByBasicFarePlan(basicFarePlan,
        Sort.sort(BasicFarePrice.class).by(BasicFarePrice::getEffDate).descending());

    LocalDate newEffDate = newPrice.getEffDate() == null ? LocalDate.MIN : newPrice.getEffDate();
    LocalDate newDscDate = newPrice.getDscDate() == null ? LocalDate.MAX : newPrice.getDscDate();

    boolean overlap = entities.stream()//
        .filter(e -> !e.getId().equals(newPrice.getId()))//
        .anyMatch(entity -> {
          LocalDate effDate = entity.getEffDate() == null ? LocalDate.MIN : entity.getEffDate();
          LocalDate dscDate = entity.getDscDate() == null ? LocalDate.MAX : entity.getDscDate();
          return !(newEffDate.isAfter(dscDate) && newDscDate.isAfter(dscDate))
              && !(newEffDate.isBefore(effDate) && newDscDate.isBefore(effDate));

        });
    if (overlap) {
      throw SharedErrorCode.DATE_RANGE_OVERLAP.toException(newEffDate.toString(),
          newDscDate.toString());
    }
  }

  private boolean validatedFreeSeating(BasicFarePrice newPrice, BasicFarePlan basicFarePlan) {
    boolean ret = false;
    if (basicFarePlan.getServiceType().equals(ServiceType.FREE_SEATING)) {
      if (newPrice.getEffDate() == null) {
        throw FareErrorCode.FA_BASIC_FARE_PRICE_FREE_SEATING_TYPE_EFF_SALE_DATE_NULL.toException();
      }

      ret = true;
    }

    return ret;
  }

  private void updateCurrentPriceDscDate(BasicFarePlan farePlan, LocalDate newDscDate) {
    BasicFarePrice current =
        repo.findByBasicFarePlan(farePlan, Sort.by(Sort.Direction.DESC, COLUMN_EFF_DATE)).stream()
            .filter(price -> price.getEffDate().isBefore(LocalDate.now())
                || price.getEffDate().isEqual(LocalDate.now()))
            .findFirst().orElseThrow(
                FareErrorCode.FA_BASIC_FARE_PRICE_UNABLE_TO_FIND_CURRENT_VERSION::toException);
    current.setDscDate(newDscDate);
    repo.save(current);
    log.info("current price of : {}, dscDate updated as : {}", current.getBasicFarePlan().getCode(),
        current.getDscDate());
  }


  private void parallelUpdate(BasicFarePriceInput input) {

    BasicFarePlan farePlan = basicFarePlanRepo.findById(input.getFarePlanId()).orElseThrow(
        FareErrorCode.FA_BASIC_FARE_PRICE_CANNOT_ASSOCIATE_TO_THE_BASIC_FARE_PLAN::toException);

    Boolean isFreeSeating = farePlan.getServiceType().equals(ServiceType.FREE_SEATING);
    Boolean isFuture = input.getEffDate().isAfter(LocalDate.now());

    // update current version's dscDate as newEffDate -1 day
    if (Boolean.TRUE.equals(isFreeSeating) && Boolean.TRUE.equals(isFuture)) {
      this.updateCurrentPriceDscDate(farePlan, input.getEffDate().minusDays(1));
    }

    String farePlanCode = farePlan.getDisplayCode();

    Boolean isParallelUpdate = isFreeSeating && (farePlanCode != null);
    if (Boolean.FALSE.equals(isParallelUpdate)) {
      return;
    }
    log.info("* parallelUpdate");

    Pair<BasicFarePrice, List<BasicFarePrice>> target =
        this.getCurrentAndFuturePricesByCode(farePlanCode);
    BasicFarePrice current = target.getLeft();
    List<BasicFarePrice> olds = target.getRight();

    if (Boolean.TRUE.equals(isFuture)) {
      current.setDscDate(input.getEffDate().minusDays(1));
      current = repo.save(current);

      log.info("current price of : {}, dscDate updated as: {}",
          current.getBasicFarePlan().getCode(), current.getDscDate());

      BasicFarePrice future = new BasicFarePrice();
      future.setBasicFarePlan(current.getBasicFarePlan());
      future.setEffDate(input.getEffDate());
      future.setDscDate(null);
      future.getDetails().addAll(createPriceDetails(input.getBasicFareMatrix(), future));

      repo.save(future);
      log.info("future saved: {}, effDate: {}, dscDate updated as: {}",
          future.getBasicFarePlan().getCode(), future.getEffDate(), future.getDscDate());

      repo.deleteAll(olds);
      log.info("old futures deleted : {}", olds.size());

    } else {

      current.getDetails().clear();
      current.getDetails().addAll(createPriceDetails(input.getBasicFareMatrix(), current));
      repo.save(current);

      log.info("current price of :{}, dscDate updated as:{}", current.getBasicFarePlan().getCode(),
          current.getDscDate());
    }
  }

  private List<BasicFarePriceDetail> createPriceDetails(
      List<BasicFarePriceDetailInput> inputDetails, BasicFarePrice entity) {
    List<BasicFarePriceDetail> newDetails = new ArrayList<>();
    for (BasicFarePriceDetailInput inputDetail : inputDetails) {
      BasicFarePriceDetail newDetail = new BasicFarePriceDetail();
      newDetail.setBasicFarePrice(entity);
      newDetail.setUnitPrice(inputDetail.getUnitPrice());
      StationPairProjection pair =
          stationPairRepo.findById(inputDetail.getStationPairId()).orElseThrow(
              FareErrorCode.FA_BASIC_FARE_PRICE_DETAIL_CANNOT_ASSOCIATE_TO_THE_STATION_PAIR::toException);
      newDetail.setStationPairProjection(pair);
      newDetails.add(newDetail);
    }
    return newDetails;
  }

  private Pair<BasicFarePrice, List<BasicFarePrice>> getCurrentAndFuturePricesByCode(
      String farePlanCode) {

    BasicFarePlan farePlan = basicFarePlanRepo.findByCode(farePlanCode).orElseThrow(
        FareErrorCode.FA_BASIC_FARE_PRICE_CANNOT_ASSOCIATE_TO_THE_BASIC_FARE_PLAN::toException);

    List<BasicFarePrice> targets =
        repo.findByBasicFarePlan(farePlan, Sort.by(Sort.Direction.DESC, COLUMN_EFF_DATE));

    List<BasicFarePrice> futures = new ArrayList<>();
    BasicFarePrice current = targets.get(0);
    for (BasicFarePrice target : targets) {
      if (target.getEffDate().isAfter(LocalDate.now())) {
        futures.add(target);
      } else {
        current = target;
        break;
      }
    }
    return Pair.of(current, futures);
  }


  private void createFutureVersionControl(LocalDate effDate) {

    LocalDateTime effDateTime;
    // 未來版->生效日凌晨執行
    if (effDate.isAfter(LocalDate.now())) {
      effDateTime = LocalDateTime.of(effDate, LocalTime.MIN);

      // 現在版->一分鐘後執行
    } else {
      effDateTime = LocalDateTime.now().plusMinutes(1);
    }

    CoBrandedCardOperatingParameterFutureVersionControl futureVersionControl =
        futureVersionControlService.init(
            effDateTime,
            CoBrandedCardOperatingParameterFutureVersionControl.class);

    futureVersionControl.setEffDate(effDate);
    futureVersionControl.setCreateUser(interceptor.getUserId());
    futureVersionControl.setCorrelationId(interceptor.getCorrelationId());
    futureVersionControl = futureVersionRepo.save(futureVersionControl);

    futureVersionControlService.scheduleFutureVersionControl(futureVersionControl,
        JOB_BSM_CO_BRANDED_CARD_OPERATING_PARAMETER, interceptor.getCorrelationId());
  }

  private void deleteFutureVersionControl(LocalDate effDate) {

    List<CoBrandedCardOperatingParameterFutureVersionControl> activeFutureVersionControls =
        futureVersionRepo.findByEffDateAndStatusInAndDeleted(effDate,
            Arrays.asList(FutureVersionControlStatus.ACCEPTED,
                FutureVersionControlStatus.SCHEDULED),
            false);

    if (!activeFutureVersionControls.isEmpty()) {
      futureVersionControlService.deleteFutureVersionControls(activeFutureVersionControls);
    }

  }

  private void validateFutureVersionControlStepRunning(LocalDate effDate) {

    List<CoBrandedCardOperatingParameterFutureVersionControl> result =
        futureVersionRepo.findByEffDateAndStatus(effDate, FutureVersionControlStatus.STARTED);
    if (!result.isEmpty()) {
      throw FareErrorCode.FA_CO_BRANDED_CARD_OPERATING_PARAMETER_STEP_IS_RUNNING.toException(
          effDate.toString());
    }
  }
}
