/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardBatchInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardBatchOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardInfoInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardInfoOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.CreditCardWeekdayPromotionDto;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionDto;
import com.ibm.tw.thsrc.bsm.fa.enums.CreditCardCampaignType;
import com.ibm.tw.thsrc.bsm.fare.entity.CampaignCreditCardBatch;
import com.ibm.tw.thsrc.bsm.fare.entity.CampaignCreditCardBatch_;
import com.ibm.tw.thsrc.bsm.fare.entity.CampaignCreditCardInfo;
import com.ibm.tw.thsrc.bsm.fare.entity.CampaignCreditCardInfo_;
import com.ibm.tw.thsrc.bsm.fare.entity.CreditCardWeekdayPromotion;
import com.ibm.tw.thsrc.bsm.fare.repository.CampaignCreditCardBatchRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CampaignCreditCardInfoRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.CampaignCreditCardBatchFilter;
import com.ibm.tw.thsrc.bsm.fare.vo.CampaignCreditCardBatchSort;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.TypedSort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class CampaignCreditCardBatchQueryService extends
AbstractQueryService<CampaignCreditCardBatch, CampaignCreditCardBatchOutput, CampaignCreditCardBatchFilter, CampaignCreditCardBatchSort> {

  @Autowired
  protected CampaignCreditCardBatchRepository repository;
  @Autowired
  protected CampaignCreditCardInfoRepository infoRepo;
  @Autowired
  private ObjectMapper objectMapper;

  @Override
  public CampaignCreditCardBatchOutput read(Long id) {
    return repository
        .findById(id)
        .map(this::translate)
        .orElseThrow(ResourceNotFoundException::new);
  }

  @Override
  public Page<CampaignCreditCardBatchOutput> search(Search search) {
    PageRequest pr = translateToPageRequest(search);
    Specification<CampaignCreditCardBatch> spec = translateToSpecification(search);
    return repository
        .findAll(spec, pr)
        .map(this::translate);
  }

  @Override
  protected CampaignCreditCardBatchOutput translate(CampaignCreditCardBatch aggregateRoot) {
    CampaignCreditCardBatchOutput output = new CampaignCreditCardBatchOutput();
    output.setId(aggregateRoot.getId());
    output.setImportDate(aggregateRoot.getImportDate());
    output.setBankName(aggregateRoot.getBankName());
    output.setDataVersion(aggregateRoot.getDataVersion());

    List<CampaignCreditCardInfoOutput> infoList = new ArrayList<>();
    for (CampaignCreditCardInfo info : aggregateRoot.getCardNos()) {
      
      // 已過期卡號不顯示
      if (info.getDscDate() != null && info.getDscDate().isBefore(LocalDate.now())
          && info.getDscSaleDate() != null && info.getDscSaleDate().isBefore(LocalDate.now())) {
        continue;
      }
      
      CampaignCreditCardInfoOutput infoOutput = new CampaignCreditCardInfoOutput();

      infoOutput.setId(info.getId());
      infoOutput.setDataVersion(info.getDataVersion());

      infoOutput.setBankName(aggregateRoot.getBankName());
      infoOutput.setImportDate(aggregateRoot.getImportDate());

      infoOutput.setCardNo(info.getCardNo());
      infoOutput.setDscDate(info.getDscDate());
      infoOutput.setEffDate(info.getEffDate());
      infoOutput.setDscSaleDate(info.getDscSaleDate());
      infoOutput.setEffSaleDate(info.getEffSaleDate());

      infoOutput.setCreditCardCampaignType(info.getCreditCardCampaignType());

      List<CreditCardWeekdayPromotionDto> promotionDtos = new ArrayList<>();

      for (CreditCardWeekdayPromotion promotion : info.getPromotions()) {
        CreditCardWeekdayPromotionDto creditCardWeekdayPromotionDto = new CreditCardWeekdayPromotionDto();
        creditCardWeekdayPromotionDto.setId(promotion.getId());
        creditCardWeekdayPromotionDto.setDayOfWeek(promotion.getDayOfWeek());
        if (promotion.getPromotionPlan() != null) {

          creditCardWeekdayPromotionDto.setPromotionId(promotion.getPromotionPlan().getId());

          PromotionDto promotionDto = new PromotionDto();
          promotionDto.setId(promotion.getPromotionPlan().getId());
          promotionDto.setCode(promotion.getPromotionPlan().getCode());
          promotionDto.setName(promotion.getPromotionPlan().getName());

          creditCardWeekdayPromotionDto.setPromotion(promotionDto);
        }
        promotionDtos.add(creditCardWeekdayPromotionDto);
      }
      infoOutput.setCreditCardWeekdayPromotion(promotionDtos);

      infoList.add(infoOutput);
    }
    output.setCampaignCreditCardInfos(infoList);
    return output;
  }

  @Override
  protected Sort translate(CampaignCreditCardBatchSort sortBy, Direction sortDirection) {

    TypedSort<CampaignCreditCardBatch> typedSort = Sort.sort(CampaignCreditCardBatch.class);

    if (sortBy.isBankName()) {
      typedSort.by(CampaignCreditCardBatch::getBankName);
    } else if (sortBy.isImportDate()) {
      typedSort.by(CampaignCreditCardBatch::getImportDate);
    } else {
      typedSort.by(CampaignCreditCardBatch::getId);
    }

    if (typedSort.isSorted()) {
      if (Sort.Direction.DESC == sortDirection) {
        return typedSort.descending();
      } else {
        return typedSort.ascending();
      }
    } else {
      return typedSort;
    }
  }

  @Override
  protected Predicate translate(ComparisonExpression<CampaignCreditCardBatchFilter> expression,
      Root<CampaignCreditCardBatch> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {

    ComparisonOperator op = expression.getOperator();

    if (expression.getAttributeValue().getBankName() != null) {
      Path<String> bankNamePath = root.get(CampaignCreditCardBatch_.BANK_NAME);
      
      return this.withFixedCriteria(root, query, criteriaBuilder
          , translate(bankNamePath, op, expression.getAttributeValue().getBankName(), criteriaBuilder));

    } else if (expression.getAttributeValue().getCardNo() != null) {

      Path<String> cardNoPath = root.join(CampaignCreditCardBatch_.CARD_NOS).get(CampaignCreditCardInfo_.CARD_NO);
      
      return this.withFixedCriteria(root, query, criteriaBuilder
          , translate(cardNoPath, op, expression.getAttributeValue().getCardNo(), criteriaBuilder));

    } else if (expression.getAttributeValue().getCreditCardCampaignType() != null) {
      Subquery<Integer> subQuery = query.subquery(Integer.class);
      Root<CampaignCreditCardInfo> subQueryRoot = subQuery.from(CampaignCreditCardInfo.class);

      Path<CreditCardCampaignType> campaignTypePath = subQueryRoot.get(CampaignCreditCardInfo_.CREDIT_CARD_CAMPAIGN_TYPE);

      Predicate p1 = criteriaBuilder.equal(
          subQueryRoot.get(CampaignCreditCardInfo_.BATCH), root);

      Predicate p2 =
          translate(campaignTypePath, ComparisonOperator.EQ,  CreditCardCampaignType.valueOf(expression.getAttributeValue().getCreditCardCampaignType()), criteriaBuilder);

      subQuery.select(criteriaBuilder.literal(1)).where(p1, p2);
      
      return this.withFixedCriteria(root, query, criteriaBuilder
          , criteriaBuilder.exists(subQuery));

    } else {
      return this.withFixedCriteria(root, query, criteriaBuilder, null);
    }
  }

  @Override
  protected Function<Map<String, Object>, CampaignCreditCardBatchFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, CampaignCreditCardBatchFilter.class);
  }

  @Override
  protected Function<Map<String, Boolean>, CampaignCreditCardBatchSort> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, CampaignCreditCardBatchSort.class);
  }

  /**
   * CampaignCreditCardBatch 轉換為 CampaignCreditCardBatchOutput
   * @param aggregateRoot
   * @return
   */
  public CampaignCreditCardBatchOutput translateToOutput(CampaignCreditCardBatch aggregateRoot) {
    return this.translate(aggregateRoot);
  }
  
  /**
   * 加入固定查訊條件
   * @param root
   * @param criteriaBuilder
   * @param predicate
   * @return
   */
  Predicate withFixedCriteria(Root<CampaignCreditCardBatch> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder, Predicate predicate) {
    // 排除已過期卡號
    Subquery<Integer> subQuery = query.subquery(Integer.class);
    Root<CampaignCreditCardInfo> subQueryRoot = subQuery.from(CampaignCreditCardInfo.class);
    Path<LocalDate> dscDatePath = subQueryRoot.get(CampaignCreditCardInfo_.DSC_DATE);
    Path<LocalDate> dscSaleDatePath = subQueryRoot.get(CampaignCreditCardInfo_.DSC_SALE_DATE);

    Predicate p1 = criteriaBuilder.equal(
        subQueryRoot.get(CampaignCreditCardInfo_.BATCH), root);
    Predicate p2 = criteriaBuilder.greaterThanOrEqualTo(dscDatePath, LocalDate.now());
    Predicate p3 = criteriaBuilder.greaterThanOrEqualTo(dscSaleDatePath, LocalDate.now());
    // dscDate null代表無限期
    Predicate p4 = criteriaBuilder.isNull(dscDatePath);
    Predicate p5 = criteriaBuilder.isNull(dscSaleDatePath);

    subQuery.select(criteriaBuilder.literal(1)).where(p1, criteriaBuilder.or(p2, p3, p4, p5));
    Predicate subQueryPredicate = criteriaBuilder.exists(subQuery);
    
    if (predicate == null) {
      return subQueryPredicate;
    }
    return criteriaBuilder.and(predicate, subQueryPredicate);
  }
  
  public OperationFunction getOpFunction(CampaignCreditCardBatchOutput input) {
    return getOpFunction(input.getCampaignCreditCardInfos().get(0));
  } 
  
  public OperationFunction getOpFunction(CampaignCreditCardInfoOutput input) {
    return getOpFunction(input.getCreditCardCampaignType());
  }
  
  public OperationFunction getOpFunction(CampaignCreditCardBatchInput input) {
    return getOpFunction(input.getCampaignCreditCardInfos().get(0));
  }
  
  public OperationFunction getOpFunction(CampaignCreditCardInfoInput input) {
    return getOpFunction(input.getCreditCardCampaignType());
  }
  
  public OperationFunction getOpFunction(Long infoId) {
    CampaignCreditCardInfo info = infoRepo.findById(infoId)
        .orElseThrow(() -> FareErrorCode.FA_CREDIT_CARD_CARD_INFO_EXIST_IN_BSM_DB
            .toException(infoId.toString()));
    return getOpFunction(info.getCreditCardCampaignType());
  }
  
  public OperationFunction getOpFunction(CreditCardCampaignType creditCardCampaignType) {
    if (creditCardCampaignType.equals(CreditCardCampaignType.STANDARD_PROMOTION)) {
      return OperationFunction.STANDARD_CARD;
    } else if (creditCardCampaignType.equals(CreditCardCampaignType.BUSINESS_UPGRADE)) {
      return OperationFunction.BUSINESS_CARD;
    }
    throw new UnsupportedOperationException("Unknown CreditCardCampaignType");
  }
}
