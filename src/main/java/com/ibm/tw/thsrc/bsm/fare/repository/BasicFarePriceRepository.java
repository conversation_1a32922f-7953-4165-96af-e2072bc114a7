/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePlan;
import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePrice;
import java.util.List;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface BasicFarePriceRepository extends JpaRepository<BasicFarePrice, Long>,
    JpaSpecificationExecutor<BasicFarePrice> {

  List<BasicFarePrice> findByBasicFarePlan(BasicFarePlan basicFarePlan, Sort by);

  List<BasicFarePrice> findByBasicFarePlan(BasicFarePlan basicFarePlan);
}
