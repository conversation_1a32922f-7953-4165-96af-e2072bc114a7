/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.cronjob.enums.FutureVersionControlStatus;
import com.ibm.tw.thsrc.bsm.fare.entity.RegularTicketRuleFutureVersionControl;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface RegularTicketRuleFutureVersionControlRepository extends
    JpaRepository<RegularTicketRuleFutureVersionControl, Long> {

  List<RegularTicketRuleFutureVersionControl> findByRegularTicketRuleTemp_IdAndStatus(
      Long ticketProductPolicyId, FutureVersionControlStatus status);

  List<RegularTicketRuleFutureVersionControl> findByRegularTicketRuleTemp_IdAndStatusInAndDeleted(
      Long ticketProductPolicyId, List<FutureVersionControlStatus> statuses, boolean deleted);
}
