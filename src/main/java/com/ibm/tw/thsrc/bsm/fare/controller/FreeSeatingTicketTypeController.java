/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.api.FreeSeatingTicketTypeApi;
import com.ibm.tw.thsrc.bsm.fa.dto.FreeSeatingTicketTypeOutput;
import com.ibm.tw.thsrc.bsm.fare.service.FreeSeatingTicketTypeQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/FreeSeatingTicketTypes")
public class FreeSeatingTicketTypeController implements FreeSeatingTicketTypeApi {

  @Autowired
  private FreeSeatingTicketTypeQueryService queryService;

  @Override
  public Page<FreeSeatingTicketTypeOutput> search(Search search) {
    return queryService.search(search);
  }
}
