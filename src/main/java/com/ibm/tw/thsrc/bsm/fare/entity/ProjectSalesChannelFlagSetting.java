/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.entity;

import com.ibm.tw.thsrc.bsm.core.entity.AggregateRoot;
import com.ibm.tw.thsrc.bsm.fa.enums.ProjectSalesChannelFlag;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@NoArgsConstructor
public class ProjectSalesChannelFlagSetting extends AggregateRoot {

  public ProjectSalesChannelFlagSetting(ProjectSalesChannelFlag flag, boolean setting,
      FareProject fareProject) {
    this.flag = flag;
    this.setting = setting;
    this.fareProject = fareProject;
  }

  @ManyToOne(optional = false)
  private FareProject fareProject;
  @Enumerated(value = EnumType.STRING)
  private ProjectSalesChannelFlag flag;
  private boolean setting;
}
