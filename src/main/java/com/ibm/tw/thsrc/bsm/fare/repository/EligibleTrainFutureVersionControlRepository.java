/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.cronjob.enums.FutureVersionControlStatus;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainFutureVersionControl;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface EligibleTrainFutureVersionControlRepository
    extends JpaRepository<EligibleTrainFutureVersionControl, Long>,
    JpaSpecificationExecutor<EligibleTrainFutureVersionControl> {

  List<EligibleTrainFutureVersionControl> findByEligibleTrainTemp_IdAndStatusIn(
      Long eligibleTrainTempId, List<FutureVersionControlStatus> statuses);

  List<EligibleTrainFutureVersionControl> findByEligibleTrainTemp_IdAndStatus(
      Long eligibleTrainTempId, FutureVersionControlStatus status);

  List<EligibleTrainFutureVersionControl> findByEligibleTrainTemp_IdAndStatusInAndDeleted(
      Long eligibleTrainTempId, List<FutureVersionControlStatus> statuses, boolean deleted);
}
