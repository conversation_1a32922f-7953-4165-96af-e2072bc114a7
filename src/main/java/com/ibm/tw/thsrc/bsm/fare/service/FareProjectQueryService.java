/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source
 * code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.cosium.spring.data.jpa.entity.graph.domain2.EntityGraphType;
import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.ColorOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.FareProjectOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.ProjectFlagSettingOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.ProjectOverviewOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.ProjectSalesChannelFlagSettingOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.FareProjectType;
import com.ibm.tw.thsrc.bsm.fa.enums.ProjectFlag;
import com.ibm.tw.thsrc.bsm.fa.enums.ProjectFlagChangeRuleType;
import com.ibm.tw.thsrc.bsm.fa.enums.ProjectFlagRefundRuleType;
import com.ibm.tw.thsrc.bsm.fa.enums.ProjectSalesChannelFlag;
import com.ibm.tw.thsrc.bsm.fa.enums.TicketEndorsementType;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProjectMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProjectMapping_;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject_;
import com.ibm.tw.thsrc.bsm.fare.entity.ProjectFlagSetting;
import com.ibm.tw.thsrc.bsm.fare.entity.ProjectFlagSetting_;
import com.ibm.tw.thsrc.bsm.fare.entity.ProjectSalesChannelFlagSetting;
import com.ibm.tw.thsrc.bsm.fare.entity.ProjectSalesChannelFlagSetting_;
import com.ibm.tw.thsrc.bsm.fare.repository.FareProjectMappingRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.FareProjectRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.FareProjectFilter;
import com.ibm.tw.thsrc.bsm.fare.vo.FareProjectSort;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.TypedSort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class FareProjectQueryService
    extends AbstractQueryService<FareProject, FareProjectOutput, FareProjectFilter, FareProjectSort>
    implements FareProjectQueryApi {

  @Autowired
  private ObjectMapper objectMapper;

  @Autowired
  private FareProjectRepository repo;

  @Autowired
  private FareProjectMappingRepository mappingRepo;

  @Override
  public FareProjectOutput read(Long id) {
    FareProject entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    return this.translate(entity);
  }

  @Override
  public Page<FareProjectOutput> search(Search searchRequest) {
    PageRequest pr = translateToPageRequest(searchRequest);
    Specification<FareProject> spec = translateToSpecification(searchRequest);
    
    Page<FareProject> page =
        repo.findAll(
            spec,
            pr,
            new NamedEntityGraph(
                EntityGraphType.FETCH, "graph.fareProject.fareProjectMappings.promotionPlan"));

    return page.map(this::translate);
  }

  public List<ProjectOverviewOutput> validate() {
    // default sort as FareProjectMapping
    String projectCodeNumber = FareProjectMapping_.FARE_PROJECT + "." + FareProject_.CODE_NUMBER;
    String projectCodeType = FareProjectMapping_.FARE_PROJECT + "." + FareProject_.CODE_TYPE;
    Sort sort = Sort.by(projectCodeNumber, projectCodeType).ascending();
    List<FareProjectMapping> fareProjectMappings = mappingRepo.findAll(sort);

    List<ProjectOverviewOutput> results = new ArrayList<>();

    fareProjectMappings.forEach(e -> {
      ProjectOverviewOutput overviewOutput = new ProjectOverviewOutput();

      if (e.getFareProject().getType().equals(FareProjectType.STANDARD)) {
        overviewOutput.setStandardProjectCode(e.getFareProject().getCode());
      } else {
        overviewOutput.setBusinessProjectCode(e.getFareProject().getCode());
      }

      translateOverview(overviewOutput, e);
      overviewOutput.setProjectInfo(translate(e.getFareProject()));

      results.add(overviewOutput);
    });

    return results;
  }

  @Override
  protected FareProjectOutput translate(FareProject entity) {
    FareProjectOutput response = new FareProjectOutput();
    response.setId(entity.getId());
    response.setCode(entity.getCode());
    response.setType(entity.getType());
    response.setName(entity.getName());
    response.setEnName(entity.getEnName());
    response.setZhName(entity.getZhName());
    response.setDataVersion(entity.getDataVersion());

    ProjectFlagSettingOutput flagSettingOutput = new ProjectFlagSettingOutput();
    flagSettingOutput.setAiValue(entity.getAiValue());
    flagSettingOutput.setTicketEndorsementType(entity.getTicketEndorsementType());

    if (Objects.nonNull(entity.getRefundRule())) {
      flagSettingOutput.setRefundRule(entity.getRefundRule());
    }
    if (Objects.nonNull(entity.getChangeRule())) {
      flagSettingOutput.setChangeRule(entity.getChangeRule());
    }
    if (Objects.nonNull(entity.getTextColor())) {
      String[] textColors = entity.getTextColor().split(",");
      ColorOutput color = new ColorOutput();
      color.setRed(textColors[0]);
      color.setGreen(textColors[1]);
      color.setBlue(textColors[2]);
      flagSettingOutput.setTextColor(color);
    }
    if (Objects.nonNull(entity.getBackgroundColor())) {
      String[] backgroundColors = entity.getBackgroundColor().split(",");
      ColorOutput color = new ColorOutput();
      color.setRed(backgroundColors[0]);
      color.setGreen(backgroundColors[1]);
      color.setBlue(backgroundColors[2]);
      flagSettingOutput.setBackgroundColor(color);
    }

    this.translateFlagSetting(flagSettingOutput, entity);
    response.setProjectFlagSettings(flagSettingOutput);
    response.setProjectSalesChannelFlagSettings(translateSalesFlagSetting(entity));

    // List<FareProjectMapping> fareProjectMappings = this.mappingRepo.findByFareProject(entity);
    List<String> promotionCodes = entity.getFareProjectMappings().stream()
        .map(m -> m.getPromotionPlan() != null && m.getPromotionPlan().getCode() != null
            ? m.getPromotionPlan().getCode()
            : "-")
        .collect(Collectors.toList());
    response.setPromotionCodes(promotionCodes);

    return response;
  }

  private void translateFlagSetting(ProjectFlagSettingOutput flagSettingOutput,
      FareProject entity) {
    List<ProjectFlagSetting> settings = entity.getFlagSettings();

    settings.forEach(s -> {
      if (ProjectFlag.ID_DIGIT_DISPLAY_P.equals(s.getFlag())) {
        flagSettingOutput.setIdDigitDisplay(Boolean.parseBoolean(s.getSetting()));
      } else if (ProjectFlag.SMIS_CHECK_S.equals(s.getFlag())) {
        flagSettingOutput.setSmisCheck(Boolean.parseBoolean(s.getSetting()));
      } else if (ProjectFlag.TICKET_BOOTH_LIGHT_T.equals(s.getFlag())) {
        flagSettingOutput.setTbLight(Boolean.parseBoolean(s.getSetting()));
      } else if (ProjectFlag.NO_REISSUE_N.equals(s.getFlag())) {
        flagSettingOutput.setNoReissue(Boolean.parseBoolean(s.getSetting()));
      }
    });
  }

  private ProjectSalesChannelFlagSettingOutput translateSalesFlagSetting(FareProject entity) {
    ProjectSalesChannelFlagSettingOutput salesChannelFlagSettingOutput =
        new ProjectSalesChannelFlagSettingOutput();
    List<ProjectSalesChannelFlagSetting> settings = entity.getSalesChannelFlagSettings();

    settings.forEach(s -> {
      if (ProjectSalesChannelFlag.TWE_COUPON_CODE_REQ.equals(s.getFlag())) {
        salesChannelFlagSettingOutput.setCouponRequired(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_MEMBER_POINT_GIVEN.equals(s.getFlag())) {
        salesChannelFlagSettingOutput.setMemberPointGiven(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_CPR_MEMBER_PTS_GIVEN.equals(s.getFlag())) {
        salesChannelFlagSettingOutput.setCprMemberPointGiven(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_OD_UNMODIFIABLE.equals(s.getFlag())) {
        salesChannelFlagSettingOutput.setOdUnmodified(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_SEAT_MODIFIABLE.equals(s.getFlag())) {
        salesChannelFlagSettingOutput.setSeatModifiable(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_ADULT_CHILD_ID_REQ.equals(s.getFlag())) {
        salesChannelFlagSettingOutput.setAdultChildIdRequired(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_MANUAL_SEATING_ONLY.equals(s.getFlag())) {
        salesChannelFlagSettingOutput.setManualSeatOnly(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_GROUP_CANCELABLE.equals(s.getFlag())) {
        salesChannelFlagSettingOutput.setGroupCancelable(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_SIGN_PRINT_REMINDER.equals(s.getFlag())) {
        salesChannelFlagSettingOutput.setSignPrintReminder(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_CURRENT_DAY_TRAIN_ONLY.equals(s.getFlag())) {
        salesChannelFlagSettingOutput.setCurrentDayTrainOnly(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_PROMOTION_MODIFIABLE.equals(s.getFlag())) {
        salesChannelFlagSettingOutput.setPromotionModifiable(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_CAR_TYPE_MODIFIABLE.equals(s.getFlag())) {
        salesChannelFlagSettingOutput.setCarTypeModifiable(s.isSetting());
      } else if (ProjectSalesChannelFlag.TWE_ROUND_TRIP_AVAILABLE.equals(s.getFlag())) {
        salesChannelFlagSettingOutput.setRoundTripAvailable(s.isSetting());
      }
    });

    return salesChannelFlagSettingOutput;
  }

  private void translateOverview(ProjectOverviewOutput response, FareProjectMapping entity) {
    if (Objects.nonNull(entity.getBasicFarePlan())) {
      response.setBasicFarePlanCode(entity.getBasicFarePlan().getCode());
    }

    if (Objects.nonNull(entity.getPromotionPlan())) {
      response.setPromotionPlanCode(entity.getPromotionPlan().getCode());
    }

    if (Objects.nonNull(entity.getProfileDiscountPlan())) {
      response.setProfileDiscountTypeCode(
          entity.getProfileDiscountPlan().getProfileDiscountType().getCode());
      response
          .setPassengerProfileCode(entity.getProfileDiscountPlan().getPassengerProfile().getCode());
      response.setPassengerProfileName(
          entity.getProfileDiscountPlan().getPassengerProfile().getZhPrintName());
    }
  }

  @Override
  protected Sort translate(FareProjectSort sortBy, Direction direction) {
    TypedSort<FareProject> typedSort = Sort.sort(FareProject.class);

    if (sortBy.isCode()) {
      typedSort//
          .by(FareProject::getCode);
    } else if (sortBy.isName()) {
      typedSort//
          .by(FareProject::getName);
    } else if (sortBy.isEnName()) {
      typedSort//
          .by(FareProject::getEnName);
    } else if (sortBy.isZhName()) {
      typedSort//
          .by(FareProject::getZhName);
    } else if (sortBy.isTicketEndorsement()) {
      typedSort//
          .by(FareProject::getTicketEndorsementType);
    } else if (sortBy.isRefundRule()) {
      typedSort//
          .by(FareProject::getRefundRule);
    } else if (sortBy.isChangeRule()) {
      typedSort//
          .by(FareProject::getChangeRule);
    } else if (sortBy.isPromotions()) {

      typedSort.by((Function<FareProject, String>) entity -> entity.getFareProjectMappings().get(0)
          .getPromotionPlan().getCode());
    }



    if (typedSort.isSorted()) {
      if (Sort.Direction.DESC == direction) {
        return typedSort.descending();
      } else {
        return typedSort.ascending();
      }
    } else { // default with no sortBy option
      return Sort//
          .by(FareProject_.CODE_NUMBER, FareProject_.CODE_TYPE).ascending();
    }
  }

  @Override
  protected Predicate translate(ComparisonExpression<FareProjectFilter> expression,
      Root<FareProject> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    ComparisonOperator op = expression.getOperator();

    if (expression.getAttributeValue().getCode() != null) {
      Path<String> path = root.get(FareProject_.CODE);
      String value = expression.getAttributeValue().getCode();
      return translate(path, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getName() != null) {
      Path<String> path = root.get(FareProject_.NAME);
      String value = expression.getAttributeValue().getName();
      return translate(path, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getFlag() != null) {

      return generateFlagFilter(expression, root, query, criteriaBuilder);

    } else if (expression.getAttributeValue().getHasSalesFlagSettings() != null) {
      boolean value = expression.getAttributeValue().getHasSalesFlagSettings();
      if (value) {
        Subquery<Integer> subQuery = query.subquery(Integer.class);
        Root<ProjectSalesChannelFlagSetting> subQueryRoot =
            subQuery.from(ProjectSalesChannelFlagSetting.class);

        Path<ProjectSalesChannelFlag> flagPath =
            subQueryRoot.get(ProjectSalesChannelFlagSetting_.FLAG);
        Predicate flagPredicate = translate(flagPath, ComparisonOperator.EQ,
            ProjectSalesChannelFlag.TWE_COUPON_CODE_REQ, criteriaBuilder);
        Predicate p1 = criteriaBuilder
            .equal(subQueryRoot.get(ProjectSalesChannelFlagSetting_.FARE_PROJECT), root);

        subQuery.select(criteriaBuilder.literal(1)).where(p1, flagPredicate);
        return criteriaBuilder.exists(subQuery);

      } else {
        return criteriaBuilder.isEmpty(root.get(FareProject_.SALES_CHANNEL_FLAG_SETTINGS));
      }

    } else if (expression.getAttributeValue().getSalesChannelFlag() != null) {
      ProjectSalesChannelFlag flag = expression.getAttributeValue().getSalesChannelFlag();

      Subquery<Integer> subQuery = query.subquery(Integer.class);
      Root<ProjectSalesChannelFlagSetting> subQueryRoot =
          subQuery.from(ProjectSalesChannelFlagSetting.class);

      Path<ProjectSalesChannelFlag> flagPath =
          subQueryRoot.get(ProjectSalesChannelFlagSetting_.FLAG);
      Path<Boolean> settingPath = subQueryRoot.get(ProjectSalesChannelFlagSetting_.SETTING);

      Predicate flagPredicate = translate(flagPath, ComparisonOperator.EQ, flag, criteriaBuilder);
      Predicate settingPredicate =
          translate(settingPath, ComparisonOperator.EQ, true, criteriaBuilder);

      Predicate p2 = criteriaBuilder.and(flagPredicate, settingPredicate);
      Predicate p1 = criteriaBuilder
          .equal(subQueryRoot.get(ProjectSalesChannelFlagSetting_.FARE_PROJECT), root);

      subQuery.select(criteriaBuilder.literal(1)).where(p1, p2);
      return criteriaBuilder.exists(subQuery);

    } else {
      return null;
    }
  }

  private Predicate generateFlagFilter(ComparisonExpression<FareProjectFilter> expression,
      Root<FareProject> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    ComparisonOperator op = ComparisonOperator.EQ;
    ProjectFlag flag = expression.getAttributeValue().getFlag();

    if (isBooleanFlag(flag)) {
      Subquery<Integer> subQuery = query.subquery(Integer.class);
      Root<ProjectFlagSetting> subQueryRoot = subQuery.from(ProjectFlagSetting.class);

      Path<ProjectFlag> flagPath = subQueryRoot.get(ProjectFlagSetting_.FLAG);
      Path<String> settingPath = subQueryRoot.get(ProjectFlagSetting_.SETTING);

      Predicate flagPredicate = translate(flagPath, ComparisonOperator.EQ, flag, criteriaBuilder);
      Predicate settingPredicate =
          translate(settingPath, ComparisonOperator.EQ, String.valueOf(true), criteriaBuilder);

      Predicate p2 = criteriaBuilder.and(flagPredicate, settingPredicate);
      Predicate p1 =
          criteriaBuilder.equal(subQueryRoot.get(ProjectFlagSetting_.FARE_PROJECT), root);

      subQuery.select(criteriaBuilder.literal(1)).where(p1, p2);
      return criteriaBuilder.exists(subQuery);

    } else if (isObjectFlag(flag)) {

      Path<ProjectFlagRefundRuleType> path = root.get(FareProject_.REFUND_RULE);
      ProjectFlagRefundRuleType value =
          ProjectFlagRefundRuleType.valueOf(expression.getAttributeValue().getFlag().toString());
      return translate(path, op, value, criteriaBuilder);

    } else if (isTicketFlag(flag)) {
      Path<TicketEndorsementType> path = root.get(FareProject_.TICKET_ENDORSEMENT_TYPE);
      TicketEndorsementType type = TicketEndorsementType.valueOf(flag.name());
      return translate(path, op, type, criteriaBuilder);

    } else if (flag.equals(ProjectFlag.CHANGE_FORBIDDEN_H) || flag.equals(ProjectFlag.CHANGE_RULE_I)
        || flag.equals(ProjectFlag.CHANGE_RULE_J)) {

      Path<ProjectFlagChangeRuleType> path = root.get(FareProject_.CHANGE_RULE);
      ProjectFlagChangeRuleType value =
          ProjectFlagChangeRuleType.valueOf(expression.getAttributeValue().getFlag().toString());
      return translate(path, op, value, criteriaBuilder);

    } else {
      Subquery<Integer> subQuery = query.subquery(Integer.class);
      Root<ProjectFlagSetting> subQueryRoot = subQuery.from(ProjectFlagSetting.class);
      Path<ProjectFlag> flagPath = subQueryRoot.get(ProjectFlagSetting_.FLAG);
      Predicate flagPredicate = translate(flagPath, ComparisonOperator.EQ, flag, criteriaBuilder);
      Predicate p1 =
          criteriaBuilder.equal(subQueryRoot.get(ProjectFlagSetting_.FARE_PROJECT), root);
      subQuery.select(criteriaBuilder.literal(1)).where(p1, flagPredicate);
      return criteriaBuilder.exists(subQuery);

    }
  }

  private boolean isBooleanFlag(ProjectFlag flag) {
    return flag.equals(ProjectFlag.ID_DIGIT_DISPLAY_P) || flag.equals(ProjectFlag.SMIS_CHECK_S)
        || flag.equals(ProjectFlag.TICKET_BOOTH_LIGHT_T) || flag.equals(ProjectFlag.NO_REISSUE_N);
  }

  private boolean isObjectFlag(ProjectFlag flag) {
    return flag.equals(ProjectFlag.REFUND_RULE_A) || flag.equals(ProjectFlag.REFUND_RULE_B)
        || flag.equals(ProjectFlag.REFUND_RULE_C) || flag.equals(ProjectFlag.REFUND_RULE_D)
        || flag.equals(ProjectFlag.REFUND_RULE_E) || flag.equals(ProjectFlag.REFUND_RULE_F)
        || flag.equals(ProjectFlag.REFUND_RULE_G);
  }

  private boolean isTicketFlag(ProjectFlag flag) {
    return flag.equals(ProjectFlag.ENDORSEMENT_1) || flag.equals(ProjectFlag.ENDORSEMENT_2)
        || flag.equals(ProjectFlag.ENDORSEMENT_3) || flag.equals(ProjectFlag.ENDORSEMENT_4)
        || flag.equals(ProjectFlag.ENDORSEMENT_5) || flag.equals(ProjectFlag.ENDORSEMENT_6)
        || flag.equals(ProjectFlag.ENDORSEMENT_7) || flag.equals(ProjectFlag.ENDORSEMENT_8)
        || flag.equals(ProjectFlag.ENDORSEMENT_9);
  }

  @Override
  protected Function<Map<String, Boolean>, FareProjectSort> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, FareProjectSort.class);
  }

  @Override
  protected Function<Map<String, Object>, FareProjectFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, FareProjectFilter.class);
  }

  @Override
  public List<FareProjectOutput> getFareProjects() {
    return repo.findAll().stream().map(this::translate).collect(Collectors.toList());
  }
}
