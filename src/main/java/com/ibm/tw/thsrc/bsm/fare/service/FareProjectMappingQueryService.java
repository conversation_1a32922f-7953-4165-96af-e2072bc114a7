/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.FareProjectMappingOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.PromotionServiceType;
import com.ibm.tw.thsrc.bsm.fare.entity.FarePlan_;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProjectMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProjectMapping_;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject_;
import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountPlan_;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan_;
import com.ibm.tw.thsrc.bsm.fare.repository.FareProjectMappingRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.FareProjectMappingFilter;
import com.ibm.tw.thsrc.bsm.fare.vo.FareProjectMappingSort;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class FareProjectMappingQueryService extends
    AbstractQueryService<FareProjectMapping, FareProjectMappingOutput, FareProjectMappingFilter, FareProjectMappingSort> implements
    FareProjectMappingQueryApi {

  private static final String SORT_CONJ = ".";
  @Autowired
  private FareProjectMappingRepository repo;

  @Autowired
  private ObjectMapper objectMapper;

  @Override
  public FareProjectMappingOutput read(Long id) {
    FareProjectMapping entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    return translate(entity);
  }

  @Override
  public Page<FareProjectMappingOutput> search(Search searchRequest) {
    PageRequest pr = translateToPageRequest(searchRequest);
    Specification<FareProjectMapping> spec = translateToSpecification(searchRequest);
    Page<FareProjectMapping> page = repo.findAll(spec, pr);
    return page.map(this::translate);
  }

  @Override
  protected FareProjectMappingOutput translate(FareProjectMapping entity) {
    FareProjectMappingOutput response = new FareProjectMappingOutput();
    response.setId(entity.getId());
    response.setDataVersion(entity.getDataVersion());
    response.setFareProjectId(entity.getFareProject().getId());
    response.setFareProjectCode(entity.getFareProject().getCode());

    if (Objects.nonNull(entity.getBasicFarePlan())) {
      response.setBasicFarePlanId(entity.getBasicFarePlan().getId());
      response.setBasicFarePlanCode(entity.getBasicFarePlan().getCode());
    }

    if (Objects.nonNull(entity.getPromotionPlan())) {
      response.setPromotionPlanId(entity.getPromotionPlan().getId());
      response.setPromotionServiceType(entity.getPromotionPlan().getServiceType());
      response.setPromotionPlanCode(entity.getPromotionPlan().getCode());
    }

    if (Objects.nonNull(entity.getProfileDiscountPlan())) {
      response.setProfileDiscountPlanId(entity.getProfileDiscountPlan().getId());
      response.setProfileDiscountTypeCode(
          entity.getProfileDiscountPlan().getProfileDiscountType().getCode());
      response.setPassengerProfileCode(
          entity.getProfileDiscountPlan().getPassengerProfile().getCode());
      response.setPassengerProfileName(
          entity.getProfileDiscountPlan().getPassengerProfile().getZhPrintName());
    }

    return response;
  }

  @Override
  protected Sort translate(FareProjectMappingSort sortBy, Direction sortDirection) {

    // 預設日期時間升冪
    Direction defaultDir = Direction.ASC;

    String projectCodeNumber =
        FareProjectMapping_.FARE_PROJECT + SORT_CONJ + FareProject_.CODE_NUMBER;
    String projectCodeType = FareProjectMapping_.FARE_PROJECT + SORT_CONJ + FareProject_.CODE_TYPE;
    Sort defaultSort = Sort.by(Arrays.asList(new Order(defaultDir, projectCodeNumber),
        new Order(defaultDir, projectCodeType)));

    Sort mainSort = null;
    Direction direction = Objects.isNull(sortDirection) ? defaultDir : sortDirection;
    String property;

    if (sortBy.isPromotion()) {
      property = FareProjectMapping_.PROMOTION_PLAN + SORT_CONJ + FarePlan_.CODE;
      mainSort = Sort.by(direction, property);

    } else if (sortBy.isBasicFarePlan()) {
      property = FareProjectMapping_.BASIC_FARE_PLAN + SORT_CONJ + FarePlan_.CODE;
      mainSort = Sort.by(direction, property);

    } else if (sortBy.isProfileDiscountPlan()) {
      property = FareProjectMapping_.PROFILE_DISCOUNT_PLAN + SORT_CONJ + ProfileDiscountPlan_.CODE;
      mainSort = Sort.by(direction, property);

    } else if (sortBy.isPromotionServiceType()) {
      property = FareProjectMapping_.PROMOTION_PLAN + SORT_CONJ + PromotionPlan_.SERVICE_TYPE;
      mainSort = Sort.by(direction, property);

    } else if (sortBy.isProject()) {
      mainSort = Sort.by(Arrays.asList(new Order(direction, projectCodeNumber),
          new Order(direction, projectCodeType)));
      defaultSort = null;
    }

    if (Objects.nonNull(mainSort)) {
      // 專案排序
      if (Objects.isNull(defaultSort)) {
        return mainSort;
      } else {
        return mainSort.and(defaultSort);
      }
    } else {
      return defaultSort;
    }
  }

  @Override
  protected Predicate translate(ComparisonExpression<FareProjectMappingFilter> expression,
      Root<FareProjectMapping> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {

    ComparisonOperator op = expression.getOperator();

    if (expression.getAttributeValue().getPromotionCode() != null) {
      // PromotionPlan.code use super class FarePlan static access
      Path<String> path = root.join(FareProjectMapping_.PROMOTION_PLAN).get(FarePlan_.CODE);
      String value = expression.getAttributeValue().getPromotionCode();
      return translate(path, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getBasicFarePlanCode() != null) {
      // PromotionPlan.code use super class FarePlan static access
      Path<String> path = root.join(FareProjectMapping_.BASIC_FARE_PLAN).get(FarePlan_.CODE);
      String value = expression.getAttributeValue().getBasicFarePlanCode();
      return translate(path, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getProjectCode() != null) {
      Path<String> path = root.join(FareProjectMapping_.FARE_PROJECT).get(FareProject_.CODE);
      String value = expression.getAttributeValue().getProjectCode();
      return translate(path, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getPromotionServiceType() != null) {
      Path<PromotionServiceType> path = root.join(FareProjectMapping_.PROMOTION_PLAN)
          .get(PromotionPlan_.SERVICE_TYPE);
      PromotionServiceType value = expression.getAttributeValue().getPromotionServiceType();
      return translate(path, op, value, criteriaBuilder);

    } else {
      return null;
    }
  }

  @Override
  protected Function<Map<String, Boolean>, FareProjectMappingSort> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, FareProjectMappingSort.class);

  }

  @Override
  protected Function<Map<String, Object>, FareProjectMappingFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, FareProjectMappingFilter.class);
  }

  @Override
  public List<FareProjectMappingOutput> getProjectMappings() {
    return repo.findAll().stream().map(this::translate).collect(Collectors.toList());
  }
}
