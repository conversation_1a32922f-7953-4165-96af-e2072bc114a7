/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketProductOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.TicketProduct;
import com.ibm.tw.thsrc.bsm.fare.entity.TicketProduct_;
import com.ibm.tw.thsrc.bsm.fare.repository.TicketProductRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.TicketProductFilter;
import com.ibm.tw.thsrc.bsm.fare.vo.TicketProductSort;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.TypedSort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class TicketProductQueryService extends
    AbstractQueryService<TicketProduct, TicketProductOutput, TicketProductFilter, TicketProductSort> {

  @Autowired
  private ObjectMapper objectMapper;
  @Autowired
  private TicketProductRepository repo;

  @Override
  public TicketProductOutput read(Long id) {
    throw new UnsupportedOperationException();
  }

  public Set<TicketProductOutput> read(Set<Long> ids) {
    return repo.findAllById(ids).stream().map(this::translate).collect(Collectors.toSet());
  }

  @Override
  public Page<TicketProductOutput> search(Search searchRequest) {
    PageRequest pr = translateToPageRequest(searchRequest);
    Specification<TicketProduct> spec = translateToSpecification(searchRequest);
    Page<TicketProduct> page = repo.findAll(spec, pr);
    return page.map(this::translate);
  }

  @Override
  protected TicketProductOutput translate(TicketProduct entity) {
    TicketProductOutput response = new TicketProductOutput();

    response.setDataVersion(entity.getDataVersion());
    response.setId(entity.getId());
    response.setTicketId(entity.getTicketId());
    response.setEffDate(entity.getEffDate());
    response.setEnName(entity.getEnName());
    response.setZhName(entity.getZhName());
    response.setEnPrintName(entity.getEnPrintName());
    response.setZhPrintName(entity.getZhPrintName());

    return response;
  }

  @Override
  protected Sort translate(TicketProductSort sortBy, Direction direction) {
    TypedSort<TicketProduct> typedSort = Sort.sort(TicketProduct.class);

    if (sortBy.isTicketId()) {
      typedSort//
          .by(TicketProduct::getTicketId);
    } else if (sortBy.isEnName()) {
      typedSort//
          .by(TicketProduct::getEnName);
    } else if (sortBy.isEnPrintName()) {
      typedSort//
          .by(TicketProduct::getEnPrintName);
    } else if (sortBy.isZhName()) {
      typedSort//
          .by(TicketProduct::getZhName);
    } else if (sortBy.isZhPrintName()) {
      typedSort//
          .by(TicketProduct::getZhPrintName);
    }

    if (typedSort.isSorted()) {
      if (Sort.Direction.DESC == direction) {
        return typedSort.descending();
      } else {
        return typedSort.ascending();
      }
    } else {
      return typedSort;
    }
  }

  @Override
  protected Predicate translate(ComparisonExpression<TicketProductFilter> expression,
      Root<TicketProduct> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    ComparisonOperator op = expression.getOperator();

    if (expression.getAttributeValue().getIsFuture() != null) {
      Path<LocalDate> path = root.get(TicketProduct_.EFF_DATE);
      boolean isFuture = expression.getAttributeValue().getIsFuture();
      LocalDate value = LocalDate.now();

      if (isFuture) {
        op = ComparisonOperator.GT;

      } else {
        op = ComparisonOperator.EQ;
        Optional<TicketProduct> newest = repo.findAll().stream()
            .filter(e -> !e.getEffDate().isAfter(LocalDate.now()))
            .max(Comparator.comparing(TicketProduct::getEffDate));
        if (newest.isPresent()) {
          value = newest.get().getEffDate();
        }
      }
      return translate(path, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getTicketId() != null) {

      Path<Long> path = root.get(TicketProduct_.TICKET_ID);
      Long value = expression.getAttributeValue().getTicketId();
      return translate(path, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getEnName() != null) {

      Path<String> path = root.get(TicketProduct_.EN_NAME);
      String value = expression.getAttributeValue().getEnName();
      return translate(path, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getEnPrintName() != null) {

      Path<String> path = root.get(TicketProduct_.EN_PRINT_NAME);
      String value = expression.getAttributeValue().getEnPrintName();
      return translate(path, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getZhName() != null) {

      Path<String> path = root.get(TicketProduct_.ZH_NAME);
      String value = expression.getAttributeValue().getZhName();
      return translate(path, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getZhPrintName() != null) {

      Path<String> path = root.get(TicketProduct_.ZH_PRINT_NAME);
      String value = expression.getAttributeValue().getZhPrintName();
      return translate(path, op, value, criteriaBuilder);

    } else {
      return null;
    }
  }

  @Override
  protected Function<Map<String, Boolean>, TicketProductSort> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, TicketProductSort.class);
  }

  @Override
  protected Function<Map<String, Object>, TicketProductFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, TicketProductFilter.class);
  }
}
