/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.event.ExceptionMessages;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.fa.api.FareProjectApi;
import com.ibm.tw.thsrc.bsm.fa.dto.FareProjectInput;
import com.ibm.tw.thsrc.bsm.fa.dto.FareProjectOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.ProjectOverviewOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.ProjectSalesChannelFlagSettingInput;
import com.ibm.tw.thsrc.bsm.fare.service.FareProjectCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.FareProjectQueryService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/FareProjects")
public class FareProjectController implements FareProjectApi {

  @Autowired
  private FareProjectCommandService commandService;

  @Autowired
  private FareProjectQueryService queryService;

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private EventStore eventStore;

  @Autowired
  private ExceptionMessages exceptionMessages;

  @Override
  public Page<FareProjectOutput> search(Search search) {
    return queryService.search(search);
  }

  @Override
  public FareProjectOutput create(FareProjectInput input) {
    eventStore.validateFunctionLock(OperationFunction.FARE_PROJECT);
    Long id = commandService.create(input);
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
    return queryService.read(id);
  }

  @Override
  public void delete(Long id) {
    eventStore.validateFunctionLock(OperationFunction.FARE_PROJECT);
    commandService.delete(id);
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
  }

  @Override
  public FareProjectOutput replace(Long id, FareProjectInput input) {
    eventStore.validateFunctionLock(OperationFunction.FARE_PROJECT);
    commandService.update(id, input);
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
    return queryService.read(id);
  }

  @Override
  public FareProjectOutput read(Long id) {
    return queryService.read(id);
  }

  @Override
  public List<ProjectOverviewOutput> validate() {
    return queryService.validate();
  }

  @Override
  public FareProjectOutput replaceSalesFlagSettings(Long id,
      ProjectSalesChannelFlagSettingInput input) {
    commandService.upsertSalesChannelFlagSettings(id, input);
    return queryService.read(id);
  }

  @Override
  public void deleteSalesFlagSettings(Long id) {
    commandService.deleteSalesChannelFlagSettings(id);
  }
}
