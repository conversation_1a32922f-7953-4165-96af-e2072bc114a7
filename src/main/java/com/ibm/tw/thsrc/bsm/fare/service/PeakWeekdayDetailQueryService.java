/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.fa.dto.PeakWeekdayOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.WeekdayPeakTimeOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.PeakTime;
import com.ibm.tw.thsrc.bsm.fare.entity.PeakTimeName;
import com.ibm.tw.thsrc.bsm.fare.entity.PeakWeekdayDetail;
import com.ibm.tw.thsrc.bsm.fare.entity.PeakWeekdayDetail_;
import com.ibm.tw.thsrc.bsm.fare.repository.PeakWeekdayDetailRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.PeakWeekdayDetailFilter;
import com.ibm.tw.thsrc.bsm.fare.vo.PeakWeekdayDetailSort;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class PeakWeekdayDetailQueryService extends
    AbstractQueryService<PeakWeekdayDetail, PeakWeekdayOutput, PeakWeekdayDetailFilter, PeakWeekdayDetailSort> {

  @Autowired
  private PeakWeekdayDetailRepository repo;

  @Autowired
  private ObjectMapper objectMapper;

  @Override
  public PeakWeekdayOutput read(Long id) {
    throw new UnsupportedOperationException();
  }

  public List<PeakWeekdayOutput> read(Set<Long> ids) {
    return repo.findAllById(ids).stream().map(this::translate).collect(Collectors.toList());
  }

  @Override
  public Page<PeakWeekdayOutput> search(Search search) {
    PageRequest pr = translateToPageRequest(search);
    Specification<PeakWeekdayDetail> spec = translateToSpecification(search);
    Page<PeakWeekdayDetail> page = repo.findAll(spec, pr);

    return page.map(this::translate);
  }

  @Override
  protected PeakWeekdayOutput translate(PeakWeekdayDetail entity) {
    PeakWeekdayOutput response = new PeakWeekdayOutput();
    response.setEffDate(entity.getEffDate());
    response.setPeakWeekdayDetailId(entity.getId());
    response.setPeakWeekdayId(entity.getPeakWeekday().getId());
    response.setDayOfWeek(entity.getPeakWeekday().getDayOfWeek());
    response.setDataVersion(entity.getPeakWeekday().getDataVersion());

    WeekdayPeakTimeOutput peakTimeOutput = new WeekdayPeakTimeOutput();
    peakTimeOutput.setId(entity.getPeakTime().getId());
    peakTimeOutput.setCode(entity.getPeakTime().getCode());
    PeakTimeName nameEntity = pickRecentVersion(entity.getPeakTime());
    peakTimeOutput.setName(nameEntity.getName());
    response.setPeakTime(peakTimeOutput);

    return response;
  }

  private PeakTimeName pickRecentVersion(PeakTime peakTime) {
    int nameLength = peakTime.getNames().size();
    PeakTimeName nameEntity = peakTime.getNames().get(nameLength - 1);
    if (nameEntity.getEffDate().compareTo(LocalDate.now()) > 0) {
      nameEntity = peakTime.getNames().get(nameLength - 2);
    }

    return nameEntity;
  }

  @Override
  protected Predicate translate(ComparisonExpression<PeakWeekdayDetailFilter> expression,
      Root<PeakWeekdayDetail> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    if (expression.getAttributeValue().getIsFuture() != null) {
      Path<LocalDate> path = root.get(PeakWeekdayDetail_.EFF_DATE);
      boolean isFuture = expression.getAttributeValue().getIsFuture();
      ComparisonOperator op;
      LocalDate value = LocalDate.now();

      if (isFuture) {
        op = ComparisonOperator.GT;

      } else {
        op = ComparisonOperator.EQ;
        Optional<PeakWeekdayDetail> newest = repo.findAll().stream()
            .filter(e -> !e.getEffDate().isAfter(LocalDate.now()))
            .max(Comparator.comparing(PeakWeekdayDetail::getEffDate));
        if (newest.isPresent()) {
          value = newest.get().getEffDate();
        }
      }
      return translate(path, op, value, criteriaBuilder);

    } else {
      return null;
    }
  }

  @Override
  protected Sort translate(PeakWeekdayDetailSort sortBy, Direction sortDirection) {
    return Sort.unsorted();
  }

  @Override
  protected Function<Map<String, Boolean>, PeakWeekdayDetailSort> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, PeakWeekdayDetailSort.class);
  }

  @Override
  protected Function<Map<String, Object>, PeakWeekdayDetailFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, PeakWeekdayDetailFilter.class);
  }
}
