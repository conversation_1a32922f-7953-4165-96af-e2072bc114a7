/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fa.enums.ElectronicMoneyType;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCard;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface CoBrandedCardRepository extends JpaRepository<CoBrandedCard, Long>,
    JpaSpecificationExecutor<CoBrandedCard> {

  Optional<CoBrandedCard> findByElectronicMoneyType(ElectronicMoneyType electronicMoneyType);
}
