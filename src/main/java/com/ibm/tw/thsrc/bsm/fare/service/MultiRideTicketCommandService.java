/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.MultiRideTicketInput;
import com.ibm.tw.thsrc.bsm.fare.domain.event.MultiRideTicketReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.MultiRideTicket;
import com.ibm.tw.thsrc.bsm.fare.entity.MultiRideTicketOption;
import com.ibm.tw.thsrc.bsm.fare.repository.MultiRideTicketRepository;
import com.ibm.tw.thsrc.bsm.message.fare.MultiRideTicketData;
import com.ibm.tw.thsrc.bsm.message.fare.MultiRideTicketOptionData;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 回數票 Command Service
 */
@Transactional
@Service
public class MultiRideTicketCommandService
    extends AbstractCommandService<MultiRideTicket, MultiRideTicketInput> {

  private final Logger log = LoggerFactory.getLogger(this.getClass());

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Autowired
  MultiRideTicketRepository repo;

  @Override
  public Long create(MultiRideTicketInput input) {
    throw new UnsupportedOperationException("create not supported");
  }

  @Override
  public void update(Long id, MultiRideTicketInput input) {
    MultiRideTicket entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    this.validateConcurrency(entity, input.getDataVersion());
    this.writeAggregateRootValue(entity, input);
    this.validateInvariants(entity);
    repo.save(entity);

    // 發布MEIG事件
    MultiRideTicketReplaced event =
        new MultiRideTicketReplaced(interceptor.getUserId(), interceptor.getCorrelationId());
    MultiRideTicketData msgPayload = this.toMsgPayload(entity);
    event.setMsgPayload(msgPayload);
    applicationEventPublisher.publishEvent(event);

    log.info("update - end");
  }

  @Override
  public void delete(Long id) {
    throw new UnsupportedOperationException("delete not supported");
  }

  @Override
  protected void validateInvariants(MultiRideTicket aggregateRoot) throws UnprocessableException {
    aggregateRoot.getMultiRideTicketOptions().forEach(option -> {
      if (Boolean.TRUE.equals(option.getIsAppSale())
          && (option.getAppDscSaleDate() == null || option.getAppEffSaleDate() == null)) {
        throw FareErrorCode.FA_TICKET_OPTION_INVALID_APP_SALE_DATE.toException();
      }
    });
  }

  @Override
  protected void writeAggregateRootValue(MultiRideTicket aggregateRoot,
      MultiRideTicketInput input) {

    aggregateRoot.setRefundFeeAmt(input.getRefundFeeAmt());
    aggregateRoot.setExpiryType(input.getExpiryType());

    List<MultiRideTicketOption> options = new ArrayList<>();
    input.getOptions().stream().forEach(o -> {
      MultiRideTicketOption option = new MultiRideTicketOption();
      // fix : all required fields should exist
      if (o.getValidDays() != null &&
          o.getRides() != null &&
          o.getDiscountPct() != null &&
          o.getIsAppSale() != null) {
        option.setRides(o.getRides());
        option.setDiscountPct(o.getDiscountPct());
        option.setIsAppSale(o.getIsAppSale());
        option.setAppEffSaleDate(o.getAppEffSaleDate());
        option.setAppDscSaleDate(o.getAppDscSaleDate());
        option.setValidDays(o.getValidDays());
      }
      option.setMultiRideTicket(aggregateRoot);
      options.add(option);
    });

    if (aggregateRoot.getMultiRideTicketOptions() != null) {
      aggregateRoot.getMultiRideTicketOptions().clear();
      aggregateRoot.getMultiRideTicketOptions().addAll(options);
    } else {
      aggregateRoot.setMultiRideTicketOptions(options);
    }
  }

  private MultiRideTicketData toMsgPayload(MultiRideTicket entity) {
    MultiRideTicketData msgPayload = new MultiRideTicketData();
    msgPayload.setExpiryType(entity.getExpiryType());
    msgPayload.setRefundFeeAmt(entity.getRefundFeeAmt());

    List<MultiRideTicketOptionData> options = new ArrayList<>();
    entity.getMultiRideTicketOptions().stream().forEach(o -> {
      if (o.getValidDays() != null &&
          o.getRides() != null &&
          o.getDiscountPct() != null &&
          o.getIsAppSale() != null) {
        MultiRideTicketOptionData option = new MultiRideTicketOptionData();
        option.setRides(o.getRides());
        option.setDiscountPct(o.getDiscountPct());
        option.setIsAppSale(o.getIsAppSale());
        option.setAppEffSaleDate(o.getAppEffSaleDate());
        option.setAppDscSaleDate(o.getAppDscSaleDate());
        option.setValidDays(o.getValidDays());
        options.add(option);
      }
    });
    msgPayload.setOptions(options);

    return msgPayload;
  }

}
