/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.core.repository.AbstractResRepositoryDecorator;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.fare.entity.ReissueTicketRefundFee;
import com.ibm.tw.thsrc.bsm.message.fare.ReissueTicketRefundFeeData;
import com.ibm.tw.thsrc.bsm.res.service.fa.ReissueTicketRefundFeeService;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public class ReissueTicketRefundFeeResRepositoryDecorator extends
    AbstractResRepositoryDecorator<ReissueTicketRefundFee, String> implements
    ReissueTicketRefundFeeRepository {

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private ReissueTicketRefundFeeService reissueTicketRefundFeeService;

  public ReissueTicketRefundFeeResRepositoryDecorator(
      JpaRepository<ReissueTicketRefundFee, Long> jpaRepositoryImpl,
      JpaSpecificationExecutor<ReissueTicketRefundFee> jpaSpecificationExecutorImpl) {
    super(jpaRepositoryImpl, jpaSpecificationExecutorImpl);
  }

  @Override
  public List<ReissueTicketRefundFee> findAll() {

    return this.synchronizeResData();
  }


  public List<ReissueTicketRefundFee> synchronizeResData() {
    List<ReissueTicketRefundFeeData> reissueTicketRefundFeeData = this.reissueTicketRefundFeeService.queryReissueTicket();

    List<ReissueTicketRefundFee> entities = reissueTicketRefundFeeData.stream()
        .map(this::reconstituteEntityFrom).collect(Collectors.toList());
    return this.jpaRepositoryImpl.saveAll(entities);
  }

  private ReissueTicketRefundFee reconstituteEntityFrom(ReissueTicketRefundFeeData dto) {
    ReissueTicketRefundFee entity = new ReissueTicketRefundFee();
    entity.setUserId(interceptor.getUserId());
    entity.setEffDate(dto.getEffDate());
    entity.setFeePct(dto.getFeePct());
    entity.setIsAdditionalCharges(dto.getIsAdditionalCharges());

    return entity;
  }
}
