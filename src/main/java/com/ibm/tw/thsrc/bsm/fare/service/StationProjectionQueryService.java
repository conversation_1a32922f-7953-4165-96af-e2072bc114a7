/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source
 * code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.dto.BaseStationOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.StationProjection;
import com.ibm.tw.thsrc.bsm.fare.repository.StationProjectionRepository;
import com.ibm.tw.thsrc.bsm.sc.enums.StationOperationalStatus;
import java.util.List;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class StationProjectionQueryService implements StationProjectionQueryApi {

  private static final Integer MAX_PASSENGER_STATION_ID = 16;

  @Autowired
  private StationProjectionRepository repo;

  private BaseStationOutput translate(StationProjection entity) {
    BaseStationOutput response = new BaseStationOutput();
    response.setId(entity.getId());
    response.setStationId(entity.getStationId());
    response.setCode(entity.getCode());
    response.setName(entity.getZhName());
    response.setDisplayOrder(entity.getDisplayOrder());
    response.setStatus(entity.getStationOperationalStatus());

    return response;
  }


  @Override
  public List<BaseStationOutput> getStations() {
    return repo.findByStationOperationalStatus(StationOperationalStatus.FULLY_OPERATIONAL).stream()
        .filter(p -> p.getStationId() <= MAX_PASSENGER_STATION_ID).map(this::translate)
        .collect(Collectors.toList());
  }

  @Override
  public List<BaseStationOutput> getAllStations() {
    return repo.findAll().stream().filter(p -> p.getStationId() <= MAX_PASSENGER_STATION_ID)
        .map(this::translate).collect(Collectors.toList());
  }
}
