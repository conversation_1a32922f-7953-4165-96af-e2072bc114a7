/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.GroupEligibleTrainDto;
import com.ibm.tw.thsrc.bsm.fa.dto.GroupPlanOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.PassengerLevelDto;
import com.ibm.tw.thsrc.bsm.fare.entity.GroupPlan;
import com.ibm.tw.thsrc.bsm.fare.entity.GroupPlan_;
import com.ibm.tw.thsrc.bsm.fare.repository.GroupPlanRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.GroupPlanFilter;
import com.ibm.tw.thsrc.bsm.fare.vo.GroupPlanSort;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.TypedSort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class GroupPlanQueryService extends AbstractQueryService<GroupPlan, GroupPlanOutput, GroupPlanFilter, GroupPlanSort> {

  @Autowired
  GroupPlanRepository repo;

  @Autowired
  private ObjectMapper objectMapper;

  @Override
  public GroupPlanOutput read(Long id) {
    return repo.findById(id)
        .map(this::translate)
        .orElseThrow(ResourceNotFoundException::new);
  }

  @Override
  public Page<GroupPlanOutput> search(Search search) {
    PageRequest pr = translateToPageRequest(search);
    Specification<GroupPlan> spec = translateToSpecification(search);
    return repo
        .findAll(spec, pr)
        .map(this::translate);
  }

  @Override
  protected GroupPlanOutput translate(GroupPlan aggregateRoot) {
    GroupPlanOutput output = new GroupPlanOutput();

    output.setId(aggregateRoot.getId());
    output.setDataVersion(aggregateRoot.getDataVersion());

    output.setPromotionCode(aggregateRoot.getPromotionPlan().getCode());
    output.setCode(aggregateRoot.getCode());
    output.setGroupPlanType(aggregateRoot.getGroupPlanType());
    output.setName(aggregateRoot.getName());
    output.setEffDate(aggregateRoot.getEffDate());
    output.setEffSaleDate(aggregateRoot.getEffSaleDate());
    output.setDscDate(aggregateRoot.getDscDate());

    List<PassengerLevelDto> passengerLevels = new ArrayList<>();
    aggregateRoot.getPassengerLevels().stream().forEach(p -> {

      PassengerLevelDto passengerLevelDto = new PassengerLevelDto();

      if (p.getGroupEligibleTrains() != null) {
        List<GroupEligibleTrainDto> groupEligibleTrains = new ArrayList<>();

        p.getGroupEligibleTrains().stream().forEach(g -> {

          GroupEligibleTrainDto groupEligibleTrainDto = new GroupEligibleTrainDto();

          groupEligibleTrainDto.setId(g.getId());
          groupEligibleTrainDto.setDataVersion(g.getDataVersion());
          groupEligibleTrainDto.setDayOfWeek(g.getDayOfWeek());
          groupEligibleTrainDto.setTrainNum(g.getTrainNum());

          groupEligibleTrains.add(groupEligibleTrainDto);
        });
        passengerLevelDto.setGroupEligibleTrains(groupEligibleTrains);
      }

      passengerLevelDto.setId(p.getId());
      passengerLevelDto.setDataVersion(p.getDataVersion());
      passengerLevelDto.setMinQty(p.getMinQty());
      passengerLevelDto.setMaxQty(p.getMaxQty());
      passengerLevelDto.setDiscountPct(p.getDiscountPct());


      passengerLevels.add(passengerLevelDto);
    });

    output.setPassengerLevels(passengerLevels);

    return output;
  }

  @Override
  protected Sort translate(GroupPlanSort sortBy, Direction sortDirection) {

    TypedSort<GroupPlan> typedSort = Sort.sort(GroupPlan.class);

    if (sortBy.isName()) {
      typedSort.by(GroupPlan::getName);
    } else if (sortBy.isEffDate()) {
      typedSort.by(GroupPlan::getEffDate);
    } else if (sortBy.isDscDate()) {
      typedSort.by(GroupPlan::getDscDate);
    } else if (sortBy.isEffSaleDate()) {
      typedSort.by(GroupPlan::getEffSaleDate);
    } else if (sortBy.isDscSaleDate()) {
      typedSort.by(GroupPlan::getDscDate);
    } else {
      typedSort.by(GroupPlan::getId);
    }

    if (typedSort.isSorted()) {
      if (Sort.Direction.DESC == sortDirection) {
        return typedSort.descending();
      } else {
        return typedSort.ascending();
      }
    } else {
      return typedSort;
    }
  }

  @Override
  protected Predicate translate(ComparisonExpression<GroupPlanFilter> expression, Root<GroupPlan> root,
      CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {

    ComparisonOperator op = expression.getOperator();

    if (expression.getAttributeValue().getName() != null) {
      Path<String> namePath = root.get(GroupPlan_.NAME);
      return translate(namePath, op, expression.getAttributeValue().getName(), criteriaBuilder);
    } else if (expression.getAttributeValue().getStartTrainDate() != null) {
      Path<LocalDate> effDatePath = root.get(GroupPlan_.EFF_DATE);
      return translate(effDatePath, op, expression.getAttributeValue().getStartTrainDate(), criteriaBuilder);
    } else if (expression.getAttributeValue().getEndTrainDate() != null) {
      Path<LocalDate> dscDatePath = root.get(GroupPlan_.DSC_DATE);
      return translate(dscDatePath, op, expression.getAttributeValue().getEndTrainDate(), criteriaBuilder);
    } else if (expression.getAttributeValue().getStartSaleDate() != null) {
      Path<LocalDate> effSaleDatePath = root.get(GroupPlan_.EFF_SALE_DATE);
      return translate(effSaleDatePath, op, expression.getAttributeValue().getStartSaleDate(), criteriaBuilder);
    } else if (expression.getAttributeValue().getEndSaleDate() != null) {
      Path<LocalDate> dscDatePath = root.get(GroupPlan_.DSC_DATE);
      return translate(dscDatePath, op, expression.getAttributeValue().getEndSaleDate(), criteriaBuilder);
    }
    return null;
  }

  @Override
  protected Function<Map<String, Boolean>, GroupPlanSort> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, GroupPlanSort.class);
  }

  @Override
  protected Function<Map<String, Object>, GroupPlanFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, GroupPlanFilter.class);
  }

}
