/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.exception.SharedErrorCode;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.CardIssuingBankInput;
import com.ibm.tw.thsrc.bsm.fare.entity.CardIssuingBank;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCard;
import com.ibm.tw.thsrc.bsm.fare.repository.CardIssuingBankRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardBankPromotionRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardRepository;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Transactional
@Service
public class CardIssuingBankCommandService extends
    AbstractCommandService<CardIssuingBank, CardIssuingBankInput> {

  @Autowired
  private CardIssuingBankRepository repo;

  @Autowired
  private CoBrandedCardRepository cardRepo;

  @Autowired
  private CoBrandedCardBankPromotionRepository bankPromotionRepo;

  @Override
  public Long create(CardIssuingBankInput input) {
    CardIssuingBank newEntity = new CardIssuingBank();
    writeAggregateRootValue(newEntity, input);
    validateInvariants(newEntity);
    CardIssuingBank entity = repo.save(newEntity);

    return entity.getId();
  }

  @Override
  public void update(Long id, CardIssuingBankInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void delete(Long id) {
    CardIssuingBank entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    if (!bankPromotionRepo.findByBank(entity).isEmpty()) {
      throw FareErrorCode.FA_CARD_ISSUING_BANK_CANNOT_BE_DELETED_WHEN_IS_USED_BY_CO_BRANDED_CARD_PROMOTION.toException();
    }

    repo.delete(entity);
  }

  public Set<Long> patch(CollectionModificationInput<CardIssuingBankInput> changeSet) {
    changeSet.getDeletions().forEach(e -> {
      CardIssuingBank entity = repo.findById(e)
          .orElseThrow(() ->
              SharedErrorCode.COLLECTION_PATCH_INPUT_ID_NOT_EXIST.toException(String.valueOf(e)));
      if (!bankPromotionRepo.findByBank(entity).isEmpty()) {
        throw FareErrorCode.FA_CARD_ISSUING_BANK_CANNOT_BE_DELETED_WHEN_IS_USED_BY_CO_BRANDED_CARD_PROMOTION.toException();
      }

      repo.delete(entity);
    });

    return new HashSet<>();
  }

  @Override
  protected void validateInvariants(CardIssuingBank entity) throws UnprocessableException {
    if (Objects.isNull(entity.getId()) && !repo.findByCodeAndCoBrandedCard(entity.getCode(),
        entity.getCoBrandedCard()).isEmpty()) {
      throw FareErrorCode.FA_CARD_ISSUING_BANK_HAS_DUPLICATE_BANK_CODE.toException(
          entity.getCode());
    }

    if (!repo.findByNameAndCoBrandedCard_Id(entity.getName(), entity.getCoBrandedCard().getId())
        .isEmpty()) {
      throw FareErrorCode.FA_CARD_ISSUING_BANK_HAS_DUPLICATE_BANK_NAME.toException();
    }
  }

  @Override
  protected void writeAggregateRootValue(CardIssuingBank entity, CardIssuingBankInput input) {
    CoBrandedCard card = cardRepo.findById(input.getCoBrandedCardId()).orElseThrow(() ->
        SharedErrorCode.CARD_ISSUING_BANK_CANNOT_ASSOCIATE_TO_THE_CARD.toException(
            String.valueOf(input.getCoBrandedCardId())));
    entity.setCoBrandedCard(card);
    entity.setCode(input.getCode());
    entity.setName(input.getName());
  }

}
