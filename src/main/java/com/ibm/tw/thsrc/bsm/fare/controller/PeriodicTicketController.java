/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.fa.api.PeriodicTicketApi;
import com.ibm.tw.thsrc.bsm.fa.dto.PeriodicTicketInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PeriodicTicketOutput;
import com.ibm.tw.thsrc.bsm.fare.service.PeriodicTicketCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.PeriodicTicketQueryService;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 定期票 Controller
 */
@RestController
@RequestMapping(value = "/PeriodicTickets")
public class PeriodicTicketController implements PeriodicTicketApi {

  @Autowired
  PeriodicTicketCommandService commandService;

  @Autowired
  PeriodicTicketQueryService queryService;

  @Autowired
  private EventStore eventStore;

  @Override
  public Page<PeriodicTicketOutput> search(@Valid Search search) {
    return queryService.search(search);
  }

  @Override
  public PeriodicTicketOutput read(Long id) {
    return queryService.read(id);
  }

  @Override
  public PeriodicTicketOutput replace(Long id, @Valid PeriodicTicketInput input) {
    eventStore.validateFunctionLock(OperationFunction.PERIODIC_TICKET);
    commandService.update(id, input);
    return queryService.read(id);
  }

}
