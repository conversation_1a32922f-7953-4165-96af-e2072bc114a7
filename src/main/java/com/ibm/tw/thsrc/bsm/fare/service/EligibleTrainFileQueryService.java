/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainFileOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainFile;
import com.ibm.tw.thsrc.bsm.fare.repository.EligibleTrainFileRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.EligibleTrainFileSort;
import java.util.Map;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.TypedSort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class EligibleTrainFileQueryService extends
    AbstractQueryService<EligibleTrainFile, EligibleTrainFileOutput, Void, EligibleTrainFileSort> {

  @Autowired
  private EligibleTrainFileRepository repo;

  @Autowired
  private ObjectMapper objectMapper;

  @Override
  public EligibleTrainFileOutput read(Long id) {
    EligibleTrainFile entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    return translate(entity);
  }

  @Override
  public Page<EligibleTrainFileOutput> search(Search searchRequest) {
    PageRequest pr = translateToPageRequest(searchRequest);
    Specification<EligibleTrainFile> spec = translateToSpecification(searchRequest);
    Page<EligibleTrainFile> page = repo.findAll(spec, pr);
    return page.map(this::translate);
  }

  @Override
  protected EligibleTrainFileOutput translate(EligibleTrainFile entity) {
    EligibleTrainFileOutput response = new EligibleTrainFileOutput();
    response.setId(entity.getId());
    response.setFileName(entity.getFileName());
    response.setFilePath(entity.getFilePath());
    response.setUploadTimestamp(entity.getUploadTimestamp());
    response.setUploadEmployeeId(entity.getUploadEmployeeId());
    response.setLatestDownloadTimestamp(entity.getLastDownloadTimestamp());
    response.setLatestDownloadEmployeeId(entity.getLastDownloadEmployeeId());
    return response;
  }

  @Override
  protected Sort translate(EligibleTrainFileSort sortBy, Direction direction) {

    TypedSort<EligibleTrainFile> typedSort = Sort.sort(EligibleTrainFile.class);

    if (sortBy.isFileName()) {
      typedSort//
          .by(EligibleTrainFile::getFileName);
    } else if (sortBy.isFilePath()) {
      typedSort//
          .by(EligibleTrainFile::getFilePath);
    } else if (sortBy.isUploadTimestamp()) {
      typedSort//
          .by(EligibleTrainFile::getUploadTimestamp);
    } else if (sortBy.isUploadEmployeeId()) {
      typedSort//
          .by(EligibleTrainFile::getUploadEmployeeId);
    } else if (sortBy.isLastDownloadTimestamp()) {
      typedSort//
          .by(EligibleTrainFile::getLastDownloadTimestamp);
    } else if (sortBy.isLastDownloadEmployeeId()) {
      typedSort//
          .by(EligibleTrainFile::getLastDownloadEmployeeId);
    }

    if (typedSort.isSorted()) {
      if (Sort.Direction.DESC == direction) {
        return typedSort.descending();
      } else {
        return typedSort.ascending();
      }
    } else {
      return typedSort;
    }
  }

  @Override
  protected Predicate translate(ComparisonExpression<Void> f, Root<EligibleTrainFile> root,
      CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    return null;
  }

  @Override
  protected Function<Map<String, Boolean>, EligibleTrainFileSort> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, EligibleTrainFileSort.class);
  }

  @Override
  protected Function<Map<String, Object>, Void> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, Void.class);
  }

}
