/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.EmailService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.cronjob.enums.FutureVersionControlStatus;
import com.ibm.tw.thsrc.bsm.cronjob.service.impl.FutureVersionControlService;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.exception.SharedErrorCode;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainInput;
import com.ibm.tw.thsrc.bsm.fare.domain.event.EligibleTrainCreated;
import com.ibm.tw.thsrc.bsm.fare.domain.event.EligibleTrainCronCreated;
import com.ibm.tw.thsrc.bsm.fare.domain.event.EligibleTrainDeleted;
import com.ibm.tw.thsrc.bsm.fare.domain.event.EligibleTrainReplaced;
import com.ibm.tw.thsrc.bsm.fare.domain.event.EligibleTrainUrgentCreated;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrain;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainDetail;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainDetailTrain;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainFutureVersionControl;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainTemp;
import com.ibm.tw.thsrc.bsm.fare.entity.FarePlan;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject;
import com.ibm.tw.thsrc.bsm.fare.repository.EligibleTrainFutureVersionControlRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.EligibleTrainRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.EligibleTrainTempRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.FarePlanRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.FareProjectRepository;
import com.ibm.tw.thsrc.bsm.message.fare.EligibleTrainData;
import com.ibm.tw.thsrc.bsm.message.fare.EligibleTrainDetailData;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class EligibleTrainCommandService extends
    AbstractCommandService<EligibleTrain, EligibleTrainInput> {

  public static final String JOB_BSM_ELIGIBLE_TRAIN = "eligibleTrainJob";
  private static final LocalTime CRON_JOB_EXEC_TIME = LocalTime.of(23, 50);
  private static final String EXCEPTION_SEPARATION = ",";
  private static final String RESULT_MAIL_SUBJECT = "BSM 操作適用車次結果通知";
  private static final String CREATE_MAIL_CONTENT = "適用車次新增結果：\n票價方案「%s (%s)」於發車日 %s 至 %s 的適用車次已新增完成";
  private static final String UPDATE_MAIL_CONTENT = "適用車次修改結果：\n票價方案「%s (%s)」於發車日 %s 至 %s 的適用車次已修改完成";
  private static final String DELETE_MAIL_CONTENT = "適用車次刪除結果：\n票價方案「%s (%s)」於發車日 %s 至 %s 的適用車次已刪除完成";
  private static final String CRON_JOB_MAIL_CONTENT = "適用車次排程執行結果：\n票價方案「%s」於開售日 %s 的適用車次已完成排程";

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private EligibleTrainRepository repo;
  @Autowired
  private FareProjectRepository fareProjectRepo;
  @Autowired
  private FarePlanRepository farePlanRepo;
  @Autowired
  private EligibleTrainFutureVersionControlRepository futureVersionRepo;
  @Autowired
  private EligibleTrainTempRepository tempRepo;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Autowired
  private FutureVersionControlService<EligibleTrainFutureVersionControl> futureVersionControlService;
  @Autowired
  private EmailService emailService;

  @Override
  public Long create(EligibleTrainInput input) {
    EligibleTrain newEntity = new EligibleTrain();
    EligibleTrain savedEntity = create(newEntity, input);

    notifyOnlineUser(CREATE_MAIL_CONTENT, savedEntity, interceptor.getUserEmail());
    return savedEntity.getId();
  }

  public EligibleTrain create(EligibleTrain newEntity, EligibleTrainInput input) {

    this.writeAggregateRootValue(newEntity, input);

    EligibleTrain savedEntity;

    boolean isNotOverlappingOrNonExistentData = this.getOverlappingEligibleTrains(newEntity)
        .isEmpty();

    if (input.getEffDate().isAfter(LocalDate.now())) {
      this.validateInvariants(newEntity);
    }

    if (input.getEffDate().isAfter(LocalDate.now()) && Boolean.FALSE.equals(
        isNotOverlappingOrNonExistentData)) {

      if (Objects.nonNull(newEntity.getProject())) {
        EligibleTrainData msg = this.translatePubMsg(newEntity);
        this.publishCreatedEvent(msg);
      }

      savedEntity = this.repo.save(newEntity);

      if (!hasFutureVersionControlStep(newEntity)) {
        this.createFutureVersionControl(savedEntity);
      }

      // 當天開售或發車日期區間未重疊之非延續性專案(寫入前就檢核過，只會有未來跟現在日期)
    } else {
      this.validateUrgent(newEntity);
      newEntity.setTgExecutionTime(LocalDateTime.now());
      savedEntity = this.repo.save(newEntity);
      this.publishUrgentCreatedEvent(savedEntity);
    }

    return savedEntity;
  }

  @Override
  public void update(Long id, EligibleTrainInput input) {
    EligibleTrain entity = this.repo.findById(id).orElseThrow(ResourceNotFoundException::new);

    // 有疏運期的情況下，不可更改開售日
    List<EligibleTrain> sameVersion = this.repo.findByFarePlanAndEffSaleDateIsAndIsAllTrainEligibleFalse(
        entity.getFarePlan(), entity.getEffSaleDate());
    if (sameVersion.size() > 1 && !entity.getEffSaleDate().isEqual(input.getEffSaleDate())) {
      throw FareErrorCode.FA_ELIGIBLE_TRAIN_CANNOT_REPLACE_EFF_SALE_DATE_WHEN_HAS_OTHER_ELIGIBLE_TRAIN_WITH_SAME_EFF_SALE_DATE.toException(
          entity.getFarePlan().getCode(), entity.getEffSaleDate().toString());
    }

    this.update(entity, input);
    this.repo.save(entity);

    notifyOnlineUser(UPDATE_MAIL_CONTENT, entity, interceptor.getUserEmail());
  }

  public void update(EligibleTrain entity, EligibleTrainInput input) {
    // 已下發 TG Table 參數，日期區間不能重疊，若有重疊需求，需刪除後在新增待排程資料
    List<EligibleTrain> overlappingData = this.getOverlappingEligibleTrains(entity, input).stream()
        .filter(d -> Objects.nonNull(d.getTgExecutionTime())).collect(Collectors.toList());
    if (!overlappingData.isEmpty() && Objects.nonNull(entity.getTgExecutionTime())) {
      throw FareErrorCode.FA_ELIGIBLE_TRAIN_CANNOT_REPLACE_THAT_EFF_DATE_OR_DSC_DATE_HAS_OVERLAPPING_WITHOUT_FUTURE_CRON_JOB.toException(
          entity.getFarePlan().getCode(), entity.getEffDate().toString(),
          entity.getDscDate().toString());
    }

    EligibleTrain copied = deepCopy(entity);
    EligibleTrainData beforeData = translatePubMsg(copied);

    this.validateConcurrency(entity, input.getDataVersion());
    // 已經有排程在執行的不能修改
    this.validateFutureVersionControlStepRunning(entity);
    this.writeAggregateRootValue(entity, input);
    this.validateInvariants(entity);

    EligibleTrainData afterData = this.translatePubMsg(entity);
    boolean hasRequiredExecutionToTg = (Objects.nonNull(entity.getTgExecutionTime())
        || overlappingData.isEmpty());
    afterData.setIsTgModificationRequired(hasRequiredExecutionToTg);

    if (Objects.nonNull(entity.getProject())) {
      this.publishReplacedEvent(Pair.of(beforeData, afterData));
    }

    this.deleteFutureVersionControl(copied);

    if (!hasRequiredExecutionToTg) {
      this.createFutureVersionControl(entity);
    } else {
      entity.setTgExecutionTime(LocalDateTime.now());
    }
  }

  @Override
  public void delete(Long id) {
    EligibleTrain entity = this.repo.findById(id).orElseThrow(ResourceNotFoundException::new);

    this.delete(entity);
    this.repo.delete(entity);

    notifyOnlineUser(DELETE_MAIL_CONTENT, entity, interceptor.getUserEmail());

    if (Objects.nonNull(entity.getProject())) {
      this.reorderPriorityForOtherEligibleTrain();
    }
  }

  public void delete(EligibleTrain entity) {

    if (!entity.getEffSaleDate().isAfter(LocalDate.now())) {
      throw FareErrorCode.FA_ELIGIBLE_TRAIN_CANNOT_CREATE_UPDATE_DELETE_THE_ACTIVE_ELIGIBLE_TRAIN.toException();
    }

    validateFutureVersionControlStepRunning(entity);
    EligibleTrainData msg = translatePubMsg(entity);

    // True 為已下發需要刪除 TG 資料, False 則否
    msg.setIsTgModificationRequired(Objects.nonNull(entity.getTgExecutionTime()));

    this.publishDeletedEvent(msg);

    //  若沒有其他疏運期(同開售日)->才可刪除排程
    List<EligibleTrain> sameVersion = repo.findByFarePlanAndEffSaleDateIsAndIsAllTrainEligibleFalse(
        entity.getFarePlan(), entity.getEffSaleDate());
    if (sameVersion.size() < 2 && Objects.isNull(entity.getTgExecutionTime())) {
      deleteFutureVersionControl(entity);
    }
  }

  public void cronCreate(Long id) {
    EligibleTrainTemp temp = tempRepo.findById(id).orElseThrow(ResourceNotFoundException::new);

    LocalDateTime tgExecutionTime = LocalDateTime.now();

    EligibleTrainCronCreated cronEvent = new EligibleTrainCronCreated(temp.getCreateUser(),
        temp.getCorrelationId());
    cronEvent.setFarePlan(temp.getFarePlanCode());
    cronEvent.setEffSaleDate(temp.getEffSaleDate());
    applicationEventPublisher.publishEvent(cronEvent);

    List<EligibleTrain> executingEntities = this.repo.findByFarePlan_CodeAndEffSaleDateIsAndIsAllTrainEligibleFalse(
            temp.getFarePlanCode(), temp.getEffSaleDate()).stream()
        .filter(e -> Objects.isNull(e.getTgExecutionTime())).collect(Collectors.toList());

    executingEntities.forEach(e -> e.setTgExecutionTime(tgExecutionTime));

    this.repo.saveAll(executingEntities);

    notifyOnlineUser(CRON_JOB_MAIL_CONTENT, temp, temp.getCreateUserEmail());
  }

  @Override
  protected void validateInvariants(EligibleTrain entity) throws UnprocessableException {

    this.validateForSameProjectOrFarePlan(entity);

    // 有該票價方案的適用車次->疏運期（同開售日）
    List<EligibleTrain> sameVersions = repo.findByFarePlanAndEffSaleDateIsAndIsAllTrainEligibleFalse(
            entity.getFarePlan(), entity.getEffSaleDate()).stream()
        .filter(e -> Objects.isNull(entity.getId()) || !Objects.equals(e.getId(), entity.getId()))
        .collect(Collectors.toList());

    // 疏運期: 發車日期區間不能 overlap
    if (!sameVersions.isEmpty() && Boolean.FALSE.equals(entity.getIsAllTrainEligible())) {
      validateForSameEffSaleDate(sameVersions, entity);
    }
  }

  private void validateForSameProjectOrFarePlan(EligibleTrain entity) {
    // 專案不可有相同開售日與發車日區間設定
    if (Objects.nonNull(entity.getProject())) {
      List<EligibleTrain> sameDatePeriodProject = repo.findByProjectAndEffSaleDateAndEffDateAndDscDate(
          entity.getProject(), entity.getEffSaleDate(), entity.getEffDate(), entity.getDscDate());
      if ((Objects.isNull(entity.getId()) && !sameDatePeriodProject.isEmpty()) || (
          Objects.nonNull(entity.getId()) && !sameDatePeriodProject.isEmpty() && !Objects.equals(
              sameDatePeriodProject.get(0).getId(), entity.getId()))) {
        throw FareErrorCode.FA_ELIGIBLE_TRAIN_ALREADY_HAS_FARE_PROJECT_SETTING_IN_THE_SAME_DATE_PERIOD.toException(
            entity.getProject().getCode(), entity.getEffSaleDate().toString(),
            entity.getEffDate().toString(), entity.getDscDate().toString());
      }
    }

    // 票價方案不可有相同開售日與發車日區間設定
    List<EligibleTrain> sameDatePeriodFarePlan = repo.findByFarePlanAndEffSaleDateAndEffDateAndDscDateAndIsAllTrainEligibleFalse(
        entity.getFarePlan(), entity.getEffSaleDate(), entity.getEffDate(), entity.getDscDate());
    if ((Objects.isNull(entity.getId()) && !sameDatePeriodFarePlan.isEmpty()) || (
        Objects.nonNull(entity.getId()) && !sameDatePeriodFarePlan.isEmpty() && !Objects.equals(
            sameDatePeriodFarePlan.get(0).getId(), entity.getId()))) {
      throw FareErrorCode.FA_ELIGIBLE_TRAIN_ALREADY_HAS_FARE_PLAN_SETTING_IN_THE_SAME_DATE_PERIOD.toException(
          entity.getFarePlan().getCode(), entity.getEffSaleDate().toString(),
          entity.getEffDate().toString(), entity.getDscDate().toString());
    }
  }

  private void validateForSameEffSaleDate(List<EligibleTrain> sameVersions, EligibleTrain entity) {
    sameVersions.forEach(e -> {
      if (isDateRangeOverlap(e, entity)) {
        throw FareErrorCode.FA_ELIGIBLE_TRAIN_FARE_PLAN_TRAIN_DATE_RANGE_CANNOT_OVERLAP_WITH_SAME_EFF_SALE_DATE.toException(
            entity.getFarePlan().getCode(), entity.getEffSaleDate().toString());
      }
    });
  }

  private void validateUrgent(EligibleTrain newEntity) {
    List<EligibleTrain> sameDateSetting = repo.findByFarePlanAndEffSaleDateAndEffDateAndDscDateAndIsAllTrainEligibleFalse(
        newEntity.getFarePlan(), newEntity.getEffSaleDate(), newEntity.getEffDate(),
        newEntity.getDscDate());

    if (sameDateSetting.isEmpty()) {
      List<EligibleTrain> sameSaleDate = repo.findByFarePlanAndEffSaleDateIsAndIsAllTrainEligibleFalse(
          newEntity.getFarePlan(), newEntity.getEffSaleDate());
      // 一個重疊就不過->沒有相同發車日區間時，可接受與其他同開售日發車日區間無重疊的新增
      boolean isOverlapping = sameSaleDate.stream().anyMatch(e -> isDateRangeOverlap(newEntity, e));

      if (isOverlapping) {
        StringBuilder sb = new StringBuilder();
        sb.append(newEntity.getFarePlan().getCode());
        sb.append(EXCEPTION_SEPARATION).append(newEntity.getEffDate());
        sb.append(EXCEPTION_SEPARATION).append(newEntity.getDscDate());
        sb.append(EXCEPTION_SEPARATION).append(newEntity.getEffSaleDate());

        throw FareErrorCode.FA_ELIGIBLE_TRAIN_CANNOT_CREATE_URGENT_SETTING_WITHOUT_SAME_OR_NON_OVERLAPPING_TRAIN_DATE_RANGE_ELIGIBLE_TRAIN.toException(
            sb.toString());
      }
    }
  }

  @Override
  protected void writeAggregateRootValue(EligibleTrain entity, EligibleTrainInput input) {
    validateInput(entity, input);

    entity.setEffDate(input.getEffDate());
    entity.setDscDate(input.getDscDate());
    entity.setEffSaleDate(input.getEffSaleDate());
    entity.setRemark(input.getRemark());
    entity.setIsAllTrainEligible(input.getIsAllTrainEligible());
    entity.setIsHolidayIneligible(input.getIsHolidayIneligible());

    if (Objects.nonNull(input.getFareProjectId())) {
      FareProject project = fareProjectRepo.findById(input.getFareProjectId()).orElseThrow(
          FareErrorCode.FA_ELIGIBLE_TRAIN_CANNOT_ASSOCIATE_TO_THE_FARE_PROJECT::toException);
      entity.setProject(project);

      // 新增時才會設定 priority
      if (Objects.isNull(entity.getId())) {
        List<Integer> priorities = repo.findByProject(project).stream()
            .map(EligibleTrain::getPriority).filter(Objects::nonNull)
            .sorted(Collections.reverseOrder()).collect(Collectors.toList());
        int maxPriority = priorities.isEmpty() ? 1 : (priorities.get(0) + 1);
        entity.setPriority(maxPriority);
      }
    }

    FarePlan farePlan;
    if (Objects.nonNull(input.getBasicFarePlanId())) {
      farePlan = farePlanRepo.findById(input.getBasicFarePlanId()).orElseThrow(
          FareErrorCode.FA_ELIGIBLE_TRAIN_CANNOT_ASSOCIATE_TO_THE_BASIC_FARE_PLAN::toException);
    } else if (Objects.nonNull(input.getPromotionId())) {
      farePlan = farePlanRepo.findById(input.getPromotionId()).orElseThrow(
          FareErrorCode.FA_ELIGIBLE_TRAIN_CANNOT_ASSOCIATE_TO_THE_PROMOTION_PLAN::toException);
    } else {
      throw FareErrorCode.FA_ELIGIBLE_TRAIN_MUST_HAS_BASIC_FARE_PLAN_OR_PROMOTION.toException();
    }

    entity.setFarePlan(farePlan);

    writeDetailValue(entity, input);
  }

  private void writeDetailValue(EligibleTrain entity, EligibleTrainInput input) {
    // 無指定車次（全車次適用）
    if (input.getIsAllTrainEligible()) {
      entity.getDetails().clear();

      // 指定車次
    } else {
      List<EligibleTrainDetail> details = new ArrayList<>();
      input.getDetails().forEach(detailDto -> {

        EligibleTrainDetail detail = new EligibleTrainDetail();
        detail.setEligibleTrain(entity);

        detail.setDayOfWeek(detailDto.getDayOfWeek());
        List<EligibleTrainDetailTrain> trains = new ArrayList<>();
        detailDto.getTrains().forEach(trainInput -> {

          EligibleTrainDetailTrain train = new EligibleTrainDetailTrain();
          train.setEligibleTrainDetail(detail);
          train.setTrainNum(trainInput);

          trains.add(train);
        });

        detail.getTrains().clear();
        detail.getTrains().addAll(trains);

        details.add(detail);
      });

      entity.getDetails().clear();
      entity.getDetails().addAll(details);
    }
  }

  private void validateInput(EligibleTrain entity, EligibleTrainInput input) {
    LocalDate now = LocalDate.now();
    // 檢核跳過新增生效日為當天
    if ((Objects.isNull(entity.getId()) && input.getEffSaleDate().isBefore(now)) || (
        Objects.nonNull(entity.getId()) && (!entity.getEffSaleDate().isAfter(now)
            || !input.getEffSaleDate().isAfter(now)))) {
      throw FareErrorCode.FA_ELIGIBLE_TRAIN_CANNOT_CREATE_UPDATE_DELETE_THE_ACTIVE_ELIGIBLE_TRAIN.toException();

    }

    // 日期順序
    if (input.getDscDate().isBefore(input.getEffDate())) {
      throw SharedErrorCode.DSC_DATE_SHOULD_BE_GREATER_THAN_EFF_DATE.toException();
    }
  }

  private EligibleTrain deepCopy(EligibleTrain entity) {
    EligibleTrain copied = new EligibleTrain();
    copied.setEffDate(entity.getEffDate());
    copied.setDscDate(entity.getDscDate());
    copied.setEffSaleDate(entity.getEffSaleDate());
    copied.setRemark(entity.getRemark());
    copied.setIsAllTrainEligible(entity.getIsAllTrainEligible());
    copied.setIsHolidayIneligible(entity.getIsHolidayIneligible());

    copied.setProject(entity.getProject());
    copied.setPriority(entity.getPriority());
    copied.setFarePlan(entity.getFarePlan());

    List<EligibleTrainDetail> details = new ArrayList<>();
    entity.getDetails().forEach(originDetail -> {

      EligibleTrainDetail detail = new EligibleTrainDetail();
      detail.setEligibleTrain(copied);

      detail.setDayOfWeek(originDetail.getDayOfWeek());
      List<EligibleTrainDetailTrain> trains = new ArrayList<>();
      originDetail.getTrains().forEach(originTrain -> {

        EligibleTrainDetailTrain train = new EligibleTrainDetailTrain();
        train.setEligibleTrainDetail(detail);
        train.setTrainNum(originTrain.getTrainNum());

        trains.add(train);
      });

      detail.getTrains().addAll(trains);

      details.add(detail);
    });

    copied.getDetails().addAll(details);
    return copied;
  }

  public boolean isDateRangeOverlap(EligibleTrain entity1, EligibleTrain entity2) {

    return isDateRangeOverlap(entity1.getEffDate(), entity1.getDscDate(), entity2.getEffDate(),
        entity2.getDscDate());
  }

  public boolean isDateRangeOverlap(EligibleTrainInput input, EligibleTrain entity) {

    return isDateRangeOverlap(input.getEffDate(), input.getDscDate(), entity.getEffDate(),
        entity.getDscDate());
  }

  public boolean isDateRangeOverlap(LocalDate eff1, LocalDate dsc1, LocalDate eff2,
      LocalDate dsc2) {
    // 起日相同 or 迄日相同 or 起日與迄日相同 or 迄日與起日相同
    boolean boardEqual =
        (eff1.isEqual(eff2)) || (dsc1.isEqual(dsc2)) || (eff1.isEqual(dsc2)) || (dsc1.isEqual(
            eff2));

    boolean startDateEarlier = eff1.isBefore(eff2);

    boolean startDateSmallerThanEndDate = eff1.isBefore(dsc2);
    boolean endDateLargerThanStartDate = dsc1.isAfter(eff2);

    // 邊界值相同時 or 起日較早時-> 迄日要比對方的起日晚
    return boardEqual || (startDateEarlier && endDateLargerThanStartDate) ||
        // or 起日較晚時-> 起日要比對方的迄日要早
        (!startDateEarlier && startDateSmallerThanEndDate);
  }

  public void reorderPriorityForOtherEligibleTrain() {

    List<EligibleTrain> entities = repo.findAll().stream()
        .filter(e -> Objects.nonNull(e.getProject())).collect(Collectors.toList());

    Set<String> projectCodes = new HashSet<>();

    entities.forEach(e -> {

      // by project 更新 priority
      if (!projectCodes.contains(e.getProject().getCode())) {

        projectCodes.add(e.getProject().getCode());

        // 過期的資料沒有 priority
        List<EligibleTrain> expiredEligibleTrains = entities.stream()//
            .filter(e1 -> e1.getProject().equals(e.getProject()))//
            .filter(e2 -> e2.getDscDate().isBefore(LocalDate.now())).collect(Collectors.toList());

        expiredEligibleTrains.forEach(e2 -> e2.setPriority(null));

        // 仍未過期的資料重編 priority
        List<EligibleTrain> effectiveEligibleTrains = entities.stream()//
            .filter(e1 -> e1.getProject().equals(e.getProject()))//
            .filter(e2 -> !e2.getDscDate().isBefore(LocalDate.now())).sorted(
                Comparator.comparing(EligibleTrain::getEffSaleDate)
                    .thenComparing(EligibleTrain::getPriority)).collect(Collectors.toList());

        for (int i = 0; i < effectiveEligibleTrains.size(); i++) {
          effectiveEligibleTrains.get(i).setPriority(i + 1);
        }

        repo.saveAll(expiredEligibleTrains);
        repo.saveAll(effectiveEligibleTrains);
      }

    });
  }

  private EligibleTrainData translatePubMsg(EligibleTrain entity) {
    EligibleTrainData msg = new EligibleTrainData();
    msg.setEffDate(entity.getEffDate());
    msg.setDscDate(entity.getDscDate());
    msg.setEffSaleDate(entity.getEffSaleDate());
    msg.setRemark(entity.getRemark());
    msg.setPriority(entity.getPriority());
    msg.setIsAllTrain(entity.getIsAllTrainEligible());
    msg.setIsHolidayIneligible(entity.getIsHolidayIneligible());

    if (Objects.nonNull(entity.getProject())) {
      msg.setFareProjectCode(entity.getProject().getCode());
    }

    msg.setFarePlanCode(entity.getFarePlan().getCode());
    msg.setFarePlanType(entity.getFarePlan().getType().toString());

    List<EligibleTrainDetailData> details = new ArrayList<>();
    entity.getDetails().forEach(detail -> {

      EligibleTrainDetailData detailDto = new EligibleTrainDetailData();
      detailDto.setDayOfWeek(detail.getDayOfWeek());
      List<String> trains = detail.getTrains().stream().map(EligibleTrainDetailTrain::getTrainNum)
          .collect(Collectors.toList());
      detailDto.setTrains(trains);
      details.add(detailDto);
    });

    msg.setDetails(details);

    return msg;
  }

  private void publishReplacedEvent(Pair<EligibleTrainData, EligibleTrainData> msgPayloads) {
    EligibleTrainReplaced replaced = new EligibleTrainReplaced(interceptor.getUserId(),
        interceptor.getCorrelationId());
    replaced.setMsgPayload(msgPayloads);
    applicationEventPublisher.publishEvent(replaced);
  }

  private void publishCreatedEvent(EligibleTrainData msgPayloads) {
    EligibleTrainCreated created = new EligibleTrainCreated(interceptor.getUserId(),
        interceptor.getCorrelationId());
    created.setMsgPayload(msgPayloads);
    applicationEventPublisher.publishEvent(created);
  }

  private void publishDeletedEvent(EligibleTrainData msgPayloads) {
    EligibleTrainDeleted deleted = new EligibleTrainDeleted(interceptor.getUserId(),
        interceptor.getCorrelationId());
    deleted.setMsgPayload(msgPayloads);
    applicationEventPublisher.publishEvent(deleted);
  }

  private void publishUrgentCreatedEvent(EligibleTrain entity) {
    EligibleTrainUrgentCreated urgent = new EligibleTrainUrgentCreated(interceptor.getUserId(),
        interceptor.getCorrelationId());
    urgent.setUrgentId(entity.getId());
    urgent.setFarePlan(entity.getFarePlan().getCode());
    urgent.setEffSaleDate(entity.getEffSaleDate());
    urgent.setMsgPayload(translatePubMsg(entity));
    applicationEventPublisher.publishEvent(urgent);
  }

  private void createFutureVersionControl(EligibleTrain addedEntity) {

    // temp table: record some extra info
    EligibleTrainTemp tempTable = new EligibleTrainTemp();
    tempTable.setFarePlanCode(addedEntity.getFarePlan().getCode());
    tempTable.setEffSaleDate(addedEntity.getEffSaleDate());
    tempTable.setCreateUser(interceptor.getUserId());
    tempTable.setCreateUserEmail(interceptor.getUserEmail());
    tempTable.setCorrelationId(interceptor.getCorrelationId());
    tempRepo.save(tempTable);

    // 未來版
    EligibleTrainFutureVersionControl futureVersionControl = futureVersionControlService.init(
        LocalDateTime.of(addedEntity.getEffSaleDate().minusDays(1), CRON_JOB_EXEC_TIME),
        EligibleTrainFutureVersionControl.class);

    futureVersionControl.setEligibleTrainTemp(tempTable);
    futureVersionControl = futureVersionRepo.save(futureVersionControl);

    futureVersionControlService.scheduleFutureVersionControl(futureVersionControl,
        JOB_BSM_ELIGIBLE_TRAIN, interceptor.getCorrelationId());
  }

  private void deleteFutureVersionControl(EligibleTrain entity) {

    List<EligibleTrainTemp> temps = tempRepo.findByFarePlanCodeAndEffSaleDate(
        entity.getFarePlan().getCode(), entity.getEffSaleDate());
    if (!temps.isEmpty()) {
      List<EligibleTrainFutureVersionControl> activeFutureVersionControls = futureVersionRepo.findByEligibleTrainTemp_IdAndStatusInAndDeleted(
          temps.get(0).getId(),
          Arrays.asList(FutureVersionControlStatus.ACCEPTED, FutureVersionControlStatus.SCHEDULED),
          false);

      if (!activeFutureVersionControls.isEmpty()) {
        futureVersionControlService.deleteFutureVersionControls(activeFutureVersionControls);
      }

      tempRepo.delete(temps.get(0));
    }
  }

  private void validateFutureVersionControlStepRunning(EligibleTrain entity) {

    List<EligibleTrainTemp> temps = tempRepo.findByFarePlanCodeAndEffSaleDate(
        entity.getFarePlan().getCode(), entity.getEffSaleDate());
    if (!temps.isEmpty()) {
      List<EligibleTrainFutureVersionControl> result = futureVersionRepo.findByEligibleTrainTemp_IdAndStatus(
          temps.get(0).getId(), FutureVersionControlStatus.STARTED);
      if (!result.isEmpty()) {
        throw FareErrorCode.FA_ELIGIBLE_TRAIN_STEP_IS_RUNNING.toException(
            entity.getFarePlan().getCode());
      }
    }
  }

  private boolean hasFutureVersionControlStep(EligibleTrain entity) {
    boolean hasFuture = false;

    List<EligibleTrainTemp> temps = tempRepo.findByFarePlanCodeAndEffSaleDate(
        entity.getFarePlan().getCode(), entity.getEffSaleDate());
    if (!temps.isEmpty()) {
      List<EligibleTrainFutureVersionControl> result = futureVersionRepo.findByEligibleTrainTemp_IdAndStatusIn(
          temps.get(0).getId(),
          Arrays.asList(FutureVersionControlStatus.ACCEPTED, FutureVersionControlStatus.SCHEDULED));
      if (!result.isEmpty()) {
        hasFuture = true;
      }
    }

    return hasFuture;
  }


  private void notifyOnlineUser(String basePattern, EligibleTrainTemp temp, String userEmail) {
    String content = String.format(basePattern, temp.getFarePlanCode(), temp.getEffSaleDate());

    emailService.sendSimpleMessage(userEmail, RESULT_MAIL_SUBJECT, content);
  }

  private void notifyOnlineUser(String basePattern, EligibleTrain entity, String userEmail) {
    String content = String.format(basePattern, entity.getFarePlan().getCode(),
        entity.getFarePlan().getName(), entity.getEffDate(), entity.getDscDate());

    emailService.sendSimpleMessage(userEmail, RESULT_MAIL_SUBJECT, content);
  }

  private List<EligibleTrain> getOverlappingEligibleTrains(EligibleTrain entity) {
    return this.repo.findByFarePlan(entity.getFarePlan()).stream().filter(p ->
        (isDateBetweenRange(entity.getEffDate(), p.getEffDate(), p.getDscDate())
            || isDateBetweenRange(entity.getDscDate(), p.getEffDate(), p.getDscDate())
            || isDateBetweenRange(p.getEffDate(), entity.getEffDate(), entity.getDscDate())
            || isDateBetweenRange(p.getDscDate(), entity.getEffDate(), entity.getDscDate()))
            && !Objects.equals(entity.getId(), p.getId())).collect(Collectors.toList());
  }

  private List<EligibleTrain> getOverlappingEligibleTrains(EligibleTrain entity,
      EligibleTrainInput input) {
    return this.repo.findByFarePlan(entity.getFarePlan()).stream().filter(p ->
        (isDateBetweenRange(input.getEffDate(), p.getEffDate(), p.getDscDate())
            || isDateBetweenRange(input.getDscDate(), p.getEffDate(), p.getDscDate())
            || isDateBetweenRange(p.getEffDate(), input.getEffDate(), input.getDscDate())
            || isDateBetweenRange(p.getDscDate(), input.getEffDate(), input.getDscDate()))
            && !Objects.equals(entity.getId(), p.getId())).collect(Collectors.toList());
  }

  private boolean isDateBetweenRange(LocalDate target, LocalDate from, LocalDate to) {
    return target.equals(from) || target.isEqual(to) || (target.isAfter(from) && target.isBefore(
        to));
  }

  public void clean(LocalDate expiryDate) {
    Set<EligibleTrain> expiresEntities = repo.findAll().stream()
        .filter(eligibleTrain -> eligibleTrain.getDscDate().isBefore(expiryDate))
        .collect(Collectors.toSet());

    // 先刪除為執行排程
    expiresEntities.forEach(e -> {
      if (hasFutureVersionControlStep(e)) {
        deleteFutureVersionControl(e);
      }
    });

    repo.deleteAll(expiresEntities);
  }
}
