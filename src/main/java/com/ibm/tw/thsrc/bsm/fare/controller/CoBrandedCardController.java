/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.fa.api.CoBrandedCardApi;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardOutput;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/CoBrandedCards")
public class CoBrandedCardController implements CoBrandedCardApi {

  @Autowired
  private CoBrandedCardCommandService commandService;

  @Autowired
  private CoBrandedCardQueryService queryService;


  @Autowired
  private EventStore eventStore;

  @Override
  public Page<CoBrandedCardOutput> search(Search search) {
    return queryService.search(search);
  }

  @Override
  public CoBrandedCardOutput replace(Long id, CoBrandedCardInput input) {
    eventStore.validateFunctionLock(OperationFunction.COBRANDED_CARD);
    commandService.update(id, input);
    return queryService.read(id);
  }
}
