/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardOperatingParameterOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardPenaltyPolicyOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardProfileDiscountOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardRoundOffOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardStationRuleOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardTransferBonusOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.ElectronicMoneyType;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCard;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardPenaltyPolicy;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardProfileDiscount;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardRoundOffRule;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardTransferBonus;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCard_;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardPenaltyPolicyRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardProfileDiscountRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardRoundOffRuleRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardTransferBonusRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.CoBrandedCardFilter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Transactional
@Service
public class CoBrandedCardOperatingParameterQueryService extends
    AbstractQueryService<CoBrandedCard, CoBrandedCardOperatingParameterOutput, CoBrandedCardFilter, Void> {

  @Autowired
  private ObjectMapper objectMapper;

  @Autowired
  private CoBrandedCardRepository cardRepo;
  @Autowired
  private CoBrandedCardTransferBonusRepository transferRepo;
  @Autowired
  private CoBrandedCardProfileDiscountRepository discountRepo;
  @Autowired
  private CoBrandedCardPenaltyPolicyRepository penaltyRepo;

  @Autowired
  private CoBrandedCardRoundOffRuleRepository roundOffRuleRepo;

  @Override
  public CoBrandedCardOperatingParameterOutput read(Long id) {
    CoBrandedCard card = cardRepo.findById(id).orElseThrow(ResourceNotFoundException::new);
    return translate(card);
  }

  @Override
  public Page<CoBrandedCardOperatingParameterOutput> search(Search searchRequest) {
    PageRequest pr = translateToPageRequest(searchRequest);
    Specification<CoBrandedCard> spec = translateToSpecification(searchRequest);
    Page<CoBrandedCard> page = cardRepo.findAll(spec, pr);
    return page.map(this::translate);
  }

  @Override
  protected CoBrandedCardOperatingParameterOutput translate(CoBrandedCard card) {
    CoBrandedCardOperatingParameterOutput response = new CoBrandedCardOperatingParameterOutput();
    response.setCoBrandedCardId(card.getId());
    response.setCoBrandedCardType(card.getElectronicMoneyType());

    List<CoBrandedCardStationRuleOutput> routes = new ArrayList<>();
    card.getRoutes().forEach(r -> {
      CoBrandedCardStationRuleOutput route = new CoBrandedCardStationRuleOutput();
      route.setId(r.getId());
      route.setDataVersion(r.getDataVersion());
      route.setStationId(r.getStation().getId());
      route.setCode(r.getStation().getCode());
      route.setEnName(r.getStation().getEnName());
      route.setZhName(r.getStation().getZhName());
      route.setZhPrintName(r.getStation().getZhPrintName());
      route.setSameStationEntryExitPrice(r.getSameStationEntryExitPrice());

      routes.add(route);
    });

    response.setRoutes(routes);

    List<CoBrandedCardTransferBonus> transferBonuses = transferRepo.findByCoBrandedCard(card);
    if (!transferBonuses.isEmpty()) {
      translateTransferBonus(response, transferBonuses);
    }

    List<CoBrandedCardProfileDiscount> profileDiscounts = discountRepo.findByCoBrandedCard(card);
    if (!transferBonuses.isEmpty()) {
      translateProfileDiscount(response, profileDiscounts);
    }

    Optional<CoBrandedCardPenaltyPolicy> penaltyPolicy = penaltyRepo.findByCoBrandedCard(card);
    penaltyPolicy.ifPresent(
        coBrandedCardPenaltyPolicy -> translatePenaltyPolicy(response, coBrandedCardPenaltyPolicy));

    Optional<CoBrandedCardRoundOffRule> roundOffRule = roundOffRuleRepo.findByElectronicMoneyType(
        card.getElectronicMoneyType());

    if (roundOffRule.isPresent()) {
      translateRoundOffRule(response, roundOffRule.get());
    } else {
      response.setRoundOff(new CoBrandedCardRoundOffOutput());
    }

    return response;
  }

  private void translateTransferBonus(CoBrandedCardOperatingParameterOutput response,
      List<CoBrandedCardTransferBonus> transferBonuses) {

    List<CoBrandedCardTransferBonusOutput> bonusOutputs = new ArrayList<>();
    transferBonuses.forEach(t -> {
      CoBrandedCardTransferBonusOutput bonusOutput = new CoBrandedCardTransferBonusOutput();
      bonusOutput.setId(t.getId());
      bonusOutput.setDataVersion(t.getDataVersion());
      bonusOutput.setTransport(t.getTransportMode());
      bonusOutput.setProfileId(t.getProfile().getId());
      bonusOutput.setProfileName(t.getProfile().getName());
      bonusOutput.setDiscountAmt(t.getDiscountAmt());

      bonusOutputs.add(bonusOutput);
    });

    response.setTransferBonuses(bonusOutputs);
  }


  private void translateProfileDiscount(CoBrandedCardOperatingParameterOutput response,
      List<CoBrandedCardProfileDiscount> profileDiscounts) {

    List<CoBrandedCardProfileDiscountOutput> discountOutputs = new ArrayList<>();
    profileDiscounts.forEach(p -> {
      CoBrandedCardProfileDiscountOutput discountOutput = new CoBrandedCardProfileDiscountOutput();
      discountOutput.setId(p.getId());
      discountOutput.setDataVersion(p.getDataVersion());
      discountOutput.setDiscountPct(p.getDiscountPct());
      discountOutput.setProfileId(p.getProfile().getId());
      discountOutput.setProfileName(p.getProfile().getName());
      discountOutput.setMunicipalityId(p.getMunicipality().getId());
      discountOutput.setMunicipalityName(p.getMunicipality().getName());

      discountOutputs.add(discountOutput);
    });

    response.setProfileDiscounts(discountOutputs);
  }

  private void translatePenaltyPolicy(CoBrandedCardOperatingParameterOutput response,
      CoBrandedCardPenaltyPolicy penaltyPolicy) {

    CoBrandedCardPenaltyPolicyOutput penaltyOutput = new CoBrandedCardPenaltyPolicyOutput();
    penaltyOutput.setId(penaltyPolicy.getId());
    penaltyOutput.setDataVersion(penaltyPolicy.getDataVersion());
    penaltyOutput.setOvertimePenalty(penaltyPolicy.getOvertimePenalty());
    penaltyOutput.setSameStationEntryExitPenalty(penaltyPolicy.getSameStationEntryExitPenalty());
    penaltyOutput.setEntryExitCodeMismatchPenalty(penaltyPolicy.getEntryExitCodeMismatchPenalty());

    response.setPenaltyPolicy(penaltyOutput);
  }

  private void translateRoundOffRule(CoBrandedCardOperatingParameterOutput response,
      CoBrandedCardRoundOffRule roundOffRule) {

    CoBrandedCardRoundOffOutput roundOffRuleOutput = new CoBrandedCardRoundOffOutput();
    roundOffRuleOutput.setId(roundOffRule.getId());
    roundOffRuleOutput.setCardType(roundOffRule.getElectronicMoneyType());
    roundOffRuleOutput.setValue(roundOffRule.getValue());
    roundOffRuleOutput.setThreshold(roundOffRule.getThreshold());

    response.setRoundOff(roundOffRuleOutput);
  }

  @Override
  protected Sort translate(Void sortBy, Direction sortDirection) {
    return Sort.unsorted();
  }

  @Override
  protected Predicate translate(ComparisonExpression<CoBrandedCardFilter> expression,
      Root<CoBrandedCard> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {

    ComparisonOperator op = expression.getOperator();

    if (expression.getAttributeValue().getType() != null) {

      Path<ElectronicMoneyType> type = root.get(CoBrandedCard_.ELECTRONIC_MONEY_TYPE);
      ElectronicMoneyType value = expression.getAttributeValue().getType();
      return translate(type, op, value, criteriaBuilder);

    } else {
      return null;
    }
  }

  @Override
  protected Function<Map<String, Boolean>, Void> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, Void.class);
  }

  @Override
  protected Function<Map<String, Object>, CoBrandedCardFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, CoBrandedCardFilter.class);
  }
}
