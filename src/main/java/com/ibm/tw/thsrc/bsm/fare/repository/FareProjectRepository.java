/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import com.ibm.tw.thsrc.bsm.fa.enums.FareProjectType;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface FareProjectRepository
    extends JpaRepository<FareProject, Long>,
        JpaSpecificationExecutor<FareProject>,
        EntityGraphJpaSpecificationExecutor<FareProject> {

  public Optional<FareProject> findByCode(String code);

  public List<FareProject> findByAiValue(String aiValue);

  public List<FareProject> findByType(FareProjectType type);
}
