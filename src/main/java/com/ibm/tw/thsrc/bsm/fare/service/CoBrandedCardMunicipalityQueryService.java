/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardMunicipalityOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.ElectronicMoneyType;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardMunicipality;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardMunicipality_;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardMunicipalityRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.CoBrandedCardMunicipalityFilter;
import java.util.Map;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class CoBrandedCardMunicipalityQueryService extends
    AbstractQueryService<CoBrandedCardMunicipality, CoBrandedCardMunicipalityOutput, CoBrandedCardMunicipalityFilter, Void> {

  @Autowired
  private ObjectMapper objectMapper;

  @Autowired
  private CoBrandedCardMunicipalityRepository repo;

  @Override
  public CoBrandedCardMunicipalityOutput read(Long id) {
    throw new UnsupportedOperationException();
  }

  @Override
  public Page<CoBrandedCardMunicipalityOutput> search(Search searchRequest) {
    PageRequest pr = translateToPageRequest(searchRequest);
    Specification<CoBrandedCardMunicipality> spec = translateToSpecification(searchRequest);
    Page<CoBrandedCardMunicipality> page = repo.findAll(spec, pr);
    return page.map(this::translate);
  }

  @Override
  protected CoBrandedCardMunicipalityOutput translate(CoBrandedCardMunicipality entity) {
    CoBrandedCardMunicipalityOutput response = new CoBrandedCardMunicipalityOutput();
    response.setId(entity.getId());
    response.setName(entity.getName());
    response.setCode(entity.getCode());
    response.setType(entity.getElectronicMoneyType());

    return response;
  }

  @Override
  protected Sort translate(Void sortBy, Direction sortDirection) {
    return Sort.unsorted();
  }

  @Override
  protected Predicate translate(ComparisonExpression<CoBrandedCardMunicipalityFilter> expression,
      Root<CoBrandedCardMunicipality> root, CriteriaQuery<?> query,
      CriteriaBuilder criteriaBuilder) {
    ComparisonOperator op = expression.getOperator();

    if (expression.getAttributeValue().getType() != null) {

      Path<ElectronicMoneyType> type = root.get(CoBrandedCardMunicipality_.ELECTRONIC_MONEY_TYPE);
      ElectronicMoneyType value = expression.getAttributeValue().getType();
      return translate(type, op, value, criteriaBuilder);

    } else {
      return null;
    }
  }

  @Override
  protected Function<Map<String, Boolean>, Void> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, Void.class);
  }

  @Override
  protected Function<Map<String, Object>, CoBrandedCardMunicipalityFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, CoBrandedCardMunicipalityFilter.class);
  }
}
