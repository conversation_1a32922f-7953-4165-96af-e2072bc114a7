/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.ConflictException;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardOperatingParameterInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardPenaltyPolicyInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardProfileDiscountInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardTransferBonusInput;
import com.ibm.tw.thsrc.bsm.fa.enums.ElectronicMoneyType;
import com.ibm.tw.thsrc.bsm.fare.domain.event.CoBrandedCardOperatingParameterCronReplaced;
import com.ibm.tw.thsrc.bsm.fare.domain.event.CoBrandedCardOperatingParameterReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCard;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardMunicipality;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardPenaltyPolicy;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardProfile;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardProfileDiscount;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardRoundOffRule;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardStationRule;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardTransferBonus;
import com.ibm.tw.thsrc.bsm.fare.entity.StationProjection;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardMunicipalityRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardPenaltyPolicyRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardProfileDiscountRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardProfileRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardRoundOffRuleRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardStationRuleRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardTransferBonusRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.StationProjectionRepository;
import com.ibm.tw.thsrc.bsm.message.fare.CoBrandedCardOperatingParameterData;
import com.ibm.tw.thsrc.bsm.message.fare.CoBrandedCardPenaltyPolicyData;
import com.ibm.tw.thsrc.bsm.message.fare.CoBrandedCardProfileDiscountData;
import com.ibm.tw.thsrc.bsm.message.fare.CoBrandedCardRoundOffData;
import com.ibm.tw.thsrc.bsm.message.fare.CoBrandedCardStationRuleData;
import com.ibm.tw.thsrc.bsm.message.fare.CoBrandedCardTransferBonusData;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Transactional
@Service
public class CoBrandedCardOperatingParameterCommandService extends
    AbstractCommandService<CoBrandedCard, CoBrandedCardOperatingParameterInput> {

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private CoBrandedCardRepository cardRepo;
  @Autowired
  private CoBrandedCardStationRuleRepository stationRuleRepo;
  @Autowired
  private StationProjectionRepository stationRepo;
  @Autowired
  private CoBrandedCardTransferBonusRepository transferRepo;
  @Autowired
  private CoBrandedCardProfileRepository profileRepo;
  @Autowired
  private CoBrandedCardProfileDiscountRepository discountRepo;
  @Autowired
  private CoBrandedCardMunicipalityRepository municipalityRepo;
  @Autowired
  private CoBrandedCardPenaltyPolicyRepository penaltyRepo;
  @Autowired
  private CoBrandedCardRoundOffRuleRepository roundOffRuleRepo;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Override
  public Long create(CoBrandedCardOperatingParameterInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void update(Long id, CoBrandedCardOperatingParameterInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void delete(Long id) {
    throw new UnsupportedOperationException();
  }

  public Long replace(CoBrandedCardOperatingParameterInput input) {

    // 票價表
    CoBrandedCard card = cardRepo.findById(input.getCoBrandedCardId()).orElseThrow(
        FareErrorCode.FA_CO_BRANDED_CARD_OPERATING_PARAMETER_CANNOT_ASSOCIATE_TO_THE_CO_BRANDED_CARD::toException);
    writeAggregateRootValue(card, input);
    cardRepo.save(card);

    // 轉乘優惠
    List<CoBrandedCardTransferBonus> transferBonuses = new ArrayList<>();
    input.getTransferBonuses().forEach(transferBonusInput -> {
      CoBrandedCardTransferBonus entity;
      if (Objects.isNull(transferBonusInput.getId())) {
        entity = new CoBrandedCardTransferBonus();
        entity.setCoBrandedCard(card);

      } else {
        entity = transferRepo.findById(transferBonusInput.getId()).orElseThrow(
            FareErrorCode.FA_CO_BRANDED_CARD_OPERATING_PARAMETER_CANNOT_ASSOCIATE_TO_THE_CO_BRANDED_CARD_TRANSFER_BONUS::toException);
        validateConcurrency(entity, transferBonusInput.getDataVersion());
      }

      writeAggregateRootValue(entity, transferBonusInput);
      CoBrandedCardTransferBonus savedEntity = transferRepo.save(entity);
      transferBonuses.add(savedEntity);
    });

    // 身份優惠
    List<CoBrandedCardProfileDiscount> profileDiscounts = new ArrayList<>();
    input.getProfileDiscounts().forEach(profileDiscountInput -> {
      CoBrandedCardProfileDiscount entity;
      if (Objects.isNull(profileDiscountInput.getId())) {
        entity = new CoBrandedCardProfileDiscount();
        entity.setCoBrandedCard(card);

      } else {
        entity = discountRepo.findById(profileDiscountInput.getId()).orElseThrow(
            FareErrorCode.FA_CO_BRANDED_CARD_OPERATING_PARAMETER_CANNOT_ASSOCIATE_TO_THE_CO_BRANDED_CARD_PROFILE_DISCOUNT::toException);
        validateConcurrency(entity, profileDiscountInput.getDataVersion());
      }

      writeAggregateRootValue(entity, profileDiscountInput);
      CoBrandedCardProfileDiscount savedEntity = discountRepo.save(entity);
      profileDiscounts.add(savedEntity);
    });

    // 罰款表
    CoBrandedCardPenaltyPolicy penaltyPolicy;
    if (Objects.isNull(input.getPenaltyPolicy().getId())) {
      penaltyPolicy = new CoBrandedCardPenaltyPolicy();
      penaltyPolicy.setCoBrandedCard(card);

    } else {
      penaltyPolicy = penaltyRepo.findById(input.getPenaltyPolicy().getId()).orElseThrow(
          FareErrorCode.FA_CO_BRANDED_CARD_OPERATING_PARAMETER_CANNOT_ASSOCIATE_TO_THE_CO_BRANDED_CARD_PENALTY_POLICY::toException);
      validateConcurrency(penaltyPolicy, input.getPenaltyPolicy().getDataVersion());
    }

    writeAggregateRootValue(penaltyPolicy, input.getPenaltyPolicy());
    CoBrandedCardPenaltyPolicy savedPenaltyPolicy = penaltyRepo.save(penaltyPolicy);

    // 寫入事件資料＆發事件
    CoBrandedCardOperatingParameterData msgPayload = translatePubMsg(card);
    translateTransFerBonusPubMsg(msgPayload, transferBonuses);
    translateProfileDiscountPubMsg(msgPayload, profileDiscounts);
    translatePenaltyPolicyPubMsg(msgPayload, savedPenaltyPolicy);
    Optional<CoBrandedCardRoundOffRule> roundOffRule = roundOffRuleRepo.findByElectronicMoneyType(
        card.getElectronicMoneyType());
    if (roundOffRule.isPresent()) {
      translateRoundOffRulePubMsg(msgPayload, roundOffRule.get());
    } else {
      msgPayload.setRoundOff(new CoBrandedCardRoundOffData());
    }

    publishReplacedEvent(msgPayload);
    return card.getId();
  }

  public void cronJob(LocalDate effDate, String userId) {
    // 悠遊卡
    CoBrandedCardOperatingParameterData easyPayload =
        getMsgPayloadByCardType(ElectronicMoneyType.EASY_CARD);

    // 一卡通
    CoBrandedCardOperatingParameterData iPassPayload =
        getMsgPayloadByCardType(ElectronicMoneyType.I_PASS);

    CoBrandedCardOperatingParameterCronReplaced replaced = new CoBrandedCardOperatingParameterCronReplaced(
        userId, interceptor.getCorrelationId());
    replaced.setEasyPayload(easyPayload);
    replaced.setIPassPayload(iPassPayload);
    replaced.setEffDate(effDate);

    publishCronReplacedEvent(replaced);
  }

  private CoBrandedCardOperatingParameterData getMsgPayloadByCardType(ElectronicMoneyType type) {
    CoBrandedCard card = cardRepo.findByElectronicMoneyType(type)
        .orElseThrow(
            FareErrorCode.FA_CO_BRANDED_CARD_OPERATING_PARAMETER_CANNOT_ASSOCIATE_TO_THE_CO_BRANDED_CARD::toException);
    List<CoBrandedCardTransferBonus> transferBonuses = transferRepo.findByCoBrandedCard(card);
    List<CoBrandedCardProfileDiscount> profileDiscounts = discountRepo.findByCoBrandedCard(card);
    CoBrandedCardPenaltyPolicy penaltyPolicy = penaltyRepo.findByCoBrandedCard(card)
        .orElseThrow(
            FareErrorCode.FA_CO_BRANDED_CARD_OPERATING_PARAMETER_CANNOT_ASSOCIATE_TO_THE_CO_BRANDED_CARD_PENALTY_POLICY::toException);

    CoBrandedCardOperatingParameterData msgPayload = translatePubMsg(card);
    translateTransFerBonusPubMsg(msgPayload, transferBonuses);
    translateProfileDiscountPubMsg(msgPayload, profileDiscounts);
    translatePenaltyPolicyPubMsg(msgPayload, penaltyPolicy);
    Optional<CoBrandedCardRoundOffRule> roundOffRule = roundOffRuleRepo.findByElectronicMoneyType(
        type);
    if (roundOffRule.isPresent()) {
      translateRoundOffRulePubMsg(msgPayload, roundOffRule.get());
    } else {
      msgPayload.setRoundOff(new CoBrandedCardRoundOffData());
    }

    return msgPayload;
  }

  @Override
  protected void validateInvariants(CoBrandedCard aggregateRoot) throws UnprocessableException {
    // no need
  }

  @Override
  protected void writeAggregateRootValue(CoBrandedCard entity,
      CoBrandedCardOperatingParameterInput input) {

    List<CoBrandedCardStationRule> newStationRules = new ArrayList<>();
    input.getRoutes().forEach(r -> {

      CoBrandedCardStationRule stationRule;
      if (Objects.nonNull(r.getId())) {
        stationRule = entity.getRoutes().stream()
            .filter(route -> route.getId().equals(r.getId())).collect(Collectors.toList()).get(0);
        validateConcurrency(stationRule, r.getDataVersion());

      } else {
        stationRule = new CoBrandedCardStationRule();
        stationRule.setCoBrandedCard(entity);

        newStationRules.add(stationRule);
      }

      StationProjection station = stationRepo.findById(r.getStationId()).orElseThrow(
          FareErrorCode.FA_CO_BRANDED_CARD_CANNOT_ASSOCIATE_TO_THE_STATION_PROJECTION::toException);
      stationRule.setStation(station);
      stationRule.setSameStationEntryExitPrice(r.getSameStationEntryExitPrice());
    });

    if (!newStationRules.isEmpty()) {
      entity.getRoutes().addAll(newStationRules);
    }
  }

  protected void writeAggregateRootValue(CoBrandedCardTransferBonus entity,
      CoBrandedCardTransferBonusInput input) {

    CoBrandedCardProfile profile = profileRepo.findById(input.getProfileId()).orElseThrow(
        FareErrorCode.FA_CO_BRANDED_CARD_TRANSFER_BONUS_CANNOT_ASSOCIATE_TO_THE_CO_BRANDED_CARD_PROFILE::toException);
    entity.setProfile(profile);
    entity.setTransportMode(input.getTransport());
    entity.setDiscountAmt(input.getDiscountAmt());
  }

  protected void writeAggregateRootValue(CoBrandedCardProfileDiscount entity,
      CoBrandedCardProfileDiscountInput input) {

    CoBrandedCardProfile profile = profileRepo.findById(input.getProfileId()).orElseThrow(
        FareErrorCode.FA_CO_BRANDED_CARD_PROFILE_DISCOUNT_CANNOT_ASSOCIATE_TO_THE_CO_BRANDED_CARD_PROFILE::toException);
    CoBrandedCardMunicipality municipality = municipalityRepo.findById(input.getMunicipalityId())
        .orElseThrow(
            FareErrorCode.FA_CO_BRANDED_CARD_PROFILE_DISCOUNT_CANNOT_ASSOCIATE_TO_THE_CO_BRANDED_CARD_MUNICIPALITY::toException);

    entity.setProfile(profile);
    entity.setMunicipality(municipality);
    entity.setDiscountPct(input.getDiscountPct());

  }

  protected void writeAggregateRootValue(CoBrandedCardPenaltyPolicy entity,
      CoBrandedCardPenaltyPolicyInput input) {

    entity.setOvertimePenalty(input.getOvertimePenalty());
    entity.setSameStationEntryExitPenalty(input.getSameStationEntryExitPenalty());
    entity.setEntryExitCodeMismatchPenalty(input.getEntryExitCodeMismatchPenalty());
  }

  protected void validateConcurrency(CoBrandedCardStationRule aggregateRoot, Long inputVersion) {
    if (!aggregateRoot.getDataVersion().equals(inputVersion)) {
      throw new ConflictException();
    }

    // force JPA to update data-version of aggregate-root even no property changed
    aggregateRoot.setUpdateTimestamp(ZonedDateTime.now());
  }

  protected void validateConcurrency(CoBrandedCardTransferBonus aggregateRoot, Long inputVersion) {
    if (!aggregateRoot.getDataVersion().equals(inputVersion)) {
      throw new ConflictException();
    }

    // force JPA to update data-version of aggregate-root even no property changed
    aggregateRoot.setUpdateTimestamp(ZonedDateTime.now());
  }

  protected void validateConcurrency(CoBrandedCardProfileDiscount aggregateRoot,
      Long inputVersion) {
    if (!aggregateRoot.getDataVersion().equals(inputVersion)) {
      throw new ConflictException();
    }

    // force JPA to update data-version of aggregate-root even no property changed
    aggregateRoot.setUpdateTimestamp(ZonedDateTime.now());
  }

  protected void validateConcurrency(CoBrandedCardPenaltyPolicy aggregateRoot, Long inputVersion) {
    if (!aggregateRoot.getDataVersion().equals(inputVersion)) {
      throw new ConflictException();
    }

    // force JPA to update data-version of aggregate-root even no property changed
    aggregateRoot.setUpdateTimestamp(ZonedDateTime.now());
  }

  private CoBrandedCardOperatingParameterData translatePubMsg(CoBrandedCard entity) {
    CoBrandedCardOperatingParameterData msg = new CoBrandedCardOperatingParameterData();

    msg.setElectronicMoneyType(entity.getElectronicMoneyType().toString());

    List<CoBrandedCardStationRuleData> routes = new ArrayList<>();
    entity.getRoutes().forEach(r -> {
      CoBrandedCardStationRuleData route = new CoBrandedCardStationRuleData();
      route.setStationCode(r.getStation().getCode());
      route.setStationZhName(r.getStation().getZhName());
      route.setDisplayOrder(r.getStation().getDisplayOrder());
      route.setSameStationEntryExitPrice(r.getSameStationEntryExitPrice());

      routes.add(route);
    });

    msg.setRoutes(routes);
    return msg;
  }

  private void translateTransFerBonusPubMsg(CoBrandedCardOperatingParameterData msg,
      List<CoBrandedCardTransferBonus> entities) {
    List<CoBrandedCardTransferBonusData> transferBonuses = new ArrayList<>();

    entities.forEach(t -> {
      CoBrandedCardTransferBonusData transferBonus = new CoBrandedCardTransferBonusData();
      transferBonus.setDiscountAmt(t.getDiscountAmt());
      transferBonus.setTransportMode(t.getTransportMode().toString());
      transferBonus.setTransportModeIndex(t.getTransportMode().ordinal());
      transferBonus.setProfileCode(t.getProfile().getCode());
      transferBonus.setProfileName(t.getProfile().getName());

      transferBonuses.add(transferBonus);
    });

    msg.getTransferBonuses().addAll(transferBonuses);
  }

  private void translateProfileDiscountPubMsg(CoBrandedCardOperatingParameterData msg,
      List<CoBrandedCardProfileDiscount> entities) {
    List<CoBrandedCardProfileDiscountData> profileDiscounts = new ArrayList<>();

    entities.forEach(d -> {
      CoBrandedCardProfileDiscountData profileDiscount = new CoBrandedCardProfileDiscountData();
      profileDiscount.setDiscountPct(d.getDiscountPct());
      profileDiscount.setProfileCode(d.getProfile().getCode());
      profileDiscount.setProfileName(d.getProfile().getName());
      profileDiscount.setMunicipalityCode(d.getMunicipality().getCode());
      profileDiscount.setMunicipalityName(d.getMunicipality().getName());

      profileDiscounts.add(profileDiscount);
    });

    msg.getProfileDiscounts().addAll(profileDiscounts);
  }

  private void translatePenaltyPolicyPubMsg(CoBrandedCardOperatingParameterData msg,
      CoBrandedCardPenaltyPolicy entity) {
    CoBrandedCardPenaltyPolicyData penaltyPolicy = new CoBrandedCardPenaltyPolicyData();

    penaltyPolicy.setOvertimePenalty(entity.getOvertimePenalty());
    penaltyPolicy.setSameStationEntryExitPenalty(entity.getSameStationEntryExitPenalty());
    penaltyPolicy.setEntryExitCodeMismatchPenalty(entity.getEntryExitCodeMismatchPenalty());

    msg.setPenaltyPolicy(penaltyPolicy);
  }

  private void translateRoundOffRulePubMsg(CoBrandedCardOperatingParameterData msg,
      CoBrandedCardRoundOffRule entity) {
    CoBrandedCardRoundOffData roundOffRule = new CoBrandedCardRoundOffData();

    roundOffRule.setType(entity.getElectronicMoneyType());
    roundOffRule.setValue(entity.getValue());
    roundOffRule.setThreshold(entity.getThreshold());

    msg.setRoundOff(roundOffRule);
  }

  private void publishReplacedEvent(CoBrandedCardOperatingParameterData msgPayload) {
    CoBrandedCardOperatingParameterReplaced replaced = new CoBrandedCardOperatingParameterReplaced(
        interceptor.getUserId(), interceptor.getCorrelationId());
    replaced.setMsgPayload(msgPayload);
    applicationEventPublisher.publishEvent(replaced);
  }

  private void publishCronReplacedEvent(CoBrandedCardOperatingParameterCronReplaced replaced) {
    applicationEventPublisher.publishEvent(replaced);
  }
}
