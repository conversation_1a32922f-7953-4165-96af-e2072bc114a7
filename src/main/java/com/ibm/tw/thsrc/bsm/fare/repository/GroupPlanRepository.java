/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fare.entity.GroupPlan;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface GroupPlanRepository extends JpaRepository<GroupPlan, Long>,
JpaSpecificationExecutor<GroupPlan> {

  List<GroupPlan> findByName(String name);

  List<GroupPlan> findByNameAndIdNot(String name, Long id);

  List<GroupPlan> findByCode(String code);

  List<GroupPlan> findByCodeAndIdNot(String code, Long id);
}
