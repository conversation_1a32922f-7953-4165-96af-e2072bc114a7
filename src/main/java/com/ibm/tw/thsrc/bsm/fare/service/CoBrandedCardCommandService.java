/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.ConflictException;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardInput;
import com.ibm.tw.thsrc.bsm.fa.enums.ElectronicMoneyType;
import com.ibm.tw.thsrc.bsm.fare.domain.event.CoBrandedCardReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCard;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardStationRule;
import com.ibm.tw.thsrc.bsm.fare.entity.StationProjection;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardStationRuleRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.StationProjectionRepository;
import com.ibm.tw.thsrc.bsm.message.fare.CoBrandedCardData;
import com.ibm.tw.thsrc.bsm.message.fare.CoBrandedCardStationRuleData;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Transactional
@Service
public class CoBrandedCardCommandService extends
    AbstractCommandService<CoBrandedCard, CoBrandedCardInput> {

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private CoBrandedCardRepository repo;

  @Autowired
  private CoBrandedCardStationRuleRepository stationRuleRepo;

  @Autowired
  private StationProjectionRepository stationRepo;


  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;


  @Override
  public Long create(CoBrandedCardInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void update(Long id, CoBrandedCardInput input) {
    CoBrandedCard entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    validateConcurrency(entity, input.getDataVersion());
    writeAggregateRootValue(entity, input);
    validateInvariants(entity);
    repo.save(entity);

    publishReplacedEvent(translatePubMsg(entity));
  }

  @Override
  public void delete(Long id) {
    throw new UnsupportedOperationException();
  }

  @Override
  protected void validateInvariants(CoBrandedCard aggregateRoot) throws UnprocessableException {
    // no need
  }

  @Override
  protected void writeAggregateRootValue(CoBrandedCard entity, CoBrandedCardInput input) {
    checkoutMinLoadAmount(entity, input);
    entity.setAutoReloaded(input.getIsAutoReloaded());
    entity.setTransferBonusMinsLimit(input.getTransferBonusMinsLimit());
    entity.setSameStationEntryExitSecsLimit(input.getSameStationEntryExitSecsLimit());
    entity.setDiffStationEntryExitSecsLimit(input.getDiffStationEntryExitSecsLimit());

    List<CoBrandedCardStationRule> newStationRules = new ArrayList<>();
    input.getRoutes().forEach(r -> {

      CoBrandedCardStationRule stationRule;
      if (Objects.nonNull(r.getId())) {
        stationRule = entity.getRoutes().stream()
            .filter(route -> route.getId().equals(r.getId())).collect(Collectors.toList()).get(0);
        validateConcurrency(stationRule, r.getDataVersion());

      } else {
        stationRule = new CoBrandedCardStationRule();
        stationRule.setCoBrandedCard(entity);

        newStationRules.add(stationRule);
      }

      StationProjection station = stationRepo.findById(r.getStationId()).orElseThrow(
          FareErrorCode.FA_CO_BRANDED_CARD_CANNOT_ASSOCIATE_TO_THE_STATION_PROJECTION::toException);
      stationRule.setStation(station);
      stationRule.setMinEntryLoadAmount(r.getMinEntryLoadAmount());
    });

    entity.getRoutes().addAll(newStationRules);
  }

  protected void validateConcurrency(CoBrandedCardStationRule aggregateRoot, Long inputVersion) {
    if (!aggregateRoot.getDataVersion().equals(inputVersion)) {
      throw new ConflictException();
    }

    // force JPA to update data-version of aggregate-root even no property changed
    aggregateRoot.setUpdateTimestamp(ZonedDateTime.now());
  }

  private void checkoutMinLoadAmount(CoBrandedCard entity, CoBrandedCardInput input) {
    if (entity.getElectronicMoneyType().equals(ElectronicMoneyType.EASY_CARD)) {
      int notFilled = input.getRoutes().stream()
          .mapToInt(e -> Objects.nonNull(e.getMinEntryLoadAmount()) ? 0 : 1).sum();
      if (notFilled > 0) {
        throw FareErrorCode.FA_CO_BRANDED_CARD_MIN_ENTRY_LOAD_AMOUNT_CANNOT_BE_NULL_WHEN_CARD_TYPE_IS_EASY_CARD.toException();
      }
    }

  }

  private CoBrandedCardData translatePubMsg(CoBrandedCard entity) {
    CoBrandedCardData msg = new CoBrandedCardData();

    msg.setIsAutoReloaded(entity.isAutoReloaded());
    msg.setTransferBonusMinsLimit(entity.getTransferBonusMinsLimit());
    msg.setSameStationEntryExitSecsLimit(entity.getSameStationEntryExitSecsLimit());
    msg.setDiffStationEntryExitSecsLimit(entity.getDiffStationEntryExitSecsLimit());
    msg.setElectronicMoneyType(entity.getElectronicMoneyType().toString());

    List<CoBrandedCardStationRuleData> routes = new ArrayList<>();
    entity.getRoutes().forEach(r -> {
      CoBrandedCardStationRuleData route = new CoBrandedCardStationRuleData();
      route.setStationCode(r.getStation().getCode());
      route.setStationEnName(r.getStation().getEnName());
      route.setStationZhName(r.getStation().getZhName());
      route.setStationZhPrintName(r.getStation().getZhPrintName());
      route.setDisplayOrder(r.getStation().getDisplayOrder());
      route.setMinEntryLoadAmount(r.getMinEntryLoadAmount());

      routes.add(route);
    });

    msg.setRoutes(routes);
    return msg;
  }

  private void publishReplacedEvent(CoBrandedCardData msgPayload) {
    CoBrandedCardReplaced replaced = new CoBrandedCardReplaced(interceptor.getUserId(),
        interceptor.getCorrelationId());
    replaced.setMsgPayload(msgPayload);
    applicationEventPublisher.publishEvent(replaced);
  }
}
