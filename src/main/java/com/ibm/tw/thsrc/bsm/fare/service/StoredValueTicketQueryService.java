/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.StoredValueTicketOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.StoredValueTicket;
import com.ibm.tw.thsrc.bsm.fare.repository.StoredValueTicketRepository;
import java.util.ArrayList;
import java.util.List;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Transactional
@Service
public class StoredValueTicketQueryService {

  @Autowired
  private StoredValueTicketRepository repo;

  public StoredValueTicketOutput read(Long id) {
    StoredValueTicket entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    return translate(entity);
  }

  public Page<StoredValueTicketOutput> search() {
    Pageable pageable = PageRequest.of(0, Integer.MAX_VALUE);
    Page<StoredValueTicket> pages = repo.findAll(pageable);
    return pages.map(this::translate);
  }

  private StoredValueTicketOutput translate(StoredValueTicket entity) {
    StoredValueTicketOutput response = new StoredValueTicketOutput();
    response.setDataVersion(entity.getDataVersion());
    response.setId(entity.getId());
    response.setExpiryType(entity.getExpiryType());
    response.setRefundFeeAmt(entity.getRefundFeeAmt());
    response.setDiscountPct(entity.getDiscountPct());
    response.setMinAmt(entity.getMinAmt());
    response.setMaxAmt(entity.getMaxAmt());
    response.setMaxAmtPerTxn(entity.getMaxAmtPerTxn());
    response.setMaxAmtPerLoad(entity.getMaxAmtPerLoad());
    response.setMagnetTktValidDays(entity.getMagnetTktValidDays());

    List<Integer> amount = new ArrayList<>();
    entity.getLoadAmount().forEach(loadAmt -> amount.add(loadAmt.getLoadAmt().intValue()));
    response.setLoadAmount(amount);

    return response;
  }

}
