/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source
 * code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.ConflictException;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.FreeSeatingProfileDiscountInput;
import com.ibm.tw.thsrc.bsm.fa.dto.FreeSeatingTicketTypeInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PassengerProfileTbLightColorInput;
import com.ibm.tw.thsrc.bsm.fa.dto.ProfileDiscountPlanInput;
import com.ibm.tw.thsrc.bsm.fare.domain.event.BatchFreeSeatingTicketTypeReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.FreeSeatingProfileDiscount;
import com.ibm.tw.thsrc.bsm.fare.entity.FreeSeatingTicketType;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerProfile;
import com.ibm.tw.thsrc.bsm.fare.repository.FreeSeatingProfileDiscountRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.FreeSeatingTicketTypeRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PassengerProfileRepository;

@Transactional
@Service
public class FreeSeatingProfileDiscountCommandService {

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Autowired
  private FreeSeatingProfileDiscountRepository freeSeatingProfileDiscountRepository;

  @Autowired
  private FreeSeatingTicketTypeRepository freeSeatingTicketTypeRepository;

  @Autowired
  private PassengerProfileRepository passengerProfileRepository;


  public void replace(FreeSeatingProfileDiscountInput input) {
    input.getFreeSeatingProfileDiscounts().stream().forEach(this::updateProfileDiscount);

    input.getFreeSeatingTicketTypes().stream().forEach(this::updateDiscountType);

    input.getProfileTbLights().stream().forEach(this::updatePassengerProfileName);

    BatchFreeSeatingTicketTypeReplaced event = new BatchFreeSeatingTicketTypeReplaced(
        interceptor.getUserId(), interceptor.getCorrelationId());
    event.setOperationId(OperationFunction.FREE_SEATING_PROFILE_DISCOUNT.name());
    event.setAssociationId(interceptor.getCorrelationId());
    applicationEventPublisher.publishEvent(event);
  }

  private void updatePassengerProfileName(PassengerProfileTbLightColorInput input) {
    PassengerProfile aggregateRoot = passengerProfileRepository.findById(input.getId())
        .orElseThrow(ResourceNotFoundException::new);
    if (!aggregateRoot.getDataVersion().equals(input.getDataVersion())) {
      throw new ConflictException();
    }

    aggregateRoot.setTbLightColor(input.getTbLightColor());
  }

  private void updateDiscountType(FreeSeatingTicketTypeInput input) {
    FreeSeatingTicketType aggregateRoot = freeSeatingTicketTypeRepository.findById(input.getId())
        .orElseThrow(ResourceNotFoundException::new);
    if (!aggregateRoot.getDataVersion().equals(input.getDataVersion())) {
      throw new ConflictException();
    }

    aggregateRoot.setName(input.getName());
  }

  private void updateProfileDiscount(ProfileDiscountPlanInput input) {
    FreeSeatingProfileDiscount aggregateRoot = freeSeatingProfileDiscountRepository
        .findById(input.getId()).orElseThrow(ResourceNotFoundException::new);
    if (!aggregateRoot.getDataVersion().equals(input.getDataVersion())) {
      throw new ConflictException();
    }
    aggregateRoot.setDiscountPct(input.getDiscountPct());
  }

}
