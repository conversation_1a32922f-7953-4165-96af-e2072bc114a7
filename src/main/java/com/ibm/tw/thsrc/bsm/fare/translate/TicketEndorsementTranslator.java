/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.translate;

import com.ibm.tw.thsrc.bsm.fa.dto.TicketEndorsementOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.TicketEndorsement;

public class TicketEndorsementTranslator {

  private TicketEndorsementTranslator() {
    throw new IllegalStateException("TicketEndorsementTranslator class");
  }

  public static TicketEndorsementOutput toTicketEndorsementOutput(TicketEndorsement entity) {
    TicketEndorsementOutput response = new TicketEndorsementOutput();
    response.setId(entity.getId());
    response.setType(entity.getType());
    response.setEnName(entity.getEnName());
    response.setZhName(entity.getZhName());
    response.setEffDate(entity.getEffDate());
    response.setDataVersion(entity.getDataVersion());
    return response;
  }
}
