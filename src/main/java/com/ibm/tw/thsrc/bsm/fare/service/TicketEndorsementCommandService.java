/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.SharedErrorCode;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketEndorsementInput;
import com.ibm.tw.thsrc.bsm.fare.domain.event.TicketEndorsementReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.TicketEndorsement;
import com.ibm.tw.thsrc.bsm.fare.repository.TicketEndorsementRepository;
import com.ibm.tw.thsrc.bsm.message.fare.TicketEndorsementData;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class TicketEndorsementCommandService extends
    AbstractCommandService<TicketEndorsement, TicketEndorsementInput> {

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private TicketEndorsementRepository repo;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Override
  public Long create(TicketEndorsementInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void update(Long id, TicketEndorsementInput input) {
    throw new UnsupportedOperationException();
  }

  public Set<Long> patch(CollectionModificationInput<TicketEndorsementInput> changeSet) {
    List<TicketEndorsementData> msgList = new ArrayList<>();

    changeSet.getReplacements().entrySet().stream()//
        .forEach(inputById -> {

          TicketEndorsement entity =
              repo.findById(inputById.getKey())
                  .orElseThrow(() -> SharedErrorCode.COLLECTION_PATCH_INPUT_ID_NOT_EXIST
                      .toException(String.valueOf(inputById)));
          this.validateConcurrency(entity, inputById.getValue().getDataVersion());
          this.writeAggregateRootValue(entity, inputById.getValue());
          validateInvariants(entity);
          msgList.add(translatePubMsg(entity));
          repo.save(entity);
        });

    Set<Long> createdIds = changeSet.getCreations().stream().map(input -> {
      TicketEndorsement newEntity = new TicketEndorsement();
      this.writeAggregateRootValue(newEntity, input);
      this.validateInvariants(newEntity);
      msgList.add(translatePubMsg(newEntity));
      TicketEndorsement entity = repo.save(newEntity);
      return entity.getId();
    }).collect(Collectors.toSet());

    if(!msgList.isEmpty()){

      this.publishReplacedEvent(msgList);
    }

    return createdIds;
  }

  @Override
  public void delete(Long id) {
    throw new UnsupportedOperationException();
  }

  @Override
  protected void validateInvariants(TicketEndorsement aggregateRoot) throws UnprocessableException {
    // not needed for now
  }

  @Override
  protected void writeAggregateRootValue(TicketEndorsement entity,
      TicketEndorsementInput input) {
    entity.setType(input.getType());
    entity.setEnName(input.getEnName());
    entity.setZhName(input.getZhName());
    entity.setEffDate(input.getEffDate());
  }

  private TicketEndorsementData translatePubMsg(TicketEndorsement entity) {
    TicketEndorsementData msg = new TicketEndorsementData();
    msg.setEnName(entity.getEnName());
    msg.setZhName(entity.getZhName());
    msg.setType(entity.getType().code);
    msg.setEffDate(entity.getEffDate());
    return msg;
  }

  private void publishReplacedEvent(List<TicketEndorsementData> msgPayload) {
    TicketEndorsementReplaced replaced = new TicketEndorsementReplaced(interceptor.getUserId(),
        interceptor.getCorrelationId());
    replaced.setMsgPayload(msgPayload);
    applicationEventPublisher.publishEvent(replaced);
  }
}
