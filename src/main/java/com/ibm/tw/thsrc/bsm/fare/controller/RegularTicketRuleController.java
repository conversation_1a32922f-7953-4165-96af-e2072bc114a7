/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.event.ExceptionMessages;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.fa.api.RegularTicketRuleApi;
import com.ibm.tw.thsrc.bsm.fa.dto.RegularTicketRuleInput;
import com.ibm.tw.thsrc.bsm.fa.dto.RegularTicketRuleOutput;
import com.ibm.tw.thsrc.bsm.fare.service.RegularTicketRuleCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.RegularTicketRuleQueryService;
import java.util.List;
import java.util.Set;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/RegularTicketRules")
public class RegularTicketRuleController implements RegularTicketRuleApi {

  @Autowired
  private RegularTicketRuleCommandService commandService;

  @Autowired
  private RegularTicketRuleQueryService queryService;

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private EventStore eventStore;

  @Autowired
  private ExceptionMessages exceptionMessages;


  @Override
  public Page<RegularTicketRuleOutput> search(Search searchRequest) {
    return queryService.search(searchRequest);
  }

  @Override
  public RegularTicketRuleOutput upsert(RegularTicketRuleInput changeInput) {
    eventStore.validateFunctionLock(OperationFunction.REGULAR_TICKET);
    Pair<List<Long>, Set<Long>> ids = commandService.upsert(changeInput);
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
    return queryService.read(ids);
  }

}
