/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainTemp;
import java.time.LocalDate;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface EligibleTrainTempRepository extends
    JpaRepository<EligibleTrainTemp, Long> {

  List<EligibleTrainTemp> findByFarePlanCodeAndEffSaleDate(String farePlanCode,
      LocalDate effSaleDate);

}
