/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.fa.api.FareEvaluationApi;
import com.ibm.tw.thsrc.bsm.fa.dto.FareEvaluationInput;
import com.ibm.tw.thsrc.bsm.fa.dto.FareEvaluationOutput;
import com.ibm.tw.thsrc.bsm.fare.service.FareEvaluationQueryService;
import java.util.List;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/FareEvaluations")

public class FareEvaluationController implements FareEvaluationApi {

  @Autowired
  private FareEvaluationQueryService queryService;

  @Override
  public List<FareEvaluationOutput> evaluate(@Valid FareEvaluationInput input) {
    return queryService.evaluate(input);
  }

}
