/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.entity.BaseEntity;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.operation.CollectionModificationOperation;
import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.SharedErrorCode;
import com.ibm.tw.thsrc.bsm.fa.dto.PassengerProfileInput;
import com.ibm.tw.thsrc.bsm.fare.domain.event.BatchPassengerProfileReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerProfile;
import com.ibm.tw.thsrc.bsm.fare.repository.PassengerProfileRepository;
import com.ibm.tw.thsrc.bsm.message.fare.PassengerProfileData;
import java.util.Collection;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Transactional
@Service
public class PassengerProfileCommandService extends
    AbstractCommandService<PassengerProfile, PassengerProfileInput> {

  @Autowired
  private PassengerProfileRepository passengerProfileRepository;

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private EventStore eventStore;

  public Set<Long> patch(CollectionModificationInput<PassengerProfileInput> changeSet) {

    Collection<PassengerProfile> passengerProfilesModified = CollectionModificationOperation
        .modify(
            passengerProfileRepository,
            changeSet,
            it -> {
              throw new UnsupportedOperationException("doest not support creation");
            },
            this::doUpdateEntity,
            idNotFound -> {
              throw SharedErrorCode.COLLECTION_PATCH_INPUT_ID_NOT_EXIST
                  .toException(String.valueOf(idNotFound));
            }
        );

    BatchPassengerProfileReplaced event = new BatchPassengerProfileReplaced(
        interceptor.getUserId(),
        interceptor.getCorrelationId(),
        passengerProfilesModified.stream()
            .map(it -> new PassengerProfileData(
                it.getId(),
                it.getCode(),
                it.getZhName(),
                it.getZhPrintName(),
                it.getEnName(),
                it.getEnPrintName(),
                it.getTbLightColor()
            ))
            .collect(Collectors.toList())
    );

    event.setOperationId(OperationFunction.PASSENGER_PROFILE_DISCOUNT.name());
    event.setAssociationId(interceptor.getCorrelationId());
    eventStore.publishEvent(event);

    return passengerProfilesModified
        .stream()
        .map(BaseEntity::getId)
        .collect(Collectors.toSet());
  }

  @Override
  public Long create(PassengerProfileInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void update(Long id, PassengerProfileInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void delete(Long id) {
    throw new UnsupportedOperationException();
  }

  @Override
  protected void validateInvariants(PassengerProfile aggregateRoot) {
    // not needed for the moment
  }

  @Override
  protected void writeAggregateRootValue(PassengerProfile aggregateRoot,
      PassengerProfileInput input) {
    aggregateRoot.setZhName(input.getZhName());
    aggregateRoot.setZhPrintName(input.getZhPrintName());
    aggregateRoot.setEnName(input.getEnName());
    aggregateRoot.setEnPrintName(input.getEnPrintName());
    aggregateRoot.setTbLightColor(input.getTbLightColor());
  }

  private void doUpdateEntity(PassengerProfile aggregateRoot,
      PassengerProfileInput input) {
    this.validateConcurrency(aggregateRoot, input.getDataVersion());
    this.writeAggregateRootValue(aggregateRoot, input);
  }

}
