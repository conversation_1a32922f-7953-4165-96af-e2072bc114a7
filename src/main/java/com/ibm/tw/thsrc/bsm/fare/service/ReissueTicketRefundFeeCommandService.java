/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.ReissueTicketRefundFeeInput;
import com.ibm.tw.thsrc.bsm.fare.domain.event.ReissueTicketRefundFeeUpserted;
import com.ibm.tw.thsrc.bsm.fare.entity.ReissueTicketRefundFee;
import com.ibm.tw.thsrc.bsm.fare.repository.ReissueTicketRefundFeeJpaRepository;
import com.ibm.tw.thsrc.bsm.message.fare.ReissueTicketRefundFeeData;
import java.util.Objects;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class ReissueTicketRefundFeeCommandService extends
    AbstractCommandService<ReissueTicketRefundFee, ReissueTicketRefundFeeInput> {

  @Autowired
  private HttpHeaderInterceptor interceptor;
  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Autowired
  private ReissueTicketRefundFeeJpaRepository reissueRepo;

  public void upsert(ReissueTicketRefundFeeInput input) {

    ReissueTicketRefundFee reissueTicket =
        input.getId() == null ? new ReissueTicketRefundFee()
            : reissueRepo.findById(input.getId())
                .orElseThrow(ResourceNotFoundException::new);
    if (Objects.nonNull(reissueTicket.getDataVersion())) {
      this.validateConcurrency(reissueTicket, input.getDataVersion());
    }
    writeAggregateRootValue(reissueTicket, input);
    this.validateInvariants(reissueTicket);

    ReissueTicketRefundFeeData payload = translateMsg(reissueTicket);
    ReissueTicketRefundFeeUpserted event = new ReissueTicketRefundFeeUpserted(
        interceptor.getUserId(), interceptor.getCorrelationId());
    event.setMsgPayload(payload);
    applicationEventPublisher.publishEvent(event);

    reissueRepo.save(reissueTicket);
  }


  @Override
  protected void validateInvariants(ReissueTicketRefundFee aggregateRoot)
      throws UnprocessableException {
    // no logic now
  }

  @Override
  protected void writeAggregateRootValue(ReissueTicketRefundFee entity,
      ReissueTicketRefundFeeInput input) {

    entity.setEffDate(input.getEffDate());
    entity.setFeePct(input.getFeePct());
    entity.setIsAdditionalCharges(input.getIsAdditionalCharges());
  }


  private ReissueTicketRefundFeeData translateMsg(ReissueTicketRefundFee reissueTicket) {

    ReissueTicketRefundFeeData msg = new ReissueTicketRefundFeeData();
    msg.setEffDate(reissueTicket.getEffDate());
    msg.setFeePct(reissueTicket.getFeePct());
    msg.setIsAdditionalCharges(reissueTicket.getIsAdditionalCharges());

    return msg;
  }


  @Override
  public Long create(ReissueTicketRefundFeeInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void update(Long id, ReissueTicketRefundFeeInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void delete(Long id) {
    throw new UnsupportedOperationException();
  }
}
