/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.MultiRideTicketOptionDto;
import com.ibm.tw.thsrc.bsm.fa.dto.MultiRideTicketOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.MultiRideTicket;
import com.ibm.tw.thsrc.bsm.fare.repository.MultiRideTicketRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.TypedSort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

/**
 * 回數票 Query Service
 */
@Service
public class MultiRideTicketQueryService extends AbstractQueryService<MultiRideTicket, MultiRideTicketOutput, Void, Void> {

  @Autowired
  MultiRideTicketRepository repo;

  @Override
  public MultiRideTicketOutput read(Long id) {
    return repo.findById(id)
        .map(this::translate)
        .orElseThrow(ResourceNotFoundException::new);
  }

  @Override
  public Page<MultiRideTicketOutput> search(Search search) {
    PageRequest pr = translateToPageRequest(search);
    Specification<MultiRideTicket> spec = translateToSpecification(search);
    return repo
        .findAll(spec, pr)
        .map(this::translate);
  }

  @Override
  protected MultiRideTicketOutput translate(MultiRideTicket aggregateRoot) {
    MultiRideTicketOutput output = new MultiRideTicketOutput();

    output.setId(aggregateRoot.getId());
    output.setDataVersion(aggregateRoot.getDataVersion());

    output.setRefundFeeAmt(aggregateRoot.getRefundFeeAmt());
    output.setExpiryType(aggregateRoot.getExpiryType());

    List<MultiRideTicketOptionDto> options = new ArrayList<>();
    aggregateRoot.getMultiRideTicketOptions().stream().forEach(o -> {
      MultiRideTicketOptionDto option = new MultiRideTicketOptionDto();

      option.setId(o.getId());
      option.setDataVersion(o.getDataVersion());

      option.setRides(o.getRides());
      option.setDiscountPct(o.getDiscountPct());
      option.setIsAppSale(o.getIsAppSale());
      option.setAppEffSaleDate(o.getAppEffSaleDate());
      option.setAppDscSaleDate(o.getAppDscSaleDate());
      option.setValidDays(o.getValidDays());

      options.add(option);
    });
    output.setOptions(options);

    return output;
  }

  @Override
  protected Sort translate(Void sortBy, Direction sortDirection) {
    TypedSort<MultiRideTicket> typedSort = Sort.sort(MultiRideTicket.class);
    typedSort.by(MultiRideTicket::getId);
    return typedSort.ascending();
  }

  @Override
  protected Predicate translate(ComparisonExpression<Void> f, Root<MultiRideTicket> root,
      CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    return null;
  }

  @Override
  protected Function<Map<String, Boolean>, Void> getSortByTranslator() {
    return data -> null;
  }

  @Override
  protected Function<Map<String, Object>, Void> getFilterAttributeValueTranslator() {
    return data -> null;
  }

}
