
/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source
 * code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryMappingOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryMappingValidationDetail;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryValidationInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryValidationOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionProjectOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.FareProjectType;
import com.ibm.tw.thsrc.bsm.fare.entity.FarePlan_;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProjectMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionCategory;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionCategoryMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionCategoryMapping_;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan;
import com.ibm.tw.thsrc.bsm.fare.repository.FareProjectMappingRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionCategoryMappingRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionCategoryRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.PromotionCategoryMappingFilter;
import com.ibm.tw.thsrc.bsm.fare.vo.PromotionCategoryMappingSort;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.TypedSort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class PromotionCategoryMappingQueryService extends
    AbstractQueryService<PromotionCategoryMapping, PromotionCategoryMappingOutput, PromotionCategoryMappingFilter, PromotionCategoryMappingSort>
    implements PromotionCategoryMappingQueryApi {

  @Autowired
  private ObjectMapper objectMapper;
  @Autowired
  private PromotionCategoryMappingRepository repo;
  @Autowired
  private PromotionCategoryRepository categoryRepo;
  @Autowired
  private FareProjectMappingRepository projectMappingRepo;

  @Override
  public PromotionCategoryMappingOutput read(Long id) {
    PromotionCategoryMapping entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    return this.translate(entity);
  }

  public Set<PromotionCategoryMappingOutput> read(Set<Long> ids) {
    return repo.findAllById(ids).stream().map(this::translate).collect(Collectors.toSet());
  }

  @Override
  public Page<PromotionCategoryMappingOutput> search(Search searchRequest) {
    PageRequest pr = translateToPageRequest(searchRequest);
    Specification<PromotionCategoryMapping> spec = translateToSpecification(searchRequest);
    Page<PromotionCategoryMapping> page = repo.findAll(spec, pr);
    return page.map(this::translate);
  }

  public PromotionCategoryValidationOutput validate(
      PromotionCategoryValidationInput validationInput) {
    PromotionCategoryValidationOutput response = new PromotionCategoryValidationOutput();
    List<PromotionCategoryMapping> categoryMappings;

    if (Objects.isNull(validationInput.getCategoryId())) {
      categoryMappings = repo.findAll(Sort.sort(PromotionCategoryMapping.class)
          .by(PromotionCategoryMapping::getCategory).by(PromotionCategory::getDisplayOrder));

    } else {
      PromotionCategory category = categoryRepo.findById(validationInput.getCategoryId())
          .orElseThrow(ResourceNotFoundException::new);
      categoryMappings = repo.findByCategory(category);
    }

    if (categoryMappings.isEmpty()) {
      return response;
    }
    Pair<LocalDate, LocalDate> recentFutureEffDate = getRecentFutureEffDate();
    if (Objects.nonNull(recentFutureEffDate.getRight())) {
      response.setFutureEffDate(recentFutureEffDate.getRight());
      response.setFutureMapping(categoryMappings.stream()
          .filter(mapping -> mapping.getEffDate().isEqual(recentFutureEffDate.getRight()))
          .map(this::translateValidation).collect(Collectors.toList()));
    }
    if (Objects.nonNull(recentFutureEffDate.getLeft())) {
      response.setCurrentEffDate(recentFutureEffDate.getLeft());
      response.setCurrentMapping(categoryMappings.stream()
          .filter(mapping -> mapping.getEffDate().isEqual(recentFutureEffDate.getLeft())
              && (Objects.isNull(mapping.getPromotionPlan().getDscDate())
                  || !mapping.getPromotionPlan().getDscDate().isBefore(LocalDate.now())))
          .map(this::translateValidation).collect(Collectors.toList()));
    }

    return response;
  }

  @Override
  protected PromotionCategoryMappingOutput translate(PromotionCategoryMapping entity) {
    PromotionCategoryMappingOutput response = new PromotionCategoryMappingOutput();
    response.setId(entity.getId());
    response.setEffDate(entity.getEffDate());
    response.setDataVersion(entity.getDataVersion());
    response.setIsTweDisplay(entity.getIsTweDisplay());
    response.setIsEcpsNeed(entity.getIsEcpsNeed());
    response.setPromotionCategoryId(entity.getCategory().getId());
    response.setPromotionCategoryName(entity.getCategory().getName());
    response.setPromotionCategoryDisplayOrder(entity.getCategory().getDisplayOrder());
    response.setPromotionId(entity.getPromotionPlan().getId());
    response.setPromotionCode(entity.getPromotionPlan().getCode());
    response.setPromotionDscDate(entity.getPromotionPlan().getDscDate());
    if (Objects.nonNull(entity.getPromotionPlan().getDiscountPct())) {
      response.setPromotionDiscountPct(entity.getPromotionPlan().getDiscountPct());
    }
    if (Objects.nonNull(entity.getPromotionPlan().getDiscountAmt())) {
      response.setPromotionDiscountAmt(entity.getPromotionPlan().getDiscountAmt());
    }

    List<PromotionProjectOutput> projectOutputs = new ArrayList<>();
    // 標準廂
    setPromotionProject(entity.getPromotionPlan(), FareProjectType.STANDARD, projectOutputs);
    // 商務廂
    setPromotionProject(entity.getPromotionPlan(), FareProjectType.BUSINESS, projectOutputs);

    response.setProjects(projectOutputs);
    return response;
  }

  private void setPromotionProject(PromotionPlan promotion, FareProjectType projectType,
      List<PromotionProjectOutput> projectOutputs) {
    List<FareProjectMapping> projectMappings =
        projectMappingRepo.findByPromotionPlanAndFareProject_Type(promotion, projectType);

    FareProject project = null;

    // 只有一筆->直接用
    if (projectMappings.size() == 1) {
      project = projectMappings.get(0).getFareProject();
    }

    // 有多筆->找成人的（即沒有身份別）
    if (projectMappings.size() > 1) {
      List<FareProjectMapping> noProfileProject = projectMappings.stream()
          .filter(e -> Objects.isNull(e.getProfileDiscountPlan())).collect(Collectors.toList());

      if (noProfileProject.size() == 1) {
        project = noProfileProject.get(0).getFareProject();
      }

      // 多個成人，找條件最少的（即只有對應促銷碼）
      if (noProfileProject.size() > 1) {
        project = noProfileProject.stream().filter(e -> Objects.isNull(e.getBasicFarePlan()))
            .map(FareProjectMapping::getFareProject).findAny().orElse(null);
      }

      // 若是都沒有就選數字最小的專案（即都有身份別 or 都有對應票價方案)
      if (Objects.isNull(project)) {
        List<FareProjectMapping> leftMappings =
            noProfileProject.isEmpty() ? projectMappings : noProfileProject;
        project = leftMappings.stream().map(FareProjectMapping::getFareProject)
            .min(Comparator.comparing(FareProject::getCodeNumber)).orElse(null);
      }
    }

    if (Objects.nonNull(project)) {
      PromotionProjectOutput output = new PromotionProjectOutput();
      output.setType(project.getType());
      output.setCode(project.getCode());
      output.setName(project.getName());

      projectOutputs.add(output);
    }
  }

  protected PromotionCategoryMappingValidationDetail translateValidation(
      PromotionCategoryMapping entity) {
    PromotionCategoryMappingValidationDetail response = new PromotionCategoryMappingValidationDetail();
    response.setPromotionCode(entity.getPromotionPlan().getCode());
    List<PromotionProjectOutput> projectOutputs = new ArrayList<>();

    //促銷碼分類驗證 - 先選成人適用&條件最少的，再選其他
    // 標準廂
    setPromotionProject(entity.getPromotionPlan(), FareProjectType.STANDARD, projectOutputs);
    // 商務廂
    setPromotionProject(entity.getPromotionPlan(), FareProjectType.BUSINESS, projectOutputs);

    if (!projectOutputs.isEmpty()) {
      response.setProjectName(projectOutputs.get(0).getName());
    } else {
      response.setProjectName("");
    }

    return response;
  }

  @Override
  protected Sort translate(PromotionCategoryMappingSort sortBy, Direction direction) {
    TypedSort<PromotionCategoryMapping> typedSort = Sort.sort(PromotionCategoryMapping.class);

    if (sortBy.isPromotionCode()) {
      typedSort//
          .by(PromotionCategoryMapping::getPromotionPlan).by(PromotionPlan::getCode);
    } else if (sortBy.isDiscountPct()) {
      typedSort//
          .by(PromotionCategoryMapping::getPromotionPlan).by(PromotionPlan::getDiscountPct);
    } else if (sortBy.isDiscountAmt()) {
      typedSort//
          .by(PromotionCategoryMapping::getPromotionPlan).by(PromotionPlan::getDiscountAmt);
    } else if (sortBy.isTweDisplay()) {
      typedSort//
          .by(PromotionCategoryMapping::getIsTweDisplay);
    } else if (sortBy.isEcpsNeed()) {
      typedSort//
          .by(PromotionCategoryMapping::getIsEcpsNeed);
    } else if (sortBy.isDscDate()) {
      typedSort//
          .by(PromotionCategoryMapping::getPromotionPlan).by(PromotionPlan::getDscDate);

      // 預設＆使用 category 排序都用分類序號
    } else {
      typedSort
      .by(PromotionCategoryMapping::getPromotionPlan)
      .by(PromotionPlan::getCode)
      .and(typedSort
          .by(PromotionCategoryMapping::getCategory)
          .by(PromotionCategory::getDisplayOrder)
          );
    }

    if (typedSort.isSorted()) {
      if (Sort.Direction.DESC == direction) {
        return typedSort.descending();
      } else {
        return typedSort.ascending();
      }
    } else {
      return typedSort;
    }
  }

  @Override
  protected Predicate translate(ComparisonExpression<PromotionCategoryMappingFilter> expression,
      Root<PromotionCategoryMapping> root, CriteriaQuery<?> query,
      CriteriaBuilder criteriaBuilder) {
    ComparisonOperator op = expression.getOperator();

    if (expression.getAttributeValue().getIsFuture() != null) {
      Path<LocalDate> path = root.get(PromotionCategoryMapping_.EFF_DATE);
      boolean isFuture = expression.getAttributeValue().getIsFuture();
      Pair<LocalDate, LocalDate> recentFutureDate = getRecentFutureEffDate();
      op = ComparisonOperator.EQ;

      if (isFuture) {
        return translate(path, op, recentFutureDate.getRight(), criteriaBuilder);

      } else {
        Path<LocalDate> dscPath =
            root.join(PromotionCategoryMapping_.PROMOTION_PLAN).get(FarePlan_.DSC_DATE);

        Predicate effectivePromotion = criteriaBuilder.or(criteriaBuilder.isNull(dscPath),
            translate(dscPath, ComparisonOperator.GE, LocalDate.now(), criteriaBuilder));

        return criteriaBuilder.and(effectivePromotion,
            translate(path, op, recentFutureDate.getLeft(), criteriaBuilder));
      }

    } else if (expression.getAttributeValue().getIsTweDisplay() != null) {
      Path<Boolean> path = root.get(PromotionCategoryMapping_.IS_TWE_DISPLAY);
      boolean value = expression.getAttributeValue().getIsTweDisplay();
      return translate(path, op, value, criteriaBuilder);

    } else {
      return null;
    }
  }

  @Override
  protected Function<Map<String, Boolean>, PromotionCategoryMappingSort> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, PromotionCategoryMappingSort.class);
  }

  @Override
  protected Function<Map<String, Object>, PromotionCategoryMappingFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, PromotionCategoryMappingFilter.class);
  }

  private Pair<LocalDate, LocalDate> getRecentFutureEffDate() {
    List<PromotionCategoryMapping> products = repo.findAll();
    LocalDate futureDate = null;
    LocalDate recentDate = null;
    Optional<PromotionCategoryMapping> recent =
        products.stream().filter(e -> !e.getEffDate().isAfter(LocalDate.now()))
            .max(Comparator.comparing(PromotionCategoryMapping::getEffDate));

    if (recent.isPresent()) {
      recentDate = recent.get().getEffDate();
    }
    Optional<PromotionCategoryMapping> future =
        products.stream().filter(e -> e.getEffDate().isAfter(LocalDate.now()))
            .max(Comparator.comparing(PromotionCategoryMapping::getEffDate));

    if (future.isPresent()) {
      futureDate = future.get().getEffDate();
    }

    return Pair.of(recentDate, futureDate);
  }

  @Override
  public List<PromotionCategoryMappingOutput> getPromotionCategoryMapping(LocalDate effDate) {
    return repo.findByEffDate(effDate).stream().map(this::translate).collect(Collectors.toList());
  }
}
