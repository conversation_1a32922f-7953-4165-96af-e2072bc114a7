/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.event.ExceptionMessages;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.fa.api.ReservedSeatProfileDiscountApi;
import com.ibm.tw.thsrc.bsm.fa.dto.ReservedSeatProfileDiscountInput;
import com.ibm.tw.thsrc.bsm.fare.service.ReservedSeatProfileDiscountCommandService;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/ReservedSeatProfileDiscounts")
public class ReservedSeatProfileDiscountController implements ReservedSeatProfileDiscountApi {

  @Autowired
  private ReservedSeatProfileDiscountCommandService commandService;

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private EventStore eventStore;

  @Autowired
  private ExceptionMessages exceptionMessages;

  @Override
  public void replace(@Valid ReservedSeatProfileDiscountInput input) {
    eventStore.validateFunctionLock(OperationFunction.RESERVED_SEAT_PROFILE_DISCOUNT);
    commandService.replace(input);
    // notify the failed outbound operation if any associated event uncompleted.
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
  }

}
