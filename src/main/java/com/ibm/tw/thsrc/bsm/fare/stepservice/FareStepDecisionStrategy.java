/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.stepservice;

import com.ibm.tw.thsrc.bsm.cronjob.service.StepDecisionStrategy;
import com.ibm.tw.thsrc.bsm.cronjob.service.StepService;
import com.ibm.tw.thsrc.bsm.cronjob.value.TaskExecution;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

@RequiredArgsConstructor
public class FareStepDecisionStrategy implements StepDecisionStrategy {

  @Autowired
  private EligibleTrainStepService eligibleTrainStepService;

  @Autowired
  private RegularTicketRuleStepService regularTicketRuleStepService;
  @Autowired
  private CoBrandedCardOperatingParameterStepService coBrandedCardOperatingParameterStepService;

  @SuppressWarnings("rawtypes")
  @Override
  public Optional<StepService> decide(TaskExecution taskExecution) {

    String stepName = taskExecution.getStepName();

    if (StringUtils.equals("eligibleTrainJobStep", stepName)) {
      return Optional.of(eligibleTrainStepService);

    } else if (StringUtils.equals("regularTicketRuleJobStep", stepName)) {
      return Optional.of(regularTicketRuleStepService);

    } else if (StringUtils.equals("coBrandedCardOperatingParameterJobStep", stepName)) {
      return Optional.of(coBrandedCardOperatingParameterStepService);
    }

    return Optional.empty();
  }
}
