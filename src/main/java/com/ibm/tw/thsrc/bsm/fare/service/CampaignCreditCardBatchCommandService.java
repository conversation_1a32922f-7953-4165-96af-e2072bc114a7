/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source
 * code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationOutput;
import com.ibm.tw.thsrc.bsm.core.entity.BaseEntity;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.core.util.ExceptionUtils;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.SharedErrorCode;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardBatchInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardInfoInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CardInfoRemovalInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CreditCardWeekdayPromotionDto;
import com.ibm.tw.thsrc.bsm.fa.enums.CreditCardCampaignType;
import com.ibm.tw.thsrc.bsm.fare.domain.event.CampaignCreditCardInfoCreated;
import com.ibm.tw.thsrc.bsm.fare.domain.event.CampaignCreditCardInfoDeleted;
import com.ibm.tw.thsrc.bsm.fare.entity.CampaignCreditCardBatch;
import com.ibm.tw.thsrc.bsm.fare.entity.CampaignCreditCardInfo;
import com.ibm.tw.thsrc.bsm.fare.entity.CreditCardWeekdayPromotion;
import com.ibm.tw.thsrc.bsm.fare.repository.CampaignCreditCardBatchRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CampaignCreditCardInfoRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionPlanRepository;
import com.ibm.tw.thsrc.bsm.message.fare.CampaignCreditCardInfoData;
import com.ibm.tw.thsrc.bsm.message.fare.CampaignCreditCardInfoData.CampaignCreditCardInfoDataBuilder;
import com.ibm.tw.thsrc.bsm.res.connection.annotation.RESTransaction;
import com.ibm.tw.thsrc.bsm.util.BsmDateUtils;
import com.ibm.tw.thsrc.bsm.util.CollectionUtils;
import com.ibm.tw.thsrc.bsm.util.StringUtils;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Transactional(propagation = Propagation.REQUIRES_NEW)
@Service
public class CampaignCreditCardBatchCommandService
    extends AbstractCommandService<CampaignCreditCardBatch, CampaignCreditCardBatchInput> {

  private static final String BULK_ERROR_START = "Err:";
  private final Logger log = LoggerFactory.getLogger(this.getClass());
  @Autowired
  private HttpHeaderInterceptor interceptor;
  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;
  @Autowired
  private CampaignCreditCardBatchRepository batchRepo;
  @Autowired
  private CampaignCreditCardInfoRepository infoRepo;
  @Autowired
  private PromotionPlanRepository promotionPlanRepo;
  /**
   * 批量新增
   *
   * @param changeSet
   * @return
   */
  @Transactional(propagation = Propagation.REQUIRES_NEW)
  @RESTransaction
  public CollectionModificationOutput bulk(
      CollectionModificationInput<CampaignCreditCardBatchInput> changeSet) {
    CollectionModificationOutput bulkResult = new CollectionModificationOutput();

    /* 多筆銀行資料新增 */
    this.bulkCreate(changeSet.getCreations(), bulkResult);

    /* 多筆銀行資料刪除 form patch */
    this.bulkDelete(changeSet.getDeletions(), bulkResult);

    return bulkResult;
  }

  /**
   * 多筆銀行資料新增
   *
   * @param createInputs bulk.getCreation() input
   * @param bulkResult bulk 結果
   */
  private void bulkCreate(List<CampaignCreditCardBatchInput> createInputs,
      CollectionModificationOutput bulkResult) {

    // 搜集所有卡號input檢查用
    List<CampaignCreditCardInfoInput> allInput = new ArrayList<>();
    createInputs.stream()
        .forEach(batchInput -> allInput.addAll(batchInput.getCampaignCreditCardInfos()));

    // 逐筆建立批次紀錄
    createInputs.stream().forEach(batchInput -> {

      List<String> cardInfoResults = new ArrayList<>();
      List<CampaignCreditCardInfo> infos = new ArrayList<>();

      CampaignCreditCardBatch batchEntity = new CampaignCreditCardBatch();
      batchEntity.setImportDate(
          batchInput.getImportDate() != null ? batchInput.getImportDate() : LocalDate.now());
      batchEntity.setBankName(batchInput.getBankName());
      batchEntity.setCardNos(infos);

      // workaround: Local variable batchEntity defined in an enclosing scope must be final or
      // effectively final
      CampaignCreditCardBatch finalBatchEntity = batchEntity;
      batchInput.getCampaignCreditCardInfos().stream().forEach(cardInfo -> {
        try {
          // 檢核輸入值
          this.validateCardInput(cardInfo, allInput, infos);
          // 組裝卡號明細資訊
          CampaignCreditCardInfo infoEntity = new CampaignCreditCardInfo();
          infoEntity.setCardNo(cardInfo.getCardNo());
          infoEntity.setEffDate(cardInfo.getEffDate());
          infoEntity.setDscDate(cardInfo.getDscDate());
          infoEntity.setEffSaleDate(cardInfo.getEffSaleDate());
          infoEntity.setDscSaleDate(cardInfo.getDscSaleDate());
          infoEntity.setCreditCardCampaignType(cardInfo.getCreditCardCampaignType());

          // 標準卡友，處理促銷碼設定
          if (CreditCardCampaignType.STANDARD_PROMOTION
              .equals(cardInfo.getCreditCardCampaignType())) {
            infoEntity.setPromotions(getPromotions(cardInfo, infoEntity));
          }
          infoEntity.setBatch(finalBatchEntity);

          // 先發布RES新增事件，RES端成功才往下處理
          CampaignCreditCardInfoCreated created = new CampaignCreditCardInfoCreated(
              interceptor.getUserId(), interceptor.getCorrelationId());
          created.setMsgPayload(this.toMsgPayload(infoEntity));
          created.setOperationId(OperationFunction.BUSINESS_CARD.name());
          created.setAssociationId(interceptor.getCorrelationId());
          applicationEventPublisher.publishEvent(created);
          log.info(created.getMsgPayload().getPromotionCode());

          // RES成功，產生該筆導入成功訊息放入結果中
          cardInfoResults.add(this.generateOKDetail(cardInfo));
          // RES端處理成功才加入
          infos.add(infoEntity);
        } catch (Exception e) {
          // 處理異常，產生該筆導入失敗訊息放入結果中
          log.error(ExceptionUtils.getStackTrace(e));
          cardInfoResults.add(this.generateErrDetail(cardInfo, ExceptionUtils.getMessage(e)));
        }
      });
      // 有卡號清單的批次紀錄才寫入BSMDB，避免產生髒資料
      if (!CollectionUtils.isEmpty(batchEntity.getCardNos())) {
        batchEntity = batchRepo.save(batchEntity);
      }
      bulkResult.getCreations().addAll(cardInfoResults);
    });
  }

  /**
   * 多筆銀行資料刪除 form patch
   *
   * @param ids bulk.getDeletion() input
   * @param bulkResult bulk 結果
   */
  private void bulkDelete(Set<Long> ids, CollectionModificationOutput bulkResult) {
    // by 匯入批次刪除
    ids.forEach(id -> {

      CampaignCreditCardBatch batch = null;
      List<CampaignCreditCardInfo> needRemoveInfo = new ArrayList<>();

      try {
        batch = batchRepo.findById(id)
            .orElseThrow(() -> SharedErrorCode.COLLECTION_BULK_INPUT_ID_NOT_EXIST
                .toException(String.valueOf(id)));

        batch.getCardNos().forEach(info -> {

          try {
            // 先發布RES刪除事件，RES端成功才往下處理
            CampaignCreditCardInfoDeleted event = new CampaignCreditCardInfoDeleted(
                interceptor.getUserId(), interceptor.getCorrelationId());

            event.setMsgPayload(this.toMsgPayload(info));
            event.setAssociationId(interceptor.getCorrelationId());
            applicationEventPublisher.publishEvent(event);

            needRemoveInfo.add(info);
            bulkResult.getDeletions().put(info.getId(), this.generateOKDetail(info));

          } catch (Exception e) {
            // 處理異常，產生該筆導入失敗訊息放入結果中
            log.error(ExceptionUtils.getStackTrace(e));
            bulkResult.getDeletions().put(info.getId(),
                this.generateErrDetail(info, ExceptionUtils.getMessage(e)));
          }

        });

        // id 查找過程有錯誤時
      } catch (Exception e) {
        // 處理異常，產生該筆導入失敗訊息放入結果中
        log.error(ExceptionUtils.getStackTrace(e));

        bulkResult.getDeletions().put(id, this.generateErrDetail(id, ExceptionUtils.getMessage(e)));
      }

      // 有找到＆有成功刪除時
      if (Objects.nonNull(batch) && !needRemoveInfo.isEmpty()) {
        batch.getCardNos().removeAll(needRemoveInfo);

        // 全空時刪除
        if (batch.getCardNos().isEmpty()) {
          batchRepo.delete(batch);

          // 有刪失敗時，存回去
        } else {
          batchRepo.saveAndFlush(batch);
        }
      }

    });
  }


  /**
   * 設定促銷碼
   *
   * @param infoInput input
   * @param infoEntity entity
   */
  private List<CreditCardWeekdayPromotion> getPromotions(CampaignCreditCardInfoInput infoInput,
      CampaignCreditCardInfo infoEntity) {

    List<CreditCardWeekdayPromotion> promotions = new ArrayList<>();

    infoInput.getCreditCardWeekdayPromotion().stream().forEach(promotionDto -> {
      CreditCardWeekdayPromotion promotion = new CreditCardWeekdayPromotion();
      if (promotionDto.getPromotionId() != null) {
        promotionPlanRepo.findById(promotionDto.getPromotionId())
            .ifPresent(promotion::setPromotionPlan);
      }
      promotion.setDayOfWeek(promotionDto.getDayOfWeek());
      promotion.setCreditCardInfo(infoEntity);
      promotions.add(promotion);
    });
    return promotions;
  }

  @Override
  public Long create(CampaignCreditCardBatchInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void update(Long id, CampaignCreditCardBatchInput input) {
    throw new UnsupportedOperationException();
  }


  @Override
  public void delete(Long id) {
    throw new UnsupportedOperationException();
  }

  public void clean(LocalDate expiryDate) {
    Set<Long> ids = batchRepo.findAll().stream()
        .filter(batch -> batch.getCardNos().stream()
            .allMatch(info -> info.getDscDate() != null && info.getDscDate().isBefore(expiryDate)))
        .map(BaseEntity::getId).collect(Collectors.toSet());

    List<CampaignCreditCardBatch> deleted = batchRepo.findAllById(ids);
    batchRepo.deleteAll(deleted);
  }

  /**
   * 檢核卡號輸入值
   *
   * @param card
   */
  protected void validateCardInput(CampaignCreditCardInfoInput card,
      List<CampaignCreditCardInfoInput> allInputs, List<CampaignCreditCardInfo> infos) {
    // 卡號前六碼不可重複
    this.validateCardNo(card, allInputs);
    // 起訖日與現有紀錄交疊 不可在BSMDB重複建立
    this.validateOverlap(card, infos);
    // 依照促銷卡別做檢核
    this.validateByCampaignType(card);
  }

  /**
   * 相同卡號 + 相同CampaignType + 訂位起訖日與現有紀錄交疊 不可在BSMDB重複建立
   *
   * @param card input
   */
  private void validateOverlap(CampaignCreditCardInfoInput card,
      List<CampaignCreditCardInfo> infos) {
    Optional<List<CampaignCreditCardInfo>> optional = infoRepo
        .findByCardNoAndCreditCardCampaignType(card.getCardNo(), card.getCreditCardCampaignType());
    if (optional.isPresent()) {
      optional.get().stream().forEach(info -> checkOverlap(card, info));
    }
    // UAT#25080 同批準備寫入BSMDB的紀錄也須納入比對對象
    List<CampaignCreditCardInfo> filteredInfos =
        infos.stream().filter(info -> StringUtils.equals(card.getCardNo(), info.getCardNo()))
            .collect(Collectors.toList());
    for (CampaignCreditCardInfo info : filteredInfos) {
      checkOverlap(card, info);
    }
  }

  private void checkOverlap(CampaignCreditCardInfoInput card, CampaignCreditCardInfo info) {
    // 截止日未輸入代表無限期
    LocalDate cardDscSaleDate = this.getDateOrDefault(card.getDscSaleDate(), LocalDate.MAX);
    LocalDate infoDscSaleDate = this.getDateOrDefault(info.getDscSaleDate(), LocalDate.MAX);

    // 2022年規格，同卡號搭乘日期可重複，訂位日期不可重複。
    if (BsmDateUtils.isOverlap(card.getEffSaleDate(), cardDscSaleDate, info.getEffSaleDate(),
        infoDscSaleDate, true)) {
      throw FareErrorCode.FA_CREDIT_CARD_CARD_INFO_EXIST_IN_BSM_DB.toException(card.getCardNo());
    }
  }

  /**
   * 日期若為空則回傳指定的預設值 (for SonarQube rule)
   *
   * @param date
   * @param defaultDate
   * @return
   */
  private LocalDate getDateOrDefault(LocalDate date, LocalDate defaultDate) {
    if (date == null) {
      return defaultDate;
    }
    return date;
  }

  /**
   * 依照促銷卡別做檢核
   *
   * @param card input
   */
  private void validateByCampaignType(CampaignCreditCardInfoInput card) {
    // 取得促銷卡別
    CreditCardCampaignType campaignType = card.getCreditCardCampaignType();

    // 標準卡友相關檢核
    if (CreditCardCampaignType.STANDARD_PROMOTION.equals(campaignType)) {
      // 需提供一週七天的促銷碼設定
      if (card.getCreditCardWeekdayPromotion() == null
          || card.getCreditCardWeekdayPromotion().size() != 7) {
        throw FareErrorCode.FA_CREDIT_CARD_ILLEGAL_WEEKDAY_PROMOTION_PLAN
            .toException(card.getCardNo());
      }
      // 至少一天有指定促銷碼
      Boolean isPromotionPlanAllNull = true;
      for (CreditCardWeekdayPromotionDto p : card.getCreditCardWeekdayPromotion()) {
        if (p.getPromotionId() != null) {
          isPromotionPlanAllNull = false;
          break;
        }
      }
      if (Boolean.TRUE.equals(isPromotionPlanAllNull)) {
        throw FareErrorCode.FA_CREDIT_CARD_ILLEGAL_WEEKDAY_PROMOTION_PLAN
            .toException(card.getCardNo());
      }
    }
  }

  @Override
  protected void validateInvariants(CampaignCreditCardBatch aggregateRoot)
      throws UnprocessableException {
    // 卡號清單不可為空
    if (aggregateRoot.getCardNos().isEmpty()) {
      throw FareErrorCode.FA_CREDIT_CARD_BATCH_NO_CARD_NO_EXISTS
          .toException(aggregateRoot.getBankName(), aggregateRoot.getImportDate().toString());
    }
  }

  @Override
  protected void writeAggregateRootValue(CampaignCreditCardBatch aggregateRoot,
      CampaignCreditCardBatchInput input) {

    aggregateRoot
        .setImportDate(input.getImportDate() != null ? input.getImportDate() : LocalDate.now());
    aggregateRoot.setBankName(input.getBankName());

    List<CampaignCreditCardInfo> infos = new ArrayList<>();
    for (CampaignCreditCardInfoInput infoInput : input.getCampaignCreditCardInfos()) {
      CampaignCreditCardInfo info = new CampaignCreditCardInfo();

      info.setCardNo(infoInput.getCardNo());
      info.setEffDate(infoInput.getEffDate());
      info.setDscDate(infoInput.getDscDate());
      info.setEffSaleDate(infoInput.getEffSaleDate());
      info.setDscSaleDate(infoInput.getDscSaleDate());
      info.setCreditCardCampaignType(infoInput.getCreditCardCampaignType());

      if (infoInput.getCreditCardWeekdayPromotion() != null) {
        List<CreditCardWeekdayPromotion> promotions = new ArrayList<>();
        infoInput.getCreditCardWeekdayPromotion().stream().forEach(promotionDto -> {
          CreditCardWeekdayPromotion promotion = new CreditCardWeekdayPromotion();
          if (promotionDto.getPromotionId() != null) {
            promotionPlanRepo.findById(promotionDto.getPromotionId())
                .ifPresent(promotion::setPromotionPlan);
          }
          promotion.setDayOfWeek(promotionDto.getDayOfWeek());
          promotion.setCreditCardInfo(info);
          promotions.add(promotion);
        });
        info.setPromotions(promotions);
      }
      info.setBatch(aggregateRoot);
      infos.add(info);
    }
    aggregateRoot.setCardNos(infos);
  }

  public List<CampaignCreditCardInfoData> translateEventMsg(List<CampaignCreditCardInfo> entities) {
    return entities.stream().map(this::toMsgPayload).collect(Collectors.toList());
  }

  /**
   * 建立事件用資料內容
   *
   * @param info
   * @return
   */
  public CampaignCreditCardInfoData toMsgPayload(CampaignCreditCardInfo info) {
    CampaignCreditCardInfoDataBuilder builder = CampaignCreditCardInfoData.builder()
        .creditCardCampaignType(info.getCreditCardCampaignType()).cardNo(info.getCardNo())
        .effDate(info.getEffDate()).dscDate(info.getDscDate()).effSaleDate(info.getEffSaleDate())
        .dscSaleDate(info.getDscSaleDate());

    if (CreditCardCampaignType.STANDARD_PROMOTION.equals(info.getCreditCardCampaignType())) {
      // 標準卡友要帶入promotionCode，逗點分隔
      String[] promotionCodeArray = {"", "", "", "", "", "", ""};

      for (CreditCardWeekdayPromotion promotion : info.getPromotions()) {
        // 前端dayOfWeek 1~7為週一~週日，RES順序為週日~週六
        int index = promotion.getDayOfWeek() == 7 ? 0 : promotion.getDayOfWeek();
        // BSM系統促銷碼為P開頭，傳給RES需去除P
        if (promotion.getPromotionPlan() != null) {
          promotionCodeArray[index] = org.apache.commons.lang3.StringUtils
              .substringAfter(promotion.getPromotionPlan().getCode(), "P");
        }
      }
      String promotionCodes = org.apache.commons.lang3.StringUtils.join(promotionCodeArray, ",");
      builder.promotionCode(promotionCodes);
    }
    return builder.build();
  }

  /**
   * 建立成功訊息明細: when creation, use info input
   *
   * @param cardInfo
   * @return
   */
  private String generateOKDetail(CampaignCreditCardInfoInput cardInfo) {

    final StringBuilder sb = new StringBuilder();
    sb.append(cardInfo.getCardNo()).append("|")
        .append(cardInfo.getEffDate().format(DateTimeFormatter.BASIC_ISO_DATE)).append("|")
        .append(cardInfo.getDscDate() != null
            ? cardInfo.getDscDate().format(DateTimeFormatter.BASIC_ISO_DATE)
            : "#")
        .append("|").append(cardInfo.getEffSaleDate().format(DateTimeFormatter.BASIC_ISO_DATE))
        .append("|")
        .append(cardInfo.getDscSaleDate() != null
            ? cardInfo.getDscSaleDate().format(DateTimeFormatter.BASIC_ISO_DATE)
            : "#")
        .append("|");

    // 一般卡友加上促銷碼
    if (CreditCardCampaignType.STANDARD_PROMOTION.equals(cardInfo.getCreditCardCampaignType())) {
      cardInfo.getCreditCardWeekdayPromotion().stream().forEach(p -> {
        if (p.getPromotionId() != null) {
          promotionPlanRepo.findById(p.getPromotionId())
              .ifPresent(plan -> sb.append(plan.getCode()).append("|"));
        } else {
          sb.append("null").append("|");
        }
      });
    }
    return sb.append("OK").toString();
  }

  /**
   * 建立成功訊息明細: when deletion, use info entity
   *
   * @param cardInfo
   * @return
   */
  private String generateOKDetail(CampaignCreditCardInfo cardInfo) {

    final StringBuilder sb = new StringBuilder();
    sb.append(cardInfo.getCardNo()).append("|")
        .append(cardInfo.getEffDate().format(DateTimeFormatter.BASIC_ISO_DATE)).append("|")
        .append(cardInfo.getDscDate() != null
            ? cardInfo.getDscDate().format(DateTimeFormatter.BASIC_ISO_DATE)
            : "#")
        .append("|").append(cardInfo.getEffSaleDate().format(DateTimeFormatter.BASIC_ISO_DATE))
        .append("|")
        .append(cardInfo.getDscSaleDate() != null
            ? cardInfo.getDscSaleDate().format(DateTimeFormatter.BASIC_ISO_DATE)
            : "#")
        .append("|");

    // 一般卡友加上促銷碼
    if (CreditCardCampaignType.STANDARD_PROMOTION.equals(cardInfo.getCreditCardCampaignType())) {
      cardInfo.getPromotions().stream().forEach(p -> {
        if (Objects.nonNull(p.getPromotionPlan())) {
          sb.append(p.getPromotionPlan().getCode()).append("|");
        } else {
          sb.append("null").append("|");
        }
      });
    }
    return sb.append("OK").toString();
  }


  /**
   * 建立失敗訊息明細: when creation, use info input
   *
   * @param cardInfo
   * @param errMsg
   * @return
   */
  private String generateErrDetail(CampaignCreditCardInfoInput cardInfo, String errMsg) {

    final StringBuilder sb = new StringBuilder();
    sb.append(cardInfo.getCardNo()).append("|")
        .append(cardInfo.getEffDate().format(DateTimeFormatter.BASIC_ISO_DATE)).append("|")
        .append(cardInfo.getDscDate() != null
            ? cardInfo.getDscDate().format(DateTimeFormatter.BASIC_ISO_DATE)
            : "#")
        .append("|").append(cardInfo.getEffSaleDate().format(DateTimeFormatter.BASIC_ISO_DATE))
        .append("|")
        .append(cardInfo.getDscSaleDate() != null
            ? cardInfo.getDscSaleDate().format(DateTimeFormatter.BASIC_ISO_DATE)
            : "#")
        .append("|");

    // 一般卡友加上促銷碼
    if (CreditCardCampaignType.STANDARD_PROMOTION.equals(cardInfo.getCreditCardCampaignType())) {
      cardInfo.getCreditCardWeekdayPromotion().stream().forEach(p -> {
        if (p.getPromotionId() != null) {
          promotionPlanRepo.findById(p.getPromotionId())
              .ifPresent(plan -> sb.append(plan.getCode()).append("|"));
        } else {
          sb.append("null").append("|");
        }
      });
    }
    return sb.append(BULK_ERROR_START).append(errMsg == null ? StringUtils.EMPTY : errMsg)
        .toString();
  }

  /**
   * 建立失敗訊息明細: when deletion, use info entity
   *
   * @param cardInfo entity
   * @param errMsg
   * @return
   */
  private String generateErrDetail(CampaignCreditCardInfo cardInfo, String errMsg) {

    final StringBuilder sb = new StringBuilder();
    sb.append(cardInfo.getCardNo()).append("|")
        .append(cardInfo.getEffDate().format(DateTimeFormatter.BASIC_ISO_DATE)).append("|")
        .append(cardInfo.getDscDate() != null
            ? cardInfo.getDscDate().format(DateTimeFormatter.BASIC_ISO_DATE)
            : "#")
        .append("|").append(cardInfo.getEffSaleDate().format(DateTimeFormatter.BASIC_ISO_DATE))
        .append("|")
        .append(cardInfo.getDscSaleDate() != null
            ? cardInfo.getDscSaleDate().format(DateTimeFormatter.BASIC_ISO_DATE)
            : "#")
        .append("|");

    // 一般卡友加上促銷碼
    if (CreditCardCampaignType.STANDARD_PROMOTION.equals(cardInfo.getCreditCardCampaignType())) {
      cardInfo.getPromotions().stream().forEach(p -> {
        if (Objects.nonNull(p.getPromotionPlan())) {
          sb.append(p.getPromotionPlan().getCode()).append("|");
        } else {
          sb.append("null").append("|");
        }
      });
    }
    return sb.append(BULK_ERROR_START).append(errMsg == null ? StringUtils.EMPTY : errMsg)
        .toString();
  }

  /**
   * 建立失敗訊息明細: when id not found, use info id
   *
   * @param id infoId input
   * @param errMsg
   * @return
   */
  private String generateErrDetail(Long id, String errMsg) {

    final StringBuilder sb = new StringBuilder();
    sb.append(id).append("|").append(StringUtils.EMPTY).append("|").append(StringUtils.EMPTY)
        .append("|").append(StringUtils.EMPTY).append("|").append(StringUtils.EMPTY).append("|");

    return sb.append(BULK_ERROR_START).append(errMsg == null ? StringUtils.EMPTY : errMsg)
        .toString();
  }


  /**
   * 刪除單筆卡號 (刪除卡號後已無其他卡號之批次則一併刪除)->改為bulk刪除的方式
   *
   * @param input List<Long>ids
   * @return
   */
  @RESTransaction
  public CollectionModificationOutput cardInfoRemovals(CardInfoRemovalInput input) {
    log.debug("ids().size() = {}", input.getDeletions().size());

    CollectionModificationOutput result = new CollectionModificationOutput();

    // 查詢批次與卡號紀錄，卡號依照所屬的批次分群
    input.getDeletions().stream().forEach(id -> {
      CampaignCreditCardBatch batch = null;
      CampaignCreditCardInfo info = null;
      try {
        info = infoRepo.findById(id)
            .orElseThrow(() -> FareErrorCode.FA_CREDIT_CARD_CARD_INFO_EXIST_IN_BSM_DB
                .toException(id.toString()));

        batch = info.getBatch();
        // 先發布RES事件
        CampaignCreditCardInfoDeleted event = new CampaignCreditCardInfoDeleted(
            interceptor.getUserId(), interceptor.getCorrelationId());
        event.setMsgPayload(this.toMsgPayload(info));
        event.setAssociationId(interceptor.getCorrelationId());
        applicationEventPublisher.publishEvent(event);

        // 成功後從batch移除infos
        batch.getCardNos().remove(info);

        result.getDeletions().put(id, this.generateOKDetail(info));

      } catch (Exception e) {
        // 處理異常，產生該筆導入失敗訊息放入結果中
        log.error(ExceptionUtils.getStackTrace(e));

        if (Objects.nonNull(info)) {
          result.getDeletions().put(id, this.generateErrDetail(info, ExceptionUtils.getMessage(e)));

        } else {
          result.getDeletions().put(id, this.generateErrDetail(id, ExceptionUtils.getMessage(e)));
        }
      }

      if (Objects.nonNull(batch)) {
        // 紀錄已經沒有卡號的批次紀錄
        if (batch.getCardNos().isEmpty()) {
          batchRepo.delete(batch);

        } else {
          batchRepo.saveAndFlush(batch);
        }
      }
    });

    return result;
  }

  /**
   * 新增8碼之前6碼與現有6碼重複，或新增6碼與現有8碼之前6碼重複，則新增失敗
   *
   * @param card validate infoInput
   */
  private void validateCardNo(CampaignCreditCardInfoInput card,
      List<CampaignCreditCardInfoInput> allInputs) {
    Integer cardNoLength = card.getCardNo().length();
    if (cardNoLength < 6) {
      return;
    }
    // 搜集現有卡號的前六碼(新增8碼找現行6碼 新增6碼找現行8碼)
    // BizUAT#22199補充規則:如果訂位日期不同，卡號前6碼相同是可以並存 -> 訂位日期有交疊才納入檢核範圍
    Optional<List<CampaignCreditCardInfo>> optional =
        infoRepo.findByCreditCardCampaignType(card.getCreditCardCampaignType());
    Set<String> cardNoSet =
        optional.map(campaignCreditCardInfos -> campaignCreditCardInfos.stream().filter(info -> {
          // 截止日未輸入代表無限期
          LocalDate cardDscSaleDate = this.getDateOrDefault(card.getDscSaleDate(), LocalDate.MAX);
          LocalDate infoDscSaleDate = this.getDateOrDefault(info.getDscSaleDate(), LocalDate.MAX);
          if (cardNoLength == 8) {
            // 輸入卡號8碼，找訂位日期有交疊&&卡號為6碼資料
            return info.getCardNo().length() == 6 && BsmDateUtils.isOverlap(card.getEffSaleDate(),
                cardDscSaleDate, info.getEffSaleDate(), infoDscSaleDate, true);
          } else {
            // 輸入卡號6碼，找訂位日期有交疊&&卡號為8碼資料
            return info.getCardNo().length() == 8 && BsmDateUtils.isOverlap(card.getEffSaleDate(),
                cardDscSaleDate, info.getEffSaleDate(), infoDscSaleDate, true);
          }
        }).map(info -> info.getCardNo().substring(0, 6)).collect(Collectors.toSet()))
            .orElseGet(LinkedHashSet::new);

    // 輸入的卡號清單也要納入比對範圍
    if (!CollectionUtils.isEmpty(allInputs)) {
      Set<String> filteredInputCardSet = allInputs.stream().filter(input -> {
        // 截止日未輸入代表無限期
        LocalDate cardDscSaleDate = this.getDateOrDefault(card.getDscSaleDate(), LocalDate.MAX);
        LocalDate inputDscSaleDate = this.getDateOrDefault(input.getDscSaleDate(), LocalDate.MAX);
        if (cardNoLength == 8) {
          // 輸入卡號8碼，找訂位日期有交疊&&卡號為6碼資料
          return input.getCardNo().length() == 6 && BsmDateUtils.isOverlap(card.getEffSaleDate(),
              cardDscSaleDate, input.getEffSaleDate(), inputDscSaleDate, true);
        } else {
          // 輸入卡號6碼，找訂位日期有交疊&&卡號為8碼資料
          return input.getCardNo().length() == 8 && BsmDateUtils.isOverlap(card.getEffSaleDate(),
              cardDscSaleDate, input.getEffSaleDate(), inputDscSaleDate, true);
        }
      }).map(info -> info.getCardNo().substring(0, 6)).collect(Collectors.toSet());

      cardNoSet.addAll(filteredInputCardSet);
    }

    // 比對輸入卡號是否與現行資料重複
    if (cardNoSet.contains(card.getCardNo().substring(0, 6))) {
      throw FareErrorCode.FA_CREDIT_CARD_TOP6_CARD_NO_DUPLICATE.toException(card.getCardNo());
    }
  }
}
