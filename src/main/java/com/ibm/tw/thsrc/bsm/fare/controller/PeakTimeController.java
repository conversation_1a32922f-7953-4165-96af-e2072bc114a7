/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.fa.api.PeakTimeApi;
import com.ibm.tw.thsrc.bsm.fa.dto.PeakTimeInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PeakTimeOutput;
import com.ibm.tw.thsrc.bsm.fare.service.PeakTimeCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.PeakTimeNameQueryService;
import java.util.List;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/PeakTimes")
public class PeakTimeController implements PeakTimeApi {

  @Autowired
  private PeakTimeCommandService commandService;

  @Autowired
  private PeakTimeNameQueryService queryServices;

  @Autowired
  private EventStore eventStore;

  @Override
  public List<PeakTimeOutput> upsert(List<PeakTimeInput> changeList) {
    eventStore.validateFunctionLock(OperationFunction.PEAK_TIMES);
    Set<Long> ids = commandService.upsert(changeList);
    return queryServices.read(ids);
  }
}
