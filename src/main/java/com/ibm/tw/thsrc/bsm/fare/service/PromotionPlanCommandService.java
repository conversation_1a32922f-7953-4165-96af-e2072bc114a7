/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source
 * code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.DateRangeDto;
import com.ibm.tw.thsrc.bsm.core.entity.DateRange;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.exception.SharedErrorCode;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionInput;
import com.ibm.tw.thsrc.bsm.fa.enums.EligiblePassengerProfileType;
import com.ibm.tw.thsrc.bsm.fa.enums.FareRule;
import com.ibm.tw.thsrc.bsm.fa.enums.PromotionServiceType;
import com.ibm.tw.thsrc.bsm.fare.domain.event.PromotionPlanCreated;
import com.ibm.tw.thsrc.bsm.fare.domain.event.PromotionPlanDeleted;
import com.ibm.tw.thsrc.bsm.fare.domain.event.PromotionPlanReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.AdditionalProfileDiscount;
import com.ibm.tw.thsrc.bsm.fare.entity.ClassProjection;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrain;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProjectMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.FareRuleSetting;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerProfile;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionCategoryMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.SmallGroupPlan;
import com.ibm.tw.thsrc.bsm.fare.repository.ClassProjectionRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CreditCardWeekdayPromotionRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.EligibleTrainRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.FareProjectMappingRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PassengerProfileRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionPlanRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionCategoryMappingRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.SmallGroupPlanRepository;
import com.ibm.tw.thsrc.bsm.message.DateRangeData;
import com.ibm.tw.thsrc.bsm.message.fare.AdditionalProfileDiscountData;
import com.ibm.tw.thsrc.bsm.message.fare.FareRuleSettingData;
import com.ibm.tw.thsrc.bsm.message.fare.PassengerProfileData;
import com.ibm.tw.thsrc.bsm.message.fare.PromotionPlanData;
import com.ibm.tw.thsrc.bsm.message.schedule.ClassData;
import com.ibm.tw.thsrc.bsm.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class PromotionPlanCommandService
    extends AbstractCommandService<PromotionPlan, PromotionInput> {

  private static final String OPEN_DATE = "#";
  private static final int expiredDays = 30;

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private PromotionPlanRepository repo;
  @Autowired
  private PassengerProfileRepository profileRepo;
  @Autowired
  private ClassProjectionRepository classRepo;
  @Autowired
  private SmallGroupPlanRepository smallGroupRepo;
  @Autowired
  private EligibleTrainRepository eligibleTrainRepo;
  @Autowired
  private FareProjectMappingRepository projectMappingRepo;
  @Autowired
  private CreditCardWeekdayPromotionRepository creditCardWeekdayRepo;
  @Autowired
  private PromotionCategoryMappingRepository categoryMappingRepo;
  @Autowired
  private EventStore eventStore;

  @Override
  public Long create(PromotionInput input) {
    if (repo.findByCode(input.getCode()).isPresent()) {
      throw FareErrorCode.FA_FARE_PLAN_CODE_CANNOT_REPEAT.toException(input.getCode());
    }
    PromotionPlan newEntity = new PromotionPlan();
    newEntity.setEligiblePassengerProfileType(EligiblePassengerProfileType.VALID);
    writeAggregateRootValue(newEntity, input);
    validateInvariants(newEntity);

    PromotionPlanCreated event =
        new PromotionPlanCreated(translateMsgData(newEntity), interceptor.getUserId(),
            interceptor.getCorrelationId());

    event.setOperationId(OperationFunction.PROMOTION.name());
    event.setAssociationId(interceptor.getCorrelationId());
    if (newEntity.getServiceType().equals(PromotionServiceType.RESERVED)) {
      eventStore.publishEvent(event);
    }

    PromotionPlan entity = repo.save(newEntity);

    return entity.getId();
  }

  @Override
  public void update(Long id, PromotionInput input) {
    PromotionPlan entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    PromotionPlanData previousData = translateMsgData(entity);

    if (!StringUtils.equals(input.getCode(), entity.getCode())) {
      throw FareErrorCode.FA_FARE_PLAN_CODE_UPDATE_NOT_ALLOWED.toException();
    }
    if (entity.isSmallGroup() && !input.getIsSmallGroup()) {
      List<SmallGroupPlan> smallGroupUsePromotion = smallGroupRepo.findByPromotionPlan(entity);
      if (!smallGroupUsePromotion.isEmpty()) {
        throw FareErrorCode.FA_PROMOTION_PLAN_CANNOT_MODIFY_TO_NOT_SMALL_GROUP_WHEN_HAS_SMALL_GROUP_SETTING.toException();
      }
    }
    this.validateConcurrency(entity, input.getDataVersion());
    this.writeAggregateRootValue(entity, input);
    validateInvariants(entity);

    PromotionPlanReplaced event =
        new PromotionPlanReplaced(translateMsgData(entity), previousData, interceptor.getUserId(),
            interceptor.getCorrelationId());

    event.setOperationId(OperationFunction.PROMOTION.name());
    event.setAssociationId(interceptor.getCorrelationId());

    if (entity.getServiceType().equals(PromotionServiceType.RESERVED)) {
      eventStore.publishEvent(event);
    }

    repo.save(entity);

  }

  @Override
  public void delete(Long id) {
    PromotionPlan entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);

    List<SmallGroupPlan> smallGroupUsingPromotion = smallGroupRepo.findByPromotionPlan(entity);
    if (!smallGroupUsingPromotion.isEmpty()) {
      throw FareErrorCode.FA_PROMOTION_PLAN_CANNOT_DELETE_PROMOTION_WHEN_HAS_SMALL_GROUP_SETTING.toException();
    }

    List<EligibleTrain> eligibleTrainUsingPromotion = eligibleTrainRepo.findByFarePlan(entity);
    if (!eligibleTrainUsingPromotion.isEmpty()) {
      throw FareErrorCode.FA_PROMOTION_PLAN_CANNOT_DELETE_PROMOTION_WHEN_HAS_ELIGIBLE_TRAIN_SETTING.toException();
    }

    List<FareProjectMapping> projectMappingUsingPromotion =
        projectMappingRepo.findByPromotionPlan(entity);
    if (!projectMappingUsingPromotion.isEmpty()) {
      throw FareErrorCode.FA_PROMOTION_PLAN_CANNOT_DELETE_PROMOTION_WHEN_HAS_PROJECT_MAPPING_SETTING.toException();
    }

    // UAT#23073 檢核促銷碼是否被標準卡友使用中
    if (!CollectionUtils.isEmpty(creditCardWeekdayRepo.findByPromotionPlan(entity))) {
      throw FareErrorCode.FA_PROMOTION_PLAN_CANNOT_DELETE_PROMOTION_WHEN_HAS_CREDIT_CARD_WEEKDAY_SETTING.toException();
    }

    PromotionPlanDeleted event =
        new PromotionPlanDeleted(translateMsgData(entity), interceptor.getUserId(),
            interceptor.getCorrelationId());

    event.setOperationId(OperationFunction.PROMOTION.name());
    event.setAssociationId(interceptor.getCorrelationId());

    if (entity.getServiceType().equals(PromotionServiceType.RESERVED)) {
      eventStore.publishEvent(event);
    }

    // 刪除促銷碼時，只要前面條件等合，就一起把促銷碼單排序刪除
    List<PromotionCategoryMapping> categoryMappings =
        categoryMappingRepo.findByPromotionPlan(entity);
    categoryMappingRepo.deleteAll(categoryMappings);
    repo.delete(entity);
  }

  @Override
  protected void validateInvariants(PromotionPlan entity) {

    if (Objects.isNull(entity.getEffDate()) && Objects.nonNull(entity.getDscDate())) {
      throw SharedErrorCode.EFF_DATE_NOT_ALLOW_EMPTY_WHEN_DSC_DATE_NOT_EMPTY.toException();
    }

    checkInvalidDatesOverlap(entity.getInvalidDateRanges());
  }

  @Override
  protected void writeAggregateRootValue(PromotionPlan entity, PromotionInput input) {
    // 在translate 中會比對原本發車起迄日，比對完在清除entity 資料
    List<FareRuleSetting> settings = translate(input, entity);
    entity.getFareRuleSettings().clear();
    entity.getFareRuleSettings().addAll(settings);

    entity.setDiscountAmt(input.getDiscountAmt());
    entity.setDiscountPct(input.getDiscountPct());
    entity.setSmallGroup(input.getIsSmallGroup());

    entity.setCode(input.getCode());
    entity.setName(input.getName());
    entity.setEffDate(input.getEffDate());
    entity.setDscDate(input.getDscDate());
    entity.setType(input.getType());
    entity.setServiceType(input.getServiceType());

    List<ClassProjection> validClasses = entity.getValidClasses();
    validClasses.clear();

    input.getValidClassIds().forEach(id -> {
      ClassProjection classProjection = classRepo.findById(id).orElseThrow(
          FareErrorCode.FA_FARE_PLAN_CANNOT_ASSOCIATE_TO_CLASS_PROJECTION::toException);
      validClasses.add(classProjection);
    });

    List<PassengerProfile> profiles = entity.getPassengerProfiles();
    profiles.clear();

    if (EligiblePassengerProfileType.INVALID.equals(entity.getEligiblePassengerProfileType())) {
      profiles.addAll(profileRepo.findByIdNotIn(input.getValidPassengerProfileIds()));
    } else if (!EligiblePassengerProfileType.ALL.equals(entity.getEligiblePassengerProfileType())) {
      input.getValidPassengerProfileIds().forEach(id -> {
        PassengerProfile profile = profileRepo.findById(id).orElseThrow(
            FareErrorCode.FA_FARE_PLAN_CANNOT_ASSOCIATE_TO_PASSENGER_PROFILE::toException);
        profiles.add(profile);
      });
    }

    List<AdditionalProfileDiscount> extraDiscounts = entity.getAdditionalProfileDiscount();
    extraDiscounts.clear();

    input.getAdditionalProfileDiscounts().stream().forEach(addDiscount -> {
      PassengerProfile profile = profileRepo.findById(addDiscount.getPassengerProfileId())
          .orElseThrow(
              FareErrorCode.FA_FARE_PLAN_CANNOT_ASSOCIATE_TO_PASSENGER_PROFILE::toException);
      AdditionalProfileDiscount extraDiscount = new AdditionalProfileDiscount();
      extraDiscount.setFarePlan(entity);
      extraDiscount.setPassengerProfile(profile);
      extraDiscount.setAdditionalDiscount(addDiscount.getPctOff());

      extraDiscounts.add(extraDiscount);
    });

    List<DateRange> invalidDateRange = entity.getInvalidDateRanges();
    invalidDateRange.clear();

    input.getInvalidDateRanges().sort(Comparator.comparing(DateRangeDto::getBeginDate));
    input.getInvalidDateRanges().forEach(inputRange -> {
      DateRange dateRange = new DateRange(inputRange.getBeginDate(), inputRange.getEndDate());
      invalidDateRange.add(dateRange);
    });

    // 只有修改促銷碼時，需要檢核小團體的適用身分別是否異動
    if (entity.getId() != null) {
      this.validateSmallGroup(entity);
    }


  }

  private void validateSmallGroup(PromotionPlan entity) {
    // check if any small group profile is removed
    List<SmallGroupPlan> smallGroupPlans = smallGroupRepo.findByPromotionPlan(entity);

    List<String> promoValidProfileCodes =
        entity.getPassengerProfiles().stream().map(PassengerProfile::getCode)
            .collect(Collectors.toList());

    for (SmallGroupPlan group : smallGroupPlans) {
      if (!promoValidProfileCodes.contains(group.getMainProfileExpr())) {
        throw FareErrorCode.FA_PROMOTION_VALID_PROFILES_MISSING_ITS_SMALL_GROUP_PROFILE.toException(
            group.getMainProfileExpr());
      }
    }
  }

  private List<FareRuleSetting> translate(PromotionInput input, PromotionPlan entity) {
    List<FareRuleSetting> settings = new ArrayList<>();
    DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    // R08
    FareRuleSetting r08setting =
        new FareRuleSetting(FareRule.R08_CHANGE_RULE, String.valueOf(input.getModifiable()),
            entity);
    settings.add(r08setting);

    // R12
    FareRuleSetting r12setting =
        new FareRuleSetting(FareRule.R12_VALID_WEEKDAY, input.getFrequency(), entity);
    settings.add(r12setting);

    // R16
    FareRuleSetting r16Setting = new FareRuleSetting(FareRule.R16_IMMEDIATE_PAYMENT,
        String.valueOf(input.getImmediatePayment()), entity);
    settings.add(r16Setting);

    // R20
    FareRuleSetting r20Setting;
    if (Objects.nonNull(input.getContent()) && StringUtils.isNotBlank(input.getContent())) {
      r20Setting = new FareRuleSetting(FareRule.R20_CONTENT, input.getContent(), entity);
    } else {
      r20Setting = new FareRuleSetting(FareRule.R20_CONTENT, StringUtils.EMPTY, entity);
    }
    settings.add(r20Setting);

    // R21
    FareRuleSetting r21Setting;
    if (Objects.nonNull(input.getDescription()) && StringUtils.isNotBlank(input.getDescription())) {
      r21Setting = new FareRuleSetting(FareRule.R21_DESCRIPTION, input.getDescription(), entity);
    } else {
      r21Setting = new FareRuleSetting(FareRule.R21_DESCRIPTION, StringUtils.EMPTY, entity);
    }
    settings.add(r21Setting);

    // R34
    FareRuleSetting r34Setting = new FareRuleSetting(FareRule.R34_ELIGIBLE_TRAIN,
        String.valueOf(input.getEligibleTrainType()), entity);
    settings.add(r34Setting);

    // R26
    FareRuleSetting r26setting = new FareRuleSetting(FareRule.R26_EFF_DATE, entity);
    r26setting.setSetting(input.getEffDate().format(dtf));

    // R27
    FareRuleSetting r27setting = new FareRuleSetting(FareRule.R27_DSC_DATE, entity);
    if (Objects.nonNull(input.getDscDate())) {
      r27setting.setSetting(input.getDscDate().format(dtf));
    } else {
      r27setting.setSetting(OPEN_DATE);
    }

    // R51
    FareRuleSetting r51setting = new FareRuleSetting(FareRule.R51_EFF_SALE_DATE, entity);
    if (Objects.nonNull(input.getEffSaleDate())) {
      r51setting.setSetting(input.getEffSaleDate().format(dtf));
    } else {
      r51setting.setSetting(OPEN_DATE);
    }

    // R09
    FareRuleSetting r09setting = new FareRuleSetting(FareRule.R09_DSC_SALE_DATE, entity);
    if (Objects.nonNull(input.getDscSaleDate())) {
      r09setting.setSetting(input.getDscSaleDate().format(dtf));
    } else {
      r09setting.setSetting(OPEN_DATE);
    }

    Optional<FareRuleSetting> effSaleDateOption = entity.getFareRuleSettings().stream()
        .filter(e -> FareRule.R51_EFF_SALE_DATE.equals(e.getFareRule())).findFirst();

    LocalDate effSaleDate = null;
    if (effSaleDateOption.isPresent() && !OPEN_DATE.equals(effSaleDateOption.get().getSetting())) {
      effSaleDate = LocalDate.parse(effSaleDateOption.get().getSetting(), dtf);
    }

    // 需判斷要先下那一個日期，除了日期往前外，一律先下迄日再下起日
    if (Objects.nonNull(entity.getEffDate()) && Objects.nonNull(
        input.getDscDate()) && input.getDscDate().isBefore(entity.getEffDate())) {
      settings.add(r26setting);
      settings.add(r27setting);
    } else {
      settings.add(r27setting);
      settings.add(r26setting);
    }

    if (Objects.nonNull(effSaleDate) && Objects.nonNull(
        input.getDscSaleDate()) && input.getDscSaleDate().isBefore(effSaleDate)) {
      settings.add(r51setting);
      settings.add(r09setting);
    } else {
      settings.add(r09setting);
      settings.add(r51setting);
    }

    // R61
    FareRuleSetting r61setting =
        new FareRuleSetting(FareRule.R61_CHANGE_RULE, String.valueOf(input.getModifiable()),
            entity);
    settings.add(r61setting);

    return settings;
  }

  private PromotionPlanData translateMsgData(PromotionPlan entity) {
    PromotionPlanData data = new PromotionPlanData();
    data.setAllClassesValid(entity.isAllClassesValid());
    data.setCode(entity.getCode());
    data.setDscDate(entity.getDscDate());
    data.setEffDate(entity.getEffDate());
    data.setEligiblePassengerProfileType(String.valueOf(entity.getEligiblePassengerProfileType()));
    data.setName(entity.getName());
    data.setType(String.valueOf(entity.getType()));
    data.setDiscountAmt(
        Objects.nonNull(entity.getDiscountAmt()) ? entity.getDiscountAmt().intValue() : null);
    data.setDiscountPct(
        Objects.nonNull(entity.getDiscountPct()) ? entity.getDiscountPct().intValue() : null);
    data.setSmallGroup(entity.isSmallGroup());

    entity.getFareRuleSettings()
        .forEach(e -> data.getFareRuleSettings().add(FareRuleSettingData.builder()//
            .fareRule(String.valueOf(e.getFareRule()))//
            .setting(e.getSetting())//
            .build()));

    entity.getInvalidDateRanges()
        .forEach(e -> data.getInvalidDateRanges().add(DateRangeData.builder()//
            .beginDate(e.getBeginDate())//
            .endDate(e.getEndDate())//
            .build()));

    entity.getPassengerProfiles()
        .forEach(e -> data.getPassengerProfiles().add(PassengerProfileData.builder()//
            .code(e.getCode())//
            .enName(e.getEnName())//
            .enPrintName(e.getEnPrintName())//
            .zhName(e.getZhName())//
            .zhPrintName(e.getZhPrintName())//
            .tbLightColor(e.getTbLightColor())//
            .id(e.getId())//
            .build()));

    entity.getValidClasses().forEach(e -> data.getValidClasses().add(ClassData.builder()//
        .baseClass(e.getBaseClass().getCode())//
        .code(e.getCode())//
        .build()));

    entity.getAdditionalProfileDiscount()
        .forEach(e -> data.getAdditionalDiscounts().add(AdditionalProfileDiscountData.builder()//
            .pctOff(e.getAdditionalDiscount().intValue())//
            .code(e.getPassengerProfile().getCode())//
            .build()));

    return data;
  }


  private void checkInvalidDatesOverlap(List<DateRange> invalidDateRanges) {

    int startDateOverlaps = invalidDateRanges.stream().map(DateRange::getBeginDate)
        .mapToInt(date -> overlap(date, invalidDateRanges)).max().orElse(0);
    int endDateOverlaps = invalidDateRanges.stream().map(DateRange::getEndDate)
        .mapToInt(date -> overlap(date, invalidDateRanges)).max().orElse(0);
    int maxOverlaps = Integer.max(startDateOverlaps, endDateOverlaps);
    if (maxOverlaps > 0) {
      throw SharedErrorCode.DATE_RANGE_OVERLAP.toException();
    }
  }

  private Integer overlap(LocalDate date, List<DateRange> dateRanges) {
    return dateRanges.stream().mapToInt(
        dateRange -> (!(date.isBefore(dateRange.getBeginDate()) || date.isAfter(
            dateRange.getEndDate()))) ? 1 : 0).sum() - 1;
  }


}
