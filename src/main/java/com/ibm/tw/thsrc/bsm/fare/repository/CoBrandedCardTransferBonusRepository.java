/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCard;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardTransferBonus;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface CoBrandedCardTransferBonusRepository extends
    JpaRepository<CoBrandedCardTransferBonus, Long>,
    JpaSpecificationExecutor<CoBrandedCardTransferBonus> {

  List<CoBrandedCardTransferBonus> findByCoBrandedCard(CoBrandedCard coBrandedCard);
}
