/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.fa.api.TicketProductApi;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketProductInput;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketProductOutput;
import com.ibm.tw.thsrc.bsm.fare.service.TicketProductCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.TicketProductQueryService;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/TicketProducts")
public class TicketProductController implements TicketProductApi {

  @Autowired
  private TicketProductCommandService commandService;

  @Autowired
  private TicketProductQueryService queryService;

  @Autowired
  private EventStore eventStore;

  @Override
  public Set<TicketProductOutput> modify(
      CollectionModificationInput<TicketProductInput> changeSet) {
    eventStore.validateFunctionLock(OperationFunction.TICKET_TYPE);
    Set<Long> ids = commandService.patch(changeSet);
    ids.addAll(changeSet.getReplacements().keySet());
    return queryService.read(ids);
  }

  @Override
  public Page<TicketProductOutput> search(Search searchRequest) {
    return queryService.search(searchRequest);
  }
}
