/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.api.PeakWeekdayDetailApi;
import com.ibm.tw.thsrc.bsm.fa.dto.PeakWeekdayOutput;
import com.ibm.tw.thsrc.bsm.fare.service.PeakWeekdayDetailQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/PeakWeekdayDetails")
public class PeakWeekdayDetailController implements PeakWeekdayDetailApi {

  @Autowired
  private PeakWeekdayDetailQueryService queryService;

  @Override
  public Page<PeakWeekdayOutput> search(Search search) {
    return queryService.search(search);
  }
}
