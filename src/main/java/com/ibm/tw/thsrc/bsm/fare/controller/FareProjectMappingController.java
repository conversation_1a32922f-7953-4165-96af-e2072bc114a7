/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.event.ExceptionMessages;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.fa.api.FareProjectMappingApi;
import com.ibm.tw.thsrc.bsm.fa.dto.FareProjectMappingInput;
import com.ibm.tw.thsrc.bsm.fa.dto.FareProjectMappingOutput;
import com.ibm.tw.thsrc.bsm.fare.service.FareProjectMappingCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.FareProjectMappingQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/FareProjectMapping")
public class FareProjectMappingController implements FareProjectMappingApi {

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private EventStore eventStore;

  @Autowired
  private ExceptionMessages exceptionMessages;


  @Autowired
  private FareProjectMappingCommandService commandService;

  @Autowired
  private FareProjectMappingQueryService queryService;

  @Override
  public Page<FareProjectMappingOutput> search(Search search) {
    return queryService.search(search);
  }

  @Override
  public FareProjectMappingOutput create(FareProjectMappingInput input) {
    eventStore.validateFunctionLock(OperationFunction.FARE_PROJECT_MAPPING);
    Long id = commandService.create(input);
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
    return queryService.read(id);
  }

  @Override
  public void delete(Long id) {
    eventStore.validateFunctionLock(OperationFunction.FARE_PROJECT_MAPPING);
    commandService.delete(id);
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
  }
}
