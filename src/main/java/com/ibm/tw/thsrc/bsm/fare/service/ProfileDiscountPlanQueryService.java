/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.ProfileDiscountPlanOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountPlan;
import com.ibm.tw.thsrc.bsm.fare.repository.ProfileDiscountPlanRepository;
import com.ibm.tw.thsrc.bsm.fare.translate.ProfileDiscountPlanTranslator;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ProfileDiscountPlanQueryService
    extends AbstractQueryService<ProfileDiscountPlan, ProfileDiscountPlanOutput, Void, Void>
    implements ProfileDiscountPlanQueryApi {

  @Autowired
  private ProfileDiscountPlanRepository profileDiscountPlanRepository;

  @Override
  protected ProfileDiscountPlanOutput translate(ProfileDiscountPlan aggregateRoot) {
    return ProfileDiscountPlanTranslator.toProfileDiscountPlanOutput(aggregateRoot);
  }

  @Override
  protected Sort translate(Void sortBy, Direction sortDirection) {
    return Sort.unsorted();
  }

  @Override
  protected Predicate translate(ComparisonExpression<Void> f, Root<ProfileDiscountPlan> root,
      CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    return null;
  }

  @Override
  protected Function<Map<String, Boolean>, Void> getSortByTranslator() {
    return data -> null;
  }

  @Override
  protected Function<Map<String, Object>, Void> getFilterAttributeValueTranslator() {
    return data -> null;
  }

  @Override
  public ProfileDiscountPlanOutput read(Long id) {
    return profileDiscountPlanRepository.findById(id).map(this::translate)
        .orElseThrow(ResourceNotFoundException::new);
  }

  @Override
  public Page<ProfileDiscountPlanOutput> search(Search search) {
    PageRequest pr = translateToPageRequest(search);
    Specification<ProfileDiscountPlan> spec = translateToSpecification(search);
    return profileDiscountPlanRepository.findAll(spec, pr).map(this::translate);
  }

  public Set<ProfileDiscountPlanOutput> read(Collection<Long> ids) {
    return profileDiscountPlanRepository.findAllById(ids).stream().map(this::translate)
        .collect(Collectors.toSet());
  }

  @Override
  public List<ProfileDiscountPlanOutput> getProfileDiscountPlans() {
    return profileDiscountPlanRepository.findAll().stream().map(this::translate)
        .collect(Collectors.toList());
  }

  @Override
  public List<ProfileDiscountPlanOutput> getProfileDiscountPlansByProflieCodes(
      List<String> profileCodes) {
    return profileDiscountPlanRepository
        .findByPassengerProfile_CodeInOrderByPassengerProfile_Code(profileCodes).stream()
        .map(this::translate).collect(Collectors.toList());
  }
}
