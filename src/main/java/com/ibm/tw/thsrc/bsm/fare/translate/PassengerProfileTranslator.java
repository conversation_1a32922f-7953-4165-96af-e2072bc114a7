/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.translate;

import com.ibm.tw.thsrc.bsm.fa.dto.PassengerProfileOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerProfile;

public class PassengerProfileTranslator {

  private PassengerProfileTranslator() {
    throw new IllegalStateException("PassengerProfileTranslator class");
  }

  public static PassengerProfileOutput toPassengerProfileOutput(PassengerProfile aggregateRoot) {
    PassengerProfileOutput output = new PassengerProfileOutput();
    output.setId(aggregateRoot.getId());
    output.setCode(aggregateRoot.getCode());
    output.setDisplay(aggregateRoot.getIsDisplay());
    output.setDisplayOrder(aggregateRoot.getDisplayOrder());
    output.setZhName(aggregateRoot.getZhName());
    output.setZhPrintName(aggregateRoot.getZhPrintName());
    output.setEnName(aggregateRoot.getEnName());
    output.setEnPrintName(aggregateRoot.getEnPrintName());
    output.setTbLightColor(aggregateRoot.getTbLightColor());
    output.setAllowedExtraDiscount(aggregateRoot.isAllowedExtraDiscount());
    output.setDataVersion(aggregateRoot.getDataVersion());
    return output;
  }
}
