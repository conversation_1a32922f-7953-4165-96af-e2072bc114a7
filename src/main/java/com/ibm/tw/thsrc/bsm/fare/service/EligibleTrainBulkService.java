/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.aors.logger.AorsLogger;
import com.ibm.tw.thsrc.aors.logger.AorsLoggerFactory;
import com.ibm.tw.thsrc.aors.logger.AorsLoggerType;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationOutput;
import com.ibm.tw.thsrc.bsm.core.service.EmailService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.core.util.ExceptionUtils;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.SharedErrorCode;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainInput;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrain;
import com.ibm.tw.thsrc.bsm.fare.entity.FarePlan;
import com.ibm.tw.thsrc.bsm.fare.repository.EligibleTrainRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.FarePlanRepository;
import com.ibm.tw.thsrc.bsm.res.connection.annotation.RESTransaction;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EligibleTrainBulkService {

  private static final AorsLogger LOGGER_BASIC = AorsLoggerFactory.getLogger(AorsLoggerType.BASIC);
  private static final String BULK_DELETE_OPERATION = "DEL";
  private static final String BULK_CREATE_OPERATION = "ADD";
  private static final String BULK_SUCCESS = "OK";
  private static final String BULK_FAIL = "Err:";
  private static final String SEPARATION = "|";
  private static final String EXCEPTION_SEPARATION = ",";
  private static final String CONTENT_SEPARATION = "\n";
  private static final String RESULT_MAIL_SUBJECT = "BSM 操作適用車次結果通知";
  private static final String EMAIL_CREATE = "新增";
  private static final String EMAIL_DELETE = "刪除";
  private static final String FARE_PLAN_PATTERN = "%s (%s)";
  private static final String MAIL_CONTENT_DETAIL = "票價方案「%s」於發車日 %s 至 %s 的適用車次已%s完成";
  private static final String MAIL_CONTENT_SUMMARY = "票價方案「%s」共%s %d 筆適用車次";
  // 適用車次多筆刪除結果：
  // 票價方案「P001 (測試促銷碼優惠)」於發車日 2023-01-05 至 2023-05-01 的適用車次已刪除完成
  // 票價方案「P001 (測試促銷碼優惠)」共刪除 3 筆適用車次
  // 共刪除 3 筆適用車次
  private static final String MAIL_CONTENT = "適用車次多筆%s結果：\n%s \n%s\n共%s %d 筆適用車次";
  @Autowired
  private HttpHeaderInterceptor interceptor;
  @Autowired
  private EligibleTrainRepository repo;
  @Autowired
  private FarePlanRepository farePlanRepo;
  @Autowired
  private EligibleTrainCommandService commandService;
  @Autowired
  private EmailService emailService;

  @RESTransaction
  public CollectionModificationOutput bulk(
      CollectionModificationInput<EligibleTrainInput> changeSet) {
    CollectionModificationOutput bulkResult = new CollectionModificationOutput();

    Map<String, List<String>> deleteFarePlans = new HashMap<>();
    Map<String, List<String>> createFarePlans = new HashMap<>();

    // deletion
    changeSet.getDeletions().forEach(e -> {
      StringBuilder sb = new StringBuilder(String.valueOf(e)).append(SEPARATION)
          .append(BULK_DELETE_OPERATION).append(SEPARATION);
      try {
        EligibleTrain entity = repo.findById(e).orElseThrow(() ->
            SharedErrorCode.COLLECTION_BULK_INPUT_ID_NOT_EXIST.toException(String.valueOf(e)));

        sb = new StringBuilder(generateBasicDetailInfo(BULK_DELETE_OPERATION, entity));
        commandService.delete(entity);

        // email details
        setSuccessContentDetail(deleteFarePlans, EMAIL_DELETE, entity);
        repo.delete(entity);
        if (Objects.nonNull(entity.getProject())) {
          commandService.reorderPriorityForOtherEligibleTrain();
        }

        sb.append(BULK_SUCCESS);

      } catch (Exception e1) {
        sb.append(BULK_FAIL).append(ExceptionUtils.getMessage(e1));
        LOGGER_BASIC.exception(e1);
      }
      bulkResult.getDeletions().put(e, sb.toString());
    });

    // creation
    // 確認是否可立即新增
    Map<String, List<Triple<LocalDate, LocalDate, LocalDate>>> invalidUrgentCreate = new HashMap<>();
    if (!changeSet.getCreations().isEmpty()) {
      checkUrgentCreate(changeSet.getCreations(), invalidUrgentCreate);
    }
    if (!invalidUrgentCreate.isEmpty()) {
      StringBuilder sb = new StringBuilder();
      invalidUrgentCreate.forEach((k, v) -> v.forEach(e -> {
        sb.append(k);
        sb.append(EXCEPTION_SEPARATION).append(e.getLeft());
        sb.append(EXCEPTION_SEPARATION).append(e.getMiddle());
        sb.append(EXCEPTION_SEPARATION).append(e.getRight());
        sb.append(SEPARATION);
      }));
      sb.setLength(sb.length() - 1);

      throw FareErrorCode.FA_ELIGIBLE_TRAIN_CANNOT_CREATE_URGENT_SETTING_WITHOUT_SAME_OR_NON_OVERLAPPING_TRAIN_DATE_RANGE_ELIGIBLE_TRAIN.toException(
          sb.toString());
    }

    // 確認完後再新增
    changeSet.getCreations().forEach(e -> {
      StringBuilder sb = new StringBuilder();
      sb.append(generateBasicDetailInfo(BULK_CREATE_OPERATION, e));
      try {
        EligibleTrain newEntity = new EligibleTrain();

        EligibleTrain savedEntity = commandService.create(newEntity, e);
        sb = new StringBuilder(generateBasicDetailInfo(BULK_CREATE_OPERATION, savedEntity));
        sb.append(BULK_SUCCESS);

        // email details
        setSuccessContentDetail(createFarePlans, EMAIL_CREATE, savedEntity);

      } catch (Exception e1) {
        sb.append(BULK_FAIL).append(ExceptionUtils.getMessage(e1));
        LOGGER_BASIC.exception(e1);
      }
      bulkResult.getCreations().add(sb.toString());
    });

    // 組裝 content, 送 email
    StringBuilder emailContent = new StringBuilder();
    emailContent.append(getEmailContent(deleteFarePlans, EMAIL_DELETE));
    emailContent.append(getEmailContent(createFarePlans, EMAIL_CREATE));
    if (!emailContent.toString().isEmpty()) {
      notifyOnlineUser(emailContent.toString(), interceptor.getUserEmail());
    }

    return bulkResult;
  }

  /**
   * 立即新增前檢核
   */
  private void checkUrgentCreate(List<EligibleTrainInput> createInputs,
      Map<String, List<Triple<LocalDate, LocalDate, LocalDate>>> invalidUrgentCreate) {
    createInputs.forEach(e -> {
      // 只檢核當天
      if (e.getEffSaleDate().isEqual(LocalDate.now())) {
        Long farePlanId =
            Objects.nonNull(e.getPromotionId()) ? e.getPromotionId() : e.getBasicFarePlanId();
        Optional<FarePlan> farePlan = farePlanRepo.findById(farePlanId);
        if (farePlan.isPresent() && !e.getIsAllTrainEligible() && !this.isCreateAllow(
            farePlan.get(), e)) {
          List<Triple<LocalDate, LocalDate, LocalDate>> invalidFarePlan = new ArrayList<>();

          List<Triple<LocalDate, LocalDate, LocalDate>> existInvalidFarePlan = invalidUrgentCreate.get(
              farePlan.get().getCode());
          if (Objects.nonNull(existInvalidFarePlan)) {
            invalidFarePlan.addAll(existInvalidFarePlan);
          }
          invalidFarePlan.add(Triple.of(e.getEffDate(), e.getDscDate(), e.getEffSaleDate()));

          invalidUrgentCreate.put(farePlan.get().getCode(), invalidFarePlan);
        }
      }
    });
  }

  private boolean isCreateAllow(FarePlan farePlan, EligibleTrainInput input) {
    List<EligibleTrain> sameDateSetting =
        repo.findByFarePlanAndEffSaleDateAndEffDateAndDscDateAndIsAllTrainEligibleFalse(farePlan,
            input.getEffSaleDate(), input.getEffDate(), input.getDscDate());

    if (sameDateSetting.isEmpty()) {
      List<EligibleTrain> sameSaleDate =
          repo.findByFarePlanAndEffSaleDateIsAndIsAllTrainEligibleFalse(farePlan,
              input.getEffSaleDate());
      // 一個重疊就不過->沒有相同發車日區間時，可接受與其他同開售日發車日區間無重疊的新增
      boolean isOverlapping = sameSaleDate.stream()
          .anyMatch(e -> commandService.isDateRangeOverlap(input, e));

      return !isOverlapping;
    }

    return true;
  }

  /**
   * 建立操作前基本訊息明細
   *
   * @param operation
   * @param input
   * @return 操作, 票價方案, 有無專案, 發車起日, 發車迄日, 開始訂位日, 指定車次, 不適用疏運期, 註記, 車次明細
   */
  private String generateBasicDetailInfo(String operation, EligibleTrainInput input) {

    String project =
        Objects.nonNull(input.getFareProjectId()) ? String.valueOf(input.getFareProjectId())
            : StringUtils.EMPTY;
    String farePlan = "";
    if (Objects.nonNull(input.getBasicFarePlanId())) {
      farePlan = String.valueOf(input.getBasicFarePlanId());
    }
    if (Objects.nonNull(input.getPromotionId())) {
      farePlan = String.valueOf(input.getPromotionId());
    }

    StringBuilder details = new StringBuilder();
    input.getDetails().forEach(detail -> {
      details.append(DayOfWeek.of(detail.getDayOfWeek()).toString(), 0, 3);
      details.append(SEPARATION);
      details.append(detail.getTrains().toString());
      details.append(";");
    });
    details.setLength(details.length() > 0 ? details.length() - 1 : 0);

    return operation.concat(SEPARATION)
        .concat(farePlan).concat(SEPARATION)
        .concat(project).concat(SEPARATION)
        .concat(input.getEffDate().toString()).concat(SEPARATION)
        .concat(input.getDscDate().toString()).concat(SEPARATION)
        .concat(input.getEffSaleDate().toString()).concat(SEPARATION)
        .concat(input.getIsAllTrainEligible() ? "ALL" : "").concat(SEPARATION)
        .concat(input.getIsHolidayIneligible() ? "" : "HOLIDAY_NA").concat(SEPARATION)
        .concat(input.getRemark()).concat(SEPARATION)
        .concat(details.toString()).concat(SEPARATION);
  }

  /**
   * 建立操作後基本訊息明細
   *
   * @param operation
   * @param entity
   * @return 操作, 票價方案, 有無專案, 發車起日, 發車迄日, 開始訂位日, 指定車次, 不適用疏運期, 註記, 車次明細
   */
  private String generateBasicDetailInfo(String operation, EligibleTrain entity) {

    String project =
        Objects.nonNull(entity.getProject()) ? entity.getProject().getCode() : StringUtils.EMPTY;
    String farePlan = entity.getFarePlan().getCode();

    StringBuilder details = new StringBuilder();
    entity.getDetails().forEach(detail -> {
      details.append(DayOfWeek.of(detail.getDayOfWeek()).toString(), 0, 3);
      details.append(SEPARATION);

      StringBuilder trains = new StringBuilder();
      detail.getTrains().forEach(train -> {
        trains.append(train.getTrainNum());
        trains.append(SEPARATION);
      });
      trains.setLength(trains.length() > 0 ? (trains.length() - 1) : 0);
      details.append(trains);
      details.append(";");
    });
    details.setLength(details.length() > 0 ? (details.length() - 1) : 0);

    return operation.concat(SEPARATION)
        .concat(farePlan).concat(SEPARATION)
        .concat(project).concat(SEPARATION)
        .concat(entity.getEffDate().toString()).concat(SEPARATION)
        .concat(entity.getDscDate().toString()).concat(SEPARATION)
        .concat(entity.getEffSaleDate().toString()).concat(SEPARATION)
        .concat(entity.getIsAllTrainEligible() ? "ALL" : "").concat(SEPARATION)
        .concat(entity.getIsHolidayIneligible() ? "" : "HOLIDAY_NA").concat(SEPARATION)
        .concat(entity.getRemark()).concat(SEPARATION)
        .concat(details.toString()).concat(SEPARATION);
  }

  private void setSuccessContentDetail(Map<String, List<String>> emailContentDetails,
      String operating, EligibleTrain entity) {
    String farePlan = String.format(FARE_PLAN_PATTERN, entity.getFarePlan().getCode(),
        entity.getFarePlan().getName());
    String detail = String.format(MAIL_CONTENT_DETAIL, farePlan, entity.getEffDate().toString(),
        entity.getDscDate().toString(), operating);

    if (emailContentDetails.containsKey(farePlan)) {
      List<String> details = new ArrayList<>(emailContentDetails.get(farePlan));
      details.add(detail);
      emailContentDetails.put(farePlan, details);
    } else {
      emailContentDetails.put(farePlan, Collections.singletonList(detail));
    }
  }

  private String getEmailContent(Map<String, List<String>> successList, String operation) {

    StringBuilder sb = new StringBuilder();
    if (!successList.isEmpty()) {
      // 明細
      StringBuilder detailSb = new StringBuilder();
      // 統計 by farePlan
      StringBuilder summarySb = new StringBuilder();
      // 總共刪除班數
      int[] count = {0};

      successList.forEach((k, v) -> {
        count[0] += v.size();

        summarySb.append(String.format(MAIL_CONTENT_SUMMARY, k, operation, v.size()));
        summarySb.append(CONTENT_SEPARATION);

        v.forEach(e -> {
          detailSb.append(e);
          detailSb.append(CONTENT_SEPARATION);
        });
      });

      sb.append(String.format(MAIL_CONTENT, operation, detailSb, summarySb, operation, count[0]));
      sb.append(CONTENT_SEPARATION);
      sb.append(CONTENT_SEPARATION);
    }

    return sb.toString();
  }

  private void notifyOnlineUser(String basePattern, String userEmail) {

    emailService.sendSimpleMessage(userEmail, RESULT_MAIL_SUBJECT, basePattern);
  }
}
