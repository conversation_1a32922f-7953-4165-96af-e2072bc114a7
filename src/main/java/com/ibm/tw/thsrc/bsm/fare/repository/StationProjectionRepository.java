/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fare.entity.StationProjection;
import com.ibm.tw.thsrc.bsm.sc.enums.StationOperationalStatus;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface StationProjectionRepository extends JpaRepository<StationProjection, Long> {

  List<StationProjection> findByStationOperationalStatus(
      StationOperationalStatus stationOperationalStatus);

  List<StationProjection> findByStationOperationalStatusNot(
      StationOperationalStatus stationOperationalStatus);
}
