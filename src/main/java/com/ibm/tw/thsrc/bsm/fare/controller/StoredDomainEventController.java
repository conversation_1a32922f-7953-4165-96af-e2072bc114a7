/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.core.event.ExceptionMessages;
import com.ibm.tw.thsrc.bsm.core.event.StoredDomainEventCommandService;
import com.ibm.tw.thsrc.bsm.core.event.StoredDomainEventQueryService;
import com.ibm.tw.thsrc.bsm.exception.FailedDependencyException;
import com.ibm.tw.thsrc.bsm.fa.api.StoredFareDomainEventApi;
import com.ibm.tw.thsrc.bsm.op.dto.StoredDomainEventReplayInput;
import java.util.Optional;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/StoredDomainEvents")
public class StoredDomainEventController implements
    StoredFareDomainEventApi {

  @Autowired
  private StoredDomainEventCommandService commandService;

  @Autowired
  private StoredDomainEventQueryService queryService;

  @Autowired
  private ExceptionMessages exceptionMessages;

  @Override
  public void replay(@Valid @RequestBody StoredDomainEventReplayInput input) {
    queryService
        .getUncompletedDomainEvents(input.getOpFunction().code, input.getCorrelationId())
        .forEach(domainEvent -> commandService.replay(domainEvent, input.getRetryOption()));

    // notify the failed outbound operation if any associated event uncompleted.
    throwIfFailedOutboundOperation(input.getCorrelationId());

  }

  private void throwIfFailedOutboundOperation(String associationId) {
    Optional<FailedDependencyException> opt = exceptionMessages.pop(associationId);
    if (opt.isPresent()) {
      throw opt.get();
    }
  }
}

