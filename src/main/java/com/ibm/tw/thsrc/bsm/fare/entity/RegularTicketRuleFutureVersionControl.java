/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.entity;

import com.ibm.tw.thsrc.bsm.cronjob.entity.FutureVersionControl;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@Entity
public class RegularTicketRuleFutureVersionControl extends FutureVersionControl {

  @ManyToOne
  @JoinColumn(foreignKey = @javax.persistence.ForeignKey(value = ConstraintMode.NO_CONSTRAINT))
  private RegularTicketRuleTemp regularTicketRuleTemp;
}
