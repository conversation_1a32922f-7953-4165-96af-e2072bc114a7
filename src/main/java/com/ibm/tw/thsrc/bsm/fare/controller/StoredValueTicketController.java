/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.fa.api.StoredValueTicketApi;
import com.ibm.tw.thsrc.bsm.fa.dto.StoredValueTicketInput;
import com.ibm.tw.thsrc.bsm.fa.dto.StoredValueTicketOutput;
import com.ibm.tw.thsrc.bsm.fare.service.StoredValueTicketCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.StoredValueTicketQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/StoredValueTickets")
public class StoredValueTicketController implements StoredValueTicketApi {

  @Autowired
  private StoredValueTicketCommandService commandService;

  @Autowired
  private StoredValueTicketQueryService queryService;

  @Autowired
  private EventStore eventStore;

  @Override
  public Page<StoredValueTicketOutput> search(Search search) {
    return queryService.search();
  }

  @Override
  public StoredValueTicketOutput replace(Long id, StoredValueTicketInput input) {
    eventStore.validateFunctionLock(OperationFunction.STORED_VALUE_TICKET);
    commandService.update(id, input);
    return queryService.read(id);
  }

  @Override
  public StoredValueTicketOutput read(Long id) {
    return queryService.read(id);
  }
}
