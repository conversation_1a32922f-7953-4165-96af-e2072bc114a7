/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.BasicFarePlanOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.BasicFarePriceDetailOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.BasicFarePriceOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.PassengerCarTypeProjectionOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.FareType;
import com.ibm.tw.thsrc.bsm.fa.enums.ServiceType;
import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePlan;
import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePlan_;
import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePrice;
import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePriceDetail;
import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePrice_;
import com.ibm.tw.thsrc.bsm.fare.entity.FarePlan_;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerCarTypeProjection;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerCarTypeProjection_;
import com.ibm.tw.thsrc.bsm.fare.repository.BasicFarePriceRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.BasicFarePriceFilter;
import com.ibm.tw.thsrc.bsm.fare.vo.BasicFarePriceSort;
import com.ibm.tw.thsrc.bsm.sc.dto.StationPairOutput;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.TypedSort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class BasicFarePriceQueryService extends
    AbstractQueryService<BasicFarePrice, BasicFarePriceOutput, BasicFarePriceFilter, BasicFarePriceSort>
    implements BasicFarePriceQueryApi {

  @Autowired
  private BasicFarePriceRepository repo;

  @Autowired
  private ObjectMapper objectMapper;

  @Override
  public BasicFarePriceOutput read(Long id) {
    BasicFarePrice entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    return this.translate(entity);
  }

  public Set<BasicFarePriceOutput> read(Set<Long> ids) {
    return repo.findAllById(ids).stream().map(this::translate).collect(Collectors.toSet());
  }

  @Override
  public Page<BasicFarePriceOutput> search(Search searchRequest) {
    PageRequest pr = translateToPageRequest(searchRequest);
    Specification<BasicFarePrice> spec = translateToSpecification(searchRequest);
    Page<BasicFarePrice> page = repo.findAll(spec, pr);
    return page.map(this::translate);
  }

  @Override
  public BasicFarePriceOutput translate(BasicFarePrice entity) {
    BasicFarePriceOutput response = new BasicFarePriceOutput();
    response.setId(entity.getId());
    response.setEffDate(entity.getEffDate());
    response.setDscDate(entity.getDscDate());
    response.setDataVersion(entity.getDataVersion());

    BasicFarePlanOutput farePlanOutput = new BasicFarePlanOutput();
    farePlanOutput.setId(entity.getBasicFarePlan().getId());
    farePlanOutput.setCode(entity.getBasicFarePlan().getCode());
    farePlanOutput.setName(entity.getBasicFarePlan().getName());
    PassengerCarTypeProjectionOutput passengerCarTypeOutput =
        new PassengerCarTypeProjectionOutput();
    passengerCarTypeOutput.setCode(entity.getBasicFarePlan().getPassengerCarType().getCode());
    passengerCarTypeOutput.setEnName(entity.getBasicFarePlan().getPassengerCarType().getEnName());
    passengerCarTypeOutput.setZhName(entity.getBasicFarePlan().getPassengerCarType().getZhName());
    passengerCarTypeOutput
        .setCompartmentType(entity.getBasicFarePlan().getPassengerCarType().getCompartmentType());
    farePlanOutput.setPassengerCarType(passengerCarTypeOutput);
    farePlanOutput.setFareType(entity.getBasicFarePlan().getFareType());
    farePlanOutput.setServiceType(entity.getBasicFarePlan().getServiceType());
    farePlanOutput.setEffDate(entity.getBasicFarePlan().getEffDate());
    farePlanOutput.setDscDate(entity.getBasicFarePlan().getDscDate());
    farePlanOutput.setType(entity.getBasicFarePlan().getType());
    farePlanOutput.setDisplayCode(entity.getBasicFarePlan().getDisplayCode());
    response.setFarePlan(farePlanOutput);

    List<BasicFarePriceDetailOutput> detailOutputs = new ArrayList<>();
    for (BasicFarePriceDetail detail : entity.getDetails()) {
      BasicFarePriceDetailOutput detailOutput = new BasicFarePriceDetailOutput();
      detailOutput.setId(detail.getId());
      StationPairOutput pairOutput = new StationPairOutput();
      pairOutput.setId(detail.getStationPairProjection().getId());
      pairOutput.setDepartureStationCode(detail.getStationPairProjection().getDepartureCode());
      pairOutput.setDepartureStationName(detail.getStationPairProjection().getDepartureName());
      pairOutput.setArrivalStationCode(detail.getStationPairProjection().getArrivalCode());
      pairOutput.setArrivalStationName(detail.getStationPairProjection().getArrivalName());
      detailOutput.setStationPair(pairOutput);
      detailOutput.setUnitPrice(detail.getUnitPrice());
      detailOutputs.add(detailOutput);
    }
    response.setBasicFareMatrix(detailOutputs);
    return response;
  }

  @Override
  protected Sort translate(BasicFarePriceSort sortBy, Sort.Direction direction) {
    TypedSort<BasicFarePrice> typedSort = Sort.sort(BasicFarePrice.class);

    if (sortBy.isFarePlanCode()) {
      typedSort//
          .by(BasicFarePrice::getBasicFarePlan)//
          .by(BasicFarePlan::getCode);
    } else if (sortBy.isFarePlanName()) {
      typedSort//
          .by(BasicFarePrice::getBasicFarePlan)//
          .by(BasicFarePlan::getName);
    } else if (sortBy.isEffDate()) {
      typedSort//
          .by(BasicFarePrice::getEffDate);
    } else if (sortBy.isDscDate()) {
      typedSort//
          .by(BasicFarePrice::getDscDate);
    } else if (sortBy.isPassengerCarType()) {
      typedSort//
          .by(BasicFarePrice::getBasicFarePlan).by(BasicFarePlan::getPassengerCarType)
          .by(PassengerCarTypeProjection::getCode);
    }

    if (typedSort.isSorted()) {
      if (Sort.Direction.DESC == direction) {
        return typedSort.descending();
      } else {
        return typedSort.ascending();
      }
    } else {
      return typedSort;
    }
  }

  @Override
  protected Predicate translate(ComparisonExpression<BasicFarePriceFilter> expression,
      Root<BasicFarePrice> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {

    ComparisonOperator op = expression.getOperator();
    if (expression.getAttributeValue().getFarePlanCode() != null) {

      Path<String> path = root.join(BasicFarePrice_.BASIC_FARE_PLAN).get(FarePlan_.CODE);
      String value = expression.getAttributeValue().getFarePlanCode();
      return translate(path, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getFarePlanName() != null) {

      Path<String> path = root.join(BasicFarePrice_.BASIC_FARE_PLAN).get(FarePlan_.NAME);
      String value = expression.getAttributeValue().getFarePlanName();
      return translate(path, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getServiceType() != null) {

      Path<ServiceType> path =
          root.join(BasicFarePrice_.BASIC_FARE_PLAN).get(BasicFarePlan_.SERVICE_TYPE);
      String value = expression.getAttributeValue().getServiceType();
      return translate(path, op, ServiceType.valueOf(value), criteriaBuilder);

    } else if (expression.getAttributeValue().getFareType() != null) {

      Path<FareType> path =
          root.join(BasicFarePrice_.BASIC_FARE_PLAN).get(BasicFarePlan_.FARE_TYPE);
      String value = expression.getAttributeValue().getFareType();
      return translate(path, op, FareType.valueOf(value), criteriaBuilder);

    } else if (expression.getAttributeValue().getStartTrainDate() != null) {

      Path<LocalDate> path = root.get(BasicFarePrice_.EFF_DATE);
      return translate(path, op, expression.getAttributeValue().getStartTrainDate(),
          criteriaBuilder);

    } else if (expression.getAttributeValue().getEndTrainDate() != null) {

      Path<LocalDate> path = root.get(BasicFarePrice_.DSC_DATE);
      return translate(path, op, expression.getAttributeValue().getEndTrainDate(), criteriaBuilder);

    } else if (expression.getAttributeValue().getPassengerCarType() != null) {

      Path<String> path = root.join(BasicFarePrice_.BASIC_FARE_PLAN)
          .join(BasicFarePlan_.PASSENGER_CAR_TYPE).get(PassengerCarTypeProjection_.CODE);
      String value = expression.getAttributeValue().getPassengerCarType();
      return translate(path, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getIsFuture() != null) {

      Path<LocalDate> path = root.get(BasicFarePrice_.EFF_DATE);
      boolean isFuture = expression.getAttributeValue().getIsFuture();
      return translateIsFuturePredicate(path, isFuture, criteriaBuilder);

    } else if (expression.getAttributeValue().getHasBeenEffected() != null) {
      Path<LocalDate> path = root.get(BasicFarePrice_.EFF_DATE);
      boolean hasBeenEffected = expression.getAttributeValue().getHasBeenEffected();
      return hasBeenEffected ?
          translate(path, ComparisonOperator.LE, LocalDate.now(), criteriaBuilder) :
          translate(path, ComparisonOperator.GT, LocalDate.now(), criteriaBuilder);
    } else {
      return null;
    }
  }


  private Predicate translateIsFuturePredicate(Path<LocalDate> path, Boolean isFuture,
      CriteriaBuilder criteriaBuilder) {

    if (Boolean.TRUE.equals(isFuture)) {

      return translate(path, ComparisonOperator.GT, LocalDate.now(), criteriaBuilder);

    } else {

      Optional<BasicFarePrice> currentPrice =
          repo.findAll().stream()
              .filter(e -> e.getBasicFarePlan().getServiceType().equals(ServiceType.FREE_SEATING))
              .filter(e -> e.getEffDate() != null)
              .filter(e -> !e.getEffDate().isAfter(LocalDate.now()))
              .sorted(Comparator.comparing(BasicFarePrice::getEffDate).reversed())
              .findFirst();

      return translate(path,
          currentPrice.isPresent() ? ComparisonOperator.EQ : ComparisonOperator.LE,
          currentPrice.isPresent() ? currentPrice.get().getEffDate() : LocalDate.now(),
          criteriaBuilder);
    }

  }

  @Override
  protected Function<Map<String, Boolean>, BasicFarePriceSort> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, BasicFarePriceSort.class);
  }

  @Override
  protected Function<Map<String, Object>, BasicFarePriceFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, BasicFarePriceFilter.class);
  }

  @Override
  public List<BasicFarePriceOutput> getBasicFarePrices(Boolean isFuture, ServiceType freeSeating) {

    List<BasicFarePrice> currentOrFutureFreeSeatingPrices = repo.findAll().stream()//
        .filter(e -> e.getBasicFarePlan().getServiceType().equals(freeSeating))
        .filter(Boolean.TRUE.equals(isFuture) ?
            e -> e.getEffDate().isAfter(LocalDate.now()) :
            e -> e.getEffDate().isBefore(LocalDate.now())
                || e.getEffDate().isEqual(LocalDate.now()))
        .collect(Collectors.toList());

    List<String> distinctFarePlanCodes =
        currentOrFutureFreeSeatingPrices.stream()
            .map(e -> e.getBasicFarePlan().getCode())
            .distinct()
            .collect(Collectors.toList());

    List<BasicFarePrice> mostRecentOrImmediateFuturePrices = new ArrayList<>();

    for (String code : distinctFarePlanCodes) {
      BasicFarePrice target = currentOrFutureFreeSeatingPrices.stream()
          .filter(e -> e.getBasicFarePlan().getCode().equals(code))
          .sorted(
              Boolean.TRUE.equals(isFuture) ?
                  Comparator.comparing(BasicFarePrice::getEffDate) :
                  Comparator.comparing(BasicFarePrice::getEffDate, Comparator.reverseOrder())
          )
          .collect(Collectors.toList()).get(0);
      mostRecentOrImmediateFuturePrices.add(target);
    }

    return mostRecentOrImmediateFuturePrices.stream()
        .map(this::translate)
        .collect(Collectors.toList());
  }

  /*
   * 自由座票價
   * effDate=null->查現在版
   * effDate=特定日期->查特定版本
   * */
  @Override
  public BasicFarePriceOutput getBasicFarePrices(String farePlanCode, LocalDate effDate) {
    // 預設查現在版，若有特定版本版->查該版本
    List<BasicFarePrice> farePrices = repo.findAll().stream()//
        .filter(e -> Objects.nonNull(e.getBasicFarePlan().getCode())
            && e.getBasicFarePlan().getCode().equals(farePlanCode))//
        .filter(Objects.nonNull(effDate) ? e -> e.getEffDate().isEqual(effDate)
            : e -> !e.getEffDate().isAfter(LocalDate.now()))
        .sorted(Comparator.comparing(BasicFarePrice::getEffDate, Comparator.reverseOrder()))
        .collect(Collectors.toList());

    return farePrices.isEmpty() ? null : translate(farePrices.get(0));
  }

  @Override
  public BasicFarePriceOutput getBasicFarePrices(Boolean isFuture, String farePlanCode) {
    List<BasicFarePrice> farePrices = repo.findAll().stream()//
        .filter(e -> e.getBasicFarePlan().getCode().equals(farePlanCode))//
        .filter(Boolean.TRUE.equals(isFuture) ? e -> e.getEffDate().isAfter(LocalDate.now())
            : e -> e.getEffDate().isBefore(LocalDate.now())
                || e.getEffDate().isEqual(LocalDate.now()))
        .sorted(Comparator.comparing(BasicFarePrice::getEffDate, Comparator.reverseOrder()))//
        .collect(Collectors.toList());

    return farePrices.isEmpty() ? null : translate(farePrices.get(0));
  }

}
