/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm;

import com.cosium.spring.data.jpa.entity.graph.repository.support.EntityGraphJpaRepositoryFactoryBean;
import com.ibm.tw.thsrc.bsm.fare.repository.ReissueTicketRefundFeeJpaRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.ReissueTicketRefundFeeRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.ReissueTicketRefundFeeResRepositoryDecorator;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@SpringBootApplication
@EnableAutoConfiguration(exclude = {ErrorMvcAutoConfiguration.class})
@EnableJpaRepositories(repositoryFactoryBeanClass = EntityGraphJpaRepositoryFactoryBean.class)
public class FareApplication {

  public static void main(String[] args) {
    SpringApplication.run(FareApplication.class, args);
  }

  @Primary
  @Bean
  public ReissueTicketRefundFeeRepository reissueTicketRefundFeeRepository(
      ReissueTicketRefundFeeJpaRepository component) {
    return new ReissueTicketRefundFeeResRepositoryDecorator(component, component);
  }
}
