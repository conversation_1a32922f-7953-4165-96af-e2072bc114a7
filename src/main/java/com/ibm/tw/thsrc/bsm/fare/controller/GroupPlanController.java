/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.fa.api.GroupPlanApi;
import com.ibm.tw.thsrc.bsm.fa.dto.GroupPlanInput;
import com.ibm.tw.thsrc.bsm.fa.dto.GroupPlanOutput;
import com.ibm.tw.thsrc.bsm.fare.service.GroupPlanCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.GroupPlanQueryService;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 團體票
 */
@RestController
@RequestMapping(value = "/GroupPlans")
public class GroupPlanController implements GroupPlanApi {

  @Autowired
  GroupPlanCommandService commandService;

  @Autowired
  GroupPlanQueryService queryService;
  
  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private EventStore eventStore;

  @Override
  public Page<GroupPlanOutput> search(@Valid Search search) {
    return queryService.search(search);
  }

  @Override
  public GroupPlanOutput create(@Valid GroupPlanInput input) {
    eventStore.validateFunctionLock(OperationFunction.GROUP_PLAN);
    Long id = commandService.create(input);
    // notify the failed outbound operation if any associated event uncompleted.
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
    return queryService.read(id);
  }

  @Override
  public GroupPlanOutput read(Long id) {
    return queryService.read(id);
  }

  @Override
  public GroupPlanOutput replace(Long id, @Valid GroupPlanInput input) {
    eventStore.validateFunctionLock(OperationFunction.GROUP_PLAN);
    commandService.update(id, input);
    // notify the failed outbound operation if any associated event uncompleted.
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
    return queryService.read(id);
  }

  @Override
  public void delete(Long id) {
    eventStore.validateFunctionLock(OperationFunction.GROUP_PLAN);
    commandService.delete(id);
    // notify the failed outbound operation if any associated event uncompleted.
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
  }
}
