/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.translate;

import com.ibm.tw.thsrc.bsm.fa.dto.ProfileDiscountPlanOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountPlan;

public class ProfileDiscountPlanTranslator {

  private ProfileDiscountPlanTranslator() {
    throw new IllegalStateException("ProfileDiscountPlanTranslator class");
  }

  public static ProfileDiscountPlanOutput toProfileDiscountPlanOutput(
      ProfileDiscountPlan aggregateRoot) {
    ProfileDiscountPlanOutput output = new ProfileDiscountPlanOutput();
    output.setId(aggregateRoot.getId());
    output.setDiscountPct(aggregateRoot.getDiscountPct());
    output.setDiscountTypeId(aggregateRoot.getProfileDiscountType().getId());
    output.setDiscountTypeCode(aggregateRoot.getProfileDiscountType().getCode());
    output.setDiscountTypeName(aggregateRoot.getProfileDiscountType().getName());
    output.setProfileCode(aggregateRoot.getPassengerProfile().getCode());
    output.setProfileZhName(aggregateRoot.getPassengerProfile().getZhName());
    output.setProfileDisplayOrder(aggregateRoot.getPassengerProfile().getDisplayOrder());
    output.setDataVersion(aggregateRoot.getDataVersion());
    return output;

  }
}
