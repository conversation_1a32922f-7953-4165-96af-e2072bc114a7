/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainFileInput;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainFile;
import com.ibm.tw.thsrc.bsm.fare.repository.EligibleTrainFileRepository;
import java.time.LocalDateTime;
import java.util.Objects;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class EligibleTrainFileCommandService extends
    AbstractCommandService<EligibleTrainFile, EligibleTrainFileInput> {

  @Autowired
  private EligibleTrainFileRepository repo;

  @Override
  public Long create(EligibleTrainFileInput input) {

    EligibleTrainFile newEntity = new EligibleTrainFile();
    this.writeAggregateRootValue(newEntity, input);
    this.validateInvariants(newEntity);

    EligibleTrainFile savedEntity = repo.save(newEntity);
    return savedEntity.getId();
  }

  @Override
  public void update(Long id, EligibleTrainFileInput input) {
    EligibleTrainFile entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    this.writeAggregateRootValue(entity, input);
    this.validateInvariants(entity);
    repo.save(entity);
  }

  @Override
  public void delete(Long id) {
    throw new UnsupportedOperationException();
  }

  @Override
  protected void validateInvariants(EligibleTrainFile entity) throws UnprocessableException {
    // not needed for now
  }

  @Override
  protected void writeAggregateRootValue(EligibleTrainFile entity, EligibleTrainFileInput input) {
    entity.setFileName(input.getFileName());
    entity.setFilePath(input.getFilePath());
    if (Objects.nonNull(input.getUploadEmployeeId())) {
      entity.setUploadTimestamp(LocalDateTime.now());
      entity.setUploadEmployeeId(input.getUploadEmployeeId());
    }

    if (Objects.nonNull(input.getLatestDownloadEmployeeId())) {
      entity.setLastDownloadTimestamp(LocalDateTime.now());
      entity.setLastDownloadEmployeeId(input.getLatestDownloadEmployeeId());
    }
  }
}
