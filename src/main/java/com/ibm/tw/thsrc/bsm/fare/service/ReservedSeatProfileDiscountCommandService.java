/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.ConflictException;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.ProfileDiscountPlanInput;
import com.ibm.tw.thsrc.bsm.fa.dto.ProfileDiscountTypeInput;
import com.ibm.tw.thsrc.bsm.fa.dto.ReservedSeatProfileDiscountInput;
import com.ibm.tw.thsrc.bsm.fare.domain.event.BatchProfileDiscountPlanReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountPlan;
import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountType;
import com.ibm.tw.thsrc.bsm.fare.repository.ProfileDiscountPlanRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.ProfileDiscountTypeRepository;
import com.ibm.tw.thsrc.bsm.message.fare.ProfileDiscountPlanData;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Service
public class ReservedSeatProfileDiscountCommandService {

  @Autowired
  private HttpHeaderInterceptor interceptor;
  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;
  @Autowired
  private ProfileDiscountPlanRepository profileDiscountPlanRepository;
  @Autowired
  private ProfileDiscountTypeRepository profileDiscountTypeRepository;

  @Autowired
  private EventStore eventStore;

  @Transactional
  public void replace(ReservedSeatProfileDiscountInput input) {
    List<ProfileDiscountPlan> profileDiscountPlans = new ArrayList<>();
    input.getReservedSeatProfileDiscounts().stream()
        .forEach(e -> profileDiscountPlans.add(updateProfileDiscount(e)));

    input.getReservedSeatDiscountTypes().forEach(this::updateDiscountType);

    BatchProfileDiscountPlanReplaced event = new BatchProfileDiscountPlanReplaced(
        interceptor.getUserId(), interceptor.getCorrelationId(),
        profileDiscountPlans.stream()
            .map(it -> new ProfileDiscountPlanData(it.getId(), it.getProfileDiscountType().getId(),
                it.getProfileDiscountType().getCode(), it.getPassengerProfile().getCode(),
                it.getDiscountPct().intValue()))
            .collect(Collectors.toList()));

    event.setOperationId(OperationFunction.RESERVED_SEAT_PROFILE_DISCOUNT.name());
    event.setAssociationId(interceptor.getCorrelationId());
    eventStore.publishEvent(event);
  }

  private void updateDiscountType(ProfileDiscountTypeInput input) {
    ProfileDiscountType aggregateRoot = profileDiscountTypeRepository.findById(input.getId())
        .orElseThrow(ResourceNotFoundException::new);
    if (!aggregateRoot.getDataVersion().equals(input.getDataVersion())) {
      throw new ConflictException();
    }

    aggregateRoot.setName(input.getName());
  }

  private ProfileDiscountPlan updateProfileDiscount(ProfileDiscountPlanInput input) {
    ProfileDiscountPlan aggregateRoot = profileDiscountPlanRepository.findById(input.getId())
        .orElseThrow(ResourceNotFoundException::new);
    if (!aggregateRoot.getDataVersion().equals(input.getDataVersion())) {
      throw new ConflictException();
    }
    aggregateRoot.setDiscountPct(input.getDiscountPct());

    return aggregateRoot;
  }
}
