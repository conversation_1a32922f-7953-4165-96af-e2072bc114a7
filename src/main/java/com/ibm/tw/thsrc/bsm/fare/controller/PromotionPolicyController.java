/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.fa.api.PromotionPolicyApi;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionPolicyOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionPolicyQueryByChannelCodeInput;
import com.ibm.tw.thsrc.bsm.fare.service.PromotionPolicyQueryService;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/PromotionPolicies")
public class PromotionPolicyController implements PromotionPolicyApi {

  @Autowired
  private PromotionPolicyQueryService queryService;

  @Override
  public PromotionPolicyOutput queryByChannelCode(
      @Valid @RequestBody PromotionPolicyQueryByChannelCodeInput input) {
    return queryService.queryByChannelCode(input);
  }
}
