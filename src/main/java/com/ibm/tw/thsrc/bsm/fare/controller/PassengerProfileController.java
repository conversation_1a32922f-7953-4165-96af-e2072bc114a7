/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.event.ExceptionMessages;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.fa.api.PassengerProfileApi;
import com.ibm.tw.thsrc.bsm.fa.dto.PassengerProfileInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PassengerProfileOutput;
import com.ibm.tw.thsrc.bsm.fare.service.PassengerProfileCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.PassengerProfileQueryService;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/PassengerProfiles")
public class PassengerProfileController implements PassengerProfileApi {

  @Autowired
  private PassengerProfileCommandService commandService;
  @Autowired
  private PassengerProfileQueryService queryService;

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private EventStore eventStore;

  @Autowired
  private ExceptionMessages exceptionMessages;

  @Override
  public Set<PassengerProfileOutput> modify(
      CollectionModificationInput<PassengerProfileInput> changeSet) {
    eventStore.validateFunctionLock(OperationFunction.PASSENGER_PROFILE_DISCOUNT);

    Set<Long> ids = commandService.patch(changeSet);
    // notify the failed outbound operation if any associated event uncompleted.
    eventStore.throwIfFailedOutboundOperationWithoutLock(interceptor.getCorrelationId());
    return queryService.read(ids);
  }

  @Override
  public Page<PassengerProfileOutput> search(Search search) {
    return queryService.search(search);
  }

}
