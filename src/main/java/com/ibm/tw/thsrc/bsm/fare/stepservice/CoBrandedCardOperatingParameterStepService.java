/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.stepservice;

import com.ibm.tw.thsrc.bsm.cronjob.helper.FutureVersionControlStatusHelper;
import com.ibm.tw.thsrc.bsm.cronjob.service.StepService;
import com.ibm.tw.thsrc.bsm.cronjob.utils.StepUtils;
import com.ibm.tw.thsrc.bsm.cronjob.value.StepResult;
import com.ibm.tw.thsrc.bsm.cronjob.value.TaskExecution;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardOperatingParameterFutureVersionControl;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardOperatingParameterFutureVersionControlRepository;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardOperatingParameterCommandService;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CoBrandedCardOperatingParameterStepService implements StepService<String> {

  @Autowired
  private CoBrandedCardOperatingParameterCommandService commandService;

  @Autowired
  private CoBrandedCardOperatingParameterFutureVersionControlRepository repo;

  @Override
  public StepResult<String> execute(TaskExecution taskExecution) {
    Map<String, Object> parameters = taskExecution.getParameters();
    Long futureVersionControlId = Long.valueOf((String) parameters.get("futureVersionControlId"));

    CoBrandedCardOperatingParameterFutureVersionControl futureVersionControl =
        repo.findById(futureVersionControlId).orElseThrow(ResourceNotFoundException::new);

    FutureVersionControlStatusHelper futureVersionHelper =
        new FutureVersionControlStatusHelper(futureVersionControl);

    try {
      futureVersionHelper.start();

      commandService.cronJob(futureVersionControl.getEffDate(),
          futureVersionControl.getCreateUser());

      futureVersionHelper.complete();
    } catch (Exception e) {
      futureVersionHelper.fail();
      throw FareErrorCode.FA_CO_BRANDED_CARD_OPERATING_PARAMETER_STEP_ERROR
          .toException(e.getMessage());

    } finally {
      futureVersionHelper.finish();
      repo.save(futureVersionControl);
    }

    log.debug("step service executed");

    return StepUtils.defaultCompletedResult();
  }
}
