/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.SmallGroupPlanInput;
import com.ibm.tw.thsrc.bsm.fare.domain.event.SmallGroupPlanCreated;
import com.ibm.tw.thsrc.bsm.fare.domain.event.SmallGroupPlanDeleted;
import com.ibm.tw.thsrc.bsm.fare.domain.event.SmallGroupPlanReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerProfile;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan;
import com.ibm.tw.thsrc.bsm.fare.entity.SmallGroupPlan;
import com.ibm.tw.thsrc.bsm.fare.repository.PassengerProfileRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionPlanRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.SmallGroupPlanRepository;
import com.ibm.tw.thsrc.bsm.message.fare.SmallGroupPlanData;
import com.ibm.tw.thsrc.bsm.message.fare.SmallGroupPlanData.SmallGroupPlanDataBuilder;
import com.ibm.tw.thsrc.bsm.util.StringUtils;
import java.time.LocalDate;
import java.util.Optional;
import javax.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Transactional
@Service
public class SmallGroupPlanCommandService extends
    AbstractCommandService<SmallGroupPlan, SmallGroupPlanInput> {

  private static final String FLEXIBLE_VOL_ACC_PRF = "#";

  private final Logger log = LoggerFactory.getLogger(this.getClass());

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Autowired
  private SmallGroupPlanRepository repo;

  @Autowired
  private PromotionPlanRepository promotionPlanRepo;

  @Autowired
  private PassengerProfileRepository passengerProfileRepo;

  @Override
  public Long create(SmallGroupPlanInput input) {

    SmallGroupPlan entity = new SmallGroupPlan();
    this.writeAggregateRootValue(entity, input);
    this.validateInvariants(entity);
    entity = repo.save(entity);

    // 發布RES事件
    SmallGroupPlanCreated event = new SmallGroupPlanCreated(
        interceptor.getUserId(),
        interceptor.getCorrelationId());
    event.setMsgPayload(this.toMsgPayload(entity));
    applicationEventPublisher.publishEvent(event);

    return entity.getId();
  }

  @Override
  public void update(Long id, SmallGroupPlanInput input) {

    SmallGroupPlan entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    // 樂觀鎖
    this.validateConcurrency(entity, input.getDataVersion());

    this.writeAggregateRootValue(entity, input);
    this.validateInvariants(entity);
    entity = repo.save(entity);

    // 發布RES事件
    SmallGroupPlanReplaced event = new SmallGroupPlanReplaced(
        interceptor.getUserId(),
        interceptor.getCorrelationId());
    event.setMsgPayload(this.toMsgPayload(entity));
    applicationEventPublisher.publishEvent(event);

    log.info("update - end");
  }

  @Override
  public void delete(Long id) {

    SmallGroupPlan entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    repo.delete(entity);

    // 發布RES事件
    SmallGroupPlanDeleted event = new SmallGroupPlanDeleted(
        interceptor.getUserId(),
        interceptor.getCorrelationId());
    event.setMsgPayload(this.toMsgPayload(entity));
    applicationEventPublisher.publishEvent(event);

    log.info("delete - end");
  }

  @Override
  protected void validateInvariants(SmallGroupPlan aggregateRoot) throws UnprocessableException {
    LocalDate planEffDate = aggregateRoot.getPromotionPlan().getEffDate();
    LocalDate planDscDate = aggregateRoot.getPromotionPlan().getDscDate();
    String planEffDateTxt = planEffDate == null ? "#" : planEffDate.toString();
    String planDscDateTxt = planDscDate == null ? "#" : planDscDate.toString();

    // 小團體計畫起日 需晚於促銷碼起日內
    // 小團體計畫起日 需早於促銷碼訖日
    if ((aggregateRoot.getEffDate().compareTo(planEffDate) < 0) || (planDscDate != null
        && aggregateRoot.getEffDate().compareTo(planDscDate) > 0)) {
      throw FareErrorCode.FA_ILLEGAL_SMALL_GROUP_PLAN_EFF_DATE.toException(planEffDateTxt, planDscDateTxt);
    }

    // 小團體計畫訖日 需晚於促銷碼起日
    if (aggregateRoot.getDscDate() != null
        && aggregateRoot.getDscDate().compareTo(planEffDate) < 0) {
      throw FareErrorCode.FA_ILLEGAL_SMALL_GROUP_PLAN_DSC_DATE.toException(planEffDateTxt, planDscDateTxt);
    }

    // 小團體計畫訖日 需早於促銷碼訖日
    if (planDscDate != null
        && aggregateRoot.getDscDate() != null
        && aggregateRoot.getDscDate().compareTo(planDscDate) > 0) {
      throw FareErrorCode.FA_ILLEGAL_SMALL_GROUP_PLAN_DSC_DATE.toException(planEffDateTxt, planDscDateTxt);
    }

    // 總人數 必須相等於 適用身分別人數 + 搭配身分別人數
    // 若搭配身分別為＃，即變動適用人數，不檢核上述條件，並強制搭配身分別人數為0
    // note: 搭配身分別可不選擇(null) 與 其人數亦可為空 (null)

    int groupSize = aggregateRoot.getGroupSize();
    int mainProfileQty = aggregateRoot.getMainProfileQty();
    int accProfileQty = aggregateRoot.getAccompanyProfileQty() == null ? 0 :
        aggregateRoot.getAccompanyProfileQty();

    if (FLEXIBLE_VOL_ACC_PRF.equals(aggregateRoot.getAccompanyProfileExpr())) {
      aggregateRoot.setAccompanyProfileQty(Integer.valueOf(0));
    } else if (groupSize != mainProfileQty + accProfileQty) {
      throw FareErrorCode.FA_SMALL_GROUP_INVALID_VOLUME.toException();
    }
  }

  @Override
  protected void writeAggregateRootValue(SmallGroupPlan aggregateRoot, SmallGroupPlanInput input) {

    PromotionPlan promotionPlan = promotionPlanRepo
        .findByCode(input.getCode())
        .orElseThrow(
            () -> FareErrorCode.FA_SMALL_GROUP_PLAN_CODE_CANNOT_ASSOCIATE_PROMOTION_PLAN
                .toException(input.getCode()));

    // 查詢passengerProfile取得該身分別的中文列印名稱，查不到該ProfileExpr就回空值
    Optional<PassengerProfile> mainProfile = passengerProfileRepo
        .findByCode(input.getMainProfileExpr());
    Optional<PassengerProfile> acccompanyProfile = passengerProfileRepo
        .findByCode(input.getAccompanyProfileExpr());
    String mainProfileName = mainProfile.isPresent() ? mainProfile.get().getZhName() : "";
    String acccompanyProfileName =
        acccompanyProfile.isPresent() ? acccompanyProfile.get().getZhName() : "";

    aggregateRoot.setPromotionPlan(promotionPlan);
    aggregateRoot.setEffDate(input.getEffDate());
    aggregateRoot.setDscDate(input.getDscDate());
    aggregateRoot.setGroupSize(input.getGroupSize());
    aggregateRoot.setMainProfileExpr(input.getMainProfileExpr());
    aggregateRoot.setMainProfileName(mainProfileName);
    aggregateRoot.setMainProfileQty(input.getMainProfileQty());
    aggregateRoot.setAccompanyProfileExpr(input.getAccompanyProfileExpr());
    aggregateRoot.setAccompanyProfileName(acccompanyProfileName);
    aggregateRoot.setAccompanyProfileQty(input.getAccompanyProfileQty());
    aggregateRoot.setIsCouponReq(input.getIsCouponReq());
    aggregateRoot.setIsPartialRefund(input.getIsPartialRefund());
  }

  /**
   * 轉為RES payload
   *
   * @param smallGroupPlan
   * @return
   */
  public SmallGroupPlanData toMsgPayload(SmallGroupPlan smallGroupPlan) {
    SmallGroupPlanDataBuilder builder = SmallGroupPlanData.builder();

    builder
        .promotionCode(smallGroupPlan.getPromotionPlan().getCode())
        .effDate(smallGroupPlan.getEffDate())
        .dscDate(smallGroupPlan.getDscDate())
        .groupSize(smallGroupPlan.getGroupSize())
        .mainProfileExpr(smallGroupPlan.getMainProfileExpr())
        .mainProfileQty(String.valueOf(smallGroupPlan.getMainProfileQty()))
    ;

    // 搭配身分別及搭配人數 如果沒有，為 -
    if (StringUtils.isBlank(smallGroupPlan.getAccompanyProfileExpr())) {
      builder.accompanyProfileExpr("-");
      builder.accompanyProfileQty("");
    } else {
      builder.accompanyProfileExpr(smallGroupPlan.getAccompanyProfileExpr());
      builder.accompanyProfileQty(String.valueOf(smallGroupPlan.getAccompanyProfileQty()));
    }

    // 使用優惠券 Y-是，N-否
    builder.isCouponReq(Boolean.TRUE.equals(smallGroupPlan.getIsCouponReq()) ? "Y" : "N");
    // 部分退票 Y-可以，N-不可
    builder.isPartialRefund(Boolean.TRUE.equals(smallGroupPlan.getIsPartialRefund()) ? "Y" : "N");

    return builder.build();
  }
}