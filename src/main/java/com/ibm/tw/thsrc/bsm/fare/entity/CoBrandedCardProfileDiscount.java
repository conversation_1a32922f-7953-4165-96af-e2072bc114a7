/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.entity;

import com.ibm.tw.thsrc.bsm.core.entity.AggregateRoot;
import java.math.BigDecimal;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity
public class CoBrandedCardProfileDiscount extends AggregateRoot {

  @ManyToOne
  @JoinColumn
  private CoBrandedCard coBrandedCard;

  @ManyToOne
  @JoinColumn
  private CoBrandedCardMunicipality municipality;

  @ManyToOne
  @JoinColumn
  private CoBrandedCardProfile profile;

  private BigDecimal discountPct;
}
