/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardBankPromotionOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.ElectronicMoneyType;
import com.ibm.tw.thsrc.bsm.fare.entity.CardIssuingBank_;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardBankPromotion;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardBankPromotion_;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCard_;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardBankPromotionRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.CoBrandedCardBankPromotionFilter;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Transactional
@Service
public class CoBrandedCardBankPromotionQueryService extends
    AbstractQueryService<CoBrandedCardBankPromotion, CoBrandedCardBankPromotionOutput, CoBrandedCardBankPromotionFilter, Void> implements
    CoBrandedCardBankPromotionQueryApi {

  @Autowired
  private ObjectMapper objectMapper;

  @Autowired
  private CoBrandedCardBankPromotionRepository repo;

  @Override
  public CoBrandedCardBankPromotionOutput read(Long id) {
    throw new UnsupportedOperationException();
  }

  public Set<CoBrandedCardBankPromotionOutput> read(Set<Long> ids) {
    List<CoBrandedCardBankPromotion> results = repo.findAllById(ids);
    return results.stream().map(this::translate).collect(Collectors.toSet());
  }

  @Override
  public Page<CoBrandedCardBankPromotionOutput> search(Search searchRequest) {
    PageRequest pr = translateToPageRequest(searchRequest);
    Specification<CoBrandedCardBankPromotion> spec = translateToSpecification(searchRequest);
    Page<CoBrandedCardBankPromotion> page = repo.findAll(spec, pr);
    return page.map(this::translate);
  }

  @Override
  protected CoBrandedCardBankPromotionOutput translate(CoBrandedCardBankPromotion entity) {
    CoBrandedCardBankPromotionOutput response = new CoBrandedCardBankPromotionOutput();
    response.setId(entity.getId());
    response.setDataVersion(entity.getDataVersion());
    response.setFrequency(entity.getFrequency());
    response.setEffSaleDate(entity.getEffSaleDate());
    response.setDscSaleDate(entity.getDscSaleDate());
    response.setDiscountPct(entity.getDiscountPct());

    response.setBankId(entity.getBank().getId());
    response.setBankCode(entity.getBank().getCode());
    response.setBankName(entity.getBank().getName());

    response.setCoBrandedCardId(entity.getCoBrandedCard().getId());
    response.setCoBrandedCardType(entity.getCoBrandedCard().getElectronicMoneyType());

    return response;
  }

  @Override
  protected Sort translate(Void sortBy, Direction sortDirection) {
    return Sort.unsorted();
  }

  @Override
  protected Predicate translate(ComparisonExpression<CoBrandedCardBankPromotionFilter> expression,
      Root<CoBrandedCardBankPromotion> root, CriteriaQuery<?> query,
      CriteriaBuilder criteriaBuilder) {

    ComparisonOperator op = expression.getOperator();

    if (expression.getAttributeValue().getType() != null) {

      Path<ElectronicMoneyType> type = root.join(CoBrandedCardBankPromotion_.CO_BRANDED_CARD).get(
          CoBrandedCard_.ELECTRONIC_MONEY_TYPE);
      ElectronicMoneyType value = expression.getAttributeValue().getType();
      return translate(type, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getName() != null) {

      Path<String> code = root.join(CoBrandedCardBankPromotion_.BANK).get(CardIssuingBank_.NAME);
      String value = expression.getAttributeValue().getName();
      return translate(code, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getDiscountPct() != null) {

      Path<BigDecimal> path = root.get(CoBrandedCardBankPromotion_.DISCOUNT_PCT);
      BigDecimal value = expression.getAttributeValue().getDiscountPct();
      return translate(path, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getFrequency() != null) {

      List<Predicate> predicates = generateFrequencyPredicates(expression, root, criteriaBuilder);
      return criteriaBuilder.or(predicates.toArray(new Predicate[predicates.size()]));

    } else if (expression.getAttributeValue().getStartDate() != null) {

      Path<LocalDate> effDate = root.get(CoBrandedCardBankPromotion_.EFF_SALE_DATE);
      LocalDate value = expression.getAttributeValue().getStartDate();
      return translate(effDate, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getEndDate() != null) {

      Path<LocalDate> dscDate = root.get(CoBrandedCardBankPromotion_.DSC_SALE_DATE);
      LocalDate value = expression.getAttributeValue().getEndDate();
      return translate(dscDate, op, value, criteriaBuilder);

    } else {
      return null;
    }
  }

  private List<Predicate> generateFrequencyPredicates(
      ComparisonExpression<CoBrandedCardBankPromotionFilter> expression,
      Root<CoBrandedCardBankPromotion> root, CriteriaBuilder criteriaBuilder) {

    Path<String> path = root.get(CoBrandedCardBankPromotion_.FREQUENCY);
    String value = expression.getAttributeValue().getFrequency();

    List<Predicate> predicates = new ArrayList<>();

    for (int i = 0; i < value.length(); i++) {
      char weekday = value.charAt(i);
      if (weekday != '-') {
        if (i == 0) {
          predicates.add(
              translate(path, ComparisonOperator.SW, String.valueOf(weekday), criteriaBuilder));
        } else if (i == 1 || i == 3 || i == 5 || (i == 2 && value.charAt(4) != '-')) {
          predicates.add(
              translate(path, ComparisonOperator.CO, String.valueOf(weekday), criteriaBuilder));
        } else if (i == 6) {
          predicates.add(
              translate(path, ComparisonOperator.EW, String.valueOf(weekday), criteriaBuilder));
        } else if (i == 2 && value.charAt(4) == '-') {
          predicates.add(translate(path, ComparisonOperator.SW, "SMT", criteriaBuilder));
          predicates.add(translate(path, ComparisonOperator.SW, "-MT", criteriaBuilder));
          predicates.add(translate(path, ComparisonOperator.SW, "S-T", criteriaBuilder));
          predicates.add(translate(path, ComparisonOperator.SW, "--T", criteriaBuilder));
        } else if (i == 4 && value.charAt(2) == '-') {
          predicates.add(translate(path, ComparisonOperator.EW, "TFS", criteriaBuilder));
          predicates.add(translate(path, ComparisonOperator.EW, "T--", criteriaBuilder));
          predicates.add(translate(path, ComparisonOperator.EW, "TF-", criteriaBuilder));
          predicates.add(translate(path, ComparisonOperator.EW, "T-S", criteriaBuilder));
        }
      }
    }

    return predicates;
  }

  @Override
  protected Function<Map<String, Boolean>, Void> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, Void.class);
  }

  @Override
  protected Function<Map<String, Object>, CoBrandedCardBankPromotionFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, CoBrandedCardBankPromotionFilter.class);
  }

  @Override
  public List<CoBrandedCardBankPromotionOutput> getCoBrandedCardBankPromotion() {

    List<CoBrandedCardBankPromotion> entities = repo.findAll();
    return entities.stream().map(this::translate).collect(Collectors.toList());
  }
}
