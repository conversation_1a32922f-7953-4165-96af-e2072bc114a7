/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.fa.api.TicketEndorsementApi;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketEndorsementInput;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketEndorsementOutput;
import com.ibm.tw.thsrc.bsm.fare.service.TicketEndorsementCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.TicketEndorsementQueryService;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/TicketEndorsements")
public class TicketEndorsementController implements TicketEndorsementApi {

  @Autowired
  private TicketEndorsementCommandService commandService;

  @Autowired
  private TicketEndorsementQueryService queryService;

  @Autowired
  private EventStore eventStore;

  @Override
  public Set<TicketEndorsementOutput> modify(
      CollectionModificationInput<TicketEndorsementInput> changeSet) {
    eventStore.validateFunctionLock(OperationFunction.TICKET_ENDORSEMENT);
    Set<Long> ids = commandService.patch(changeSet);
    ids.addAll(changeSet.getReplacements().keySet());
    return queryService.read(ids);
  }

  @Override
  public Page<TicketEndorsementOutput> search(Search search) {
    return queryService.search(search);
  }
}
