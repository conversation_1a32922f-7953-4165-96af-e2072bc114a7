/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fa.enums.ElectronicMoneyType;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardRoundOffRule;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CoBrandedCardRoundOffRuleRepository extends
    JpaRepository<CoBrandedCardRoundOffRule, Long> {

  Optional<CoBrandedCardRoundOffRule> findByElectronicMoneyType(ElectronicMoneyType cardType);

}
