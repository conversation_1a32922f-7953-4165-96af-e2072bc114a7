/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketEndorsementOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.TicketEndorsement;
import com.ibm.tw.thsrc.bsm.fare.entity.TicketEndorsement_;
import com.ibm.tw.thsrc.bsm.fare.repository.TicketEndorsementRepository;
import com.ibm.tw.thsrc.bsm.fare.translate.TicketEndorsementTranslator;
import com.ibm.tw.thsrc.bsm.fare.vo.TicketEndorsementFilter;
import com.ibm.tw.thsrc.bsm.fare.vo.TicketEndorsementSort;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.TypedSort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class TicketEndorsementQueryService extends
    AbstractQueryService<TicketEndorsement, TicketEndorsementOutput, TicketEndorsementFilter, TicketEndorsementSort>
    implements TicketEndorsementQueryApi {

  @Autowired
  private ObjectMapper objectMapper;

  @Autowired
  private TicketEndorsementRepository repo;

  @Override
  public TicketEndorsementOutput read(Long id) {
    throw new UnsupportedOperationException();
  }

  public Set<TicketEndorsementOutput> read(Set<Long> ids) {
    return repo.findAllById(ids).stream().map(this::translate).collect(Collectors.toSet());
  }

  @Override
  public Page<TicketEndorsementOutput> search(Search searchRequest) {
    PageRequest pr = translateToPageRequest(searchRequest);
    Specification<TicketEndorsement> spec = translateToSpecification(searchRequest);
    Page<TicketEndorsement> page = repo.findAll(spec, pr);

    return page.map(this::translate);

  }

  @Override
  protected TicketEndorsementOutput translate(TicketEndorsement entity) {
    return TicketEndorsementTranslator.toTicketEndorsementOutput(entity);
  }

  @Override
  protected Sort translate(TicketEndorsementSort sortBy, Direction direction) {
    TypedSort<TicketEndorsement> typedSort = Sort.sort(TicketEndorsement.class);

    if (sortBy.isCode()) {
      typedSort//
          .by(TicketEndorsement::getType);
    } else if (sortBy.isEnName()) {
      typedSort//
          .by(TicketEndorsement::getEnName);
    } else if (sortBy.isZhName()) {
      typedSort//
          .by(TicketEndorsement::getZhName);
    }

    if (typedSort.isSorted()) {
      if (Sort.Direction.DESC == direction) {
        return typedSort.descending();
      } else {
        return typedSort.ascending();
      }
    } else {
      return typedSort;
    }
  }

  @Override
  protected Predicate translate(ComparisonExpression<TicketEndorsementFilter> expression,
      Root<TicketEndorsement> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    if (expression.getAttributeValue().getIsFuture() != null) {
      Path<LocalDate> path = root.get(TicketEndorsement_.EFF_DATE);
      boolean isFuture = expression.getAttributeValue().getIsFuture();
      ComparisonOperator op;
      LocalDate value = LocalDate.now();

      if (isFuture) {
        op = ComparisonOperator.GT;

      } else {
        op = ComparisonOperator.EQ;
        Optional<TicketEndorsement> newest = repo.findAll().stream()
            .filter(e -> !e.getEffDate().isAfter(LocalDate.now()))
            .max(Comparator.comparing(TicketEndorsement::getEffDate));
        if (newest.isPresent()) {
          value = newest.get().getEffDate();
        }
      }
      return translate(path, op, value, criteriaBuilder);

    } else {
      return null;
    }
  }

  @Override
  protected Function<Map<String, Boolean>, TicketEndorsementSort> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, TicketEndorsementSort.class);
  }

  @Override
  protected Function<Map<String, Object>, TicketEndorsementFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, TicketEndorsementFilter.class);
  }

  @Override
  public List<TicketEndorsementOutput> getTicketEndorsement(LocalDate effDate) {
    return repo.findByEffDate(effDate).stream().map(this::translate).collect(Collectors.toList());
  }
}
