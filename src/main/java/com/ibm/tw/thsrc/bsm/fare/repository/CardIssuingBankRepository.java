/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fare.entity.CardIssuingBank;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCard;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface CardIssuingBankRepository extends JpaRepository<CardIssuingBank, Long>,
    JpaSpecificationExecutor<CardIssuingBank> {

  List<CardIssuingBank> findByCodeAndCoBrandedCard(String code, CoBrandedCard coBrandedCard);

  List<CardIssuingBank> findByNameAndCoBrandedCard_Id(String name, Long cardId);
}
