/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.fa.api.CoBrandedCardOperatingParameterApi;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardOperatingParameterInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardOperatingParameterOutput;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardOperatingParameterCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardOperatingParameterQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/CoBrandedCardOperatingParameters")
public class CoBrandedCardOperatingParameterController implements
    CoBrandedCardOperatingParameterApi {

  @Autowired
  private CoBrandedCardOperatingParameterCommandService commandService;

  @Autowired
  private CoBrandedCardOperatingParameterQueryService queryService;

  @Autowired
  private EventStore eventStore;

  @Override
  public Page<CoBrandedCardOperatingParameterOutput> search(Search search) {
    return queryService.search(search);
  }

  @Override
  public CoBrandedCardOperatingParameterOutput replace(
      CoBrandedCardOperatingParameterInput changeInput) {
    eventStore.validateFunctionLock(OperationFunction.CO_BRANDED_CARD_OPERATING_PARAMETER);
    Long id = commandService.replace(changeInput);
    return queryService.read(id);
  }
}
