/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.fa.api.CardIssuingBankApi;
import com.ibm.tw.thsrc.bsm.fa.dto.CardIssuingBankInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CardIssuingBankOutput;
import com.ibm.tw.thsrc.bsm.fare.service.CardIssuingBankCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.CardIssuingBankQueryService;
import java.util.Set;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/CardIssuingBanks")
public class CardIssuingBankController implements CardIssuingBankApi {

  @Autowired
  private CardIssuingBankCommandService commandService;

  @Autowired
  private CardIssuingBankQueryService queryService;

  @Autowired
  private EventStore eventStore;

  @Override
  public Set<CardIssuingBankOutput> modify(
      CollectionModificationInput<CardIssuingBankInput> changeSet) {
    eventStore.validateFunctionLock(OperationFunction.CARD_ISSUING_BANK);
    Set<Long> ids = commandService.patch(changeSet);
    return queryService.read(ids);
  }

  @Override
  public Page<CardIssuingBankOutput> search(Search search) {
    return queryService.search(search);
  }

  @Override
  public CardIssuingBankOutput create(CardIssuingBankInput input) {
    eventStore.validateFunctionLock(OperationFunction.CARD_ISSUING_BANK);
    Long id = commandService.create(input);
    return queryService.read(id);
  }

  @Override
  public void delete(Long id) {
    eventStore.validateFunctionLock(OperationFunction.CARD_ISSUING_BANK);
    commandService.delete(id);
  }
}
