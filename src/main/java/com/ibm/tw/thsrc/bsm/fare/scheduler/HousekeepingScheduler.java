package com.ibm.tw.thsrc.bsm.fare.scheduler;


import com.ibm.tw.thsrc.aors.logger.AorsAbstractLogger;
import com.ibm.tw.thsrc.aors.logger.AorsLoggerFactory;
import com.ibm.tw.thsrc.aors.logger.AorsLoggerType;
import com.ibm.tw.thsrc.bsm.fare.service.CampaignCreditCardBatchCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.EligibleTrainCommandService;
import java.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class HousekeepingScheduler {

  private static final AorsAbstractLogger LOGGER_BASIC =
      (AorsAbstractLogger) AorsLoggerFactory.getLogger(AorsLoggerType.BASIC);
  @Autowired
  CampaignCreditCardBatchCommandService campaignCreditCardBatchCommandService;
  @Autowired
  EligibleTrainCommandService eligibleTrainCommandService;
  @Value("${housekeeping.expiry.days}")
  private Integer days;

  @Scheduled(cron = "${housekeeping.scheduler.cron}", zone = "${housekeeping.scheduler.zone}")
  public void clean() {

    LOGGER_BASIC.description("housekeeping starts").info();
    LocalDate expiryDate = LocalDate.now().minusDays(days);
    campaignCreditCardBatchCommandService.clean(expiryDate);
    eligibleTrainCommandService.clean(expiryDate);
    LOGGER_BASIC.description("housekeeping ends").info();
  }

}
