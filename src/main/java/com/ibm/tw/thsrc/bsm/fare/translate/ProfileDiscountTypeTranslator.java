/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.translate;

import com.ibm.tw.thsrc.bsm.fa.dto.ProfileDiscountTypeOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountType;

public class ProfileDiscountTypeTranslator {

  private ProfileDiscountTypeTranslator() {
    throw new IllegalStateException("ProfileDiscountTypeTranslator class");
  }

  public static ProfileDiscountTypeOutput toProfileDiscountTypeOutput(
      ProfileDiscountType aggregateRoot) {

    ProfileDiscountTypeOutput output = new ProfileDiscountTypeOutput();
    output.setId(aggregateRoot.getId());
    output.setCode(aggregateRoot.getCode());
    output.setName(aggregateRoot.getName());
    output.setDataVersion(aggregateRoot.getDataVersion());
    return output;
  }
}
