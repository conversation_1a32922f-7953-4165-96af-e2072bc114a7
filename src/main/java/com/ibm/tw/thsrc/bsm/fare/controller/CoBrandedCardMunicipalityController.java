/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.api.CoBrandedCardMunicipalityApi;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardMunicipalityOutput;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardMunicipalityQueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/CoBrandedCardMunicipalities")
public class CoBrandedCardMunicipalityController implements CoBrandedCardMunicipalityApi {

  @Autowired
  private CoBrandedCardMunicipalityQueryService queryService;


  @Override
  public Page<CoBrandedCardMunicipalityOutput> search(Search search) {
    return queryService.search(search);
  }
}
