/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.ProfileDiscountTypeOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountType;
import com.ibm.tw.thsrc.bsm.fare.repository.ProfileDiscountTypeRepository;
import com.ibm.tw.thsrc.bsm.fare.translate.ProfileDiscountTypeTranslator;
import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
public class ProfileDiscountTypeQueryService extends
    AbstractQueryService<ProfileDiscountType, ProfileDiscountTypeOutput, Void, Void> {

  @Autowired
  private ProfileDiscountTypeRepository profileDiscountTypeRepository;

  @Override
  protected ProfileDiscountTypeOutput translate(ProfileDiscountType aggregateRoot) {
    return ProfileDiscountTypeTranslator.toProfileDiscountTypeOutput(aggregateRoot);
  }

  @Override
  protected Sort translate(Void sortBy, Direction sortDirection) {
    return Sort.unsorted();
  }

  @Override
  protected Predicate translate(ComparisonExpression<Void> f, Root<ProfileDiscountType> root,
      CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    return null;
  }

  @Override
  protected Function<Map<String, Boolean>, Void> getSortByTranslator() {
    return data -> null;
  }

  @Override
  protected Function<Map<String, Object>, Void> getFilterAttributeValueTranslator() {
    return data -> null;
  }

  @Override
  public ProfileDiscountTypeOutput read(Long id) {
    return profileDiscountTypeRepository
        .findById(id)
        .map(this::translate)
        .orElseThrow(ResourceNotFoundException::new);
  }

  @Override
  public Page<ProfileDiscountTypeOutput> search(Search search) {
    PageRequest pr = translateToPageRequest(search);
    Specification<ProfileDiscountType> spec = translateToSpecification(search);
    return profileDiscountTypeRepository.findAll(spec, pr).map(this::translate);
  }

  public Set<ProfileDiscountTypeOutput> read(Collection<Long> ids) {
    return profileDiscountTypeRepository
        .findAllById(ids)
        .stream()
        .map(this::translate)
        .collect(Collectors.toSet());
  }
}
