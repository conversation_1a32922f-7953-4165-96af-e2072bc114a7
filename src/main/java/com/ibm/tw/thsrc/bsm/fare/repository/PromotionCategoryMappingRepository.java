/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fare.entity.PromotionCategory;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionCategoryMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan;
import java.time.LocalDate;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface PromotionCategoryMappingRepository extends
    JpaRepository<PromotionCategoryMapping, Long>,
    JpaSpecificationExecutor<PromotionCategoryMapping> {

  public List<PromotionCategoryMapping> findByCategory(PromotionCategory category);

  public List<PromotionCategoryMapping> findByEffDate(LocalDate effDate);

  public List<PromotionCategoryMapping> findByEffDateAndPromotionPlan(
      LocalDate effDate, PromotionPlan promotionPlan);

  public List<PromotionCategoryMapping> findByPromotionPlan(PromotionPlan promotionPlan);
}
