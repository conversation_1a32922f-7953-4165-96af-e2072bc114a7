/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.entity.BaseEntity;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.cronjob.enums.FutureVersionControlStatus;
import com.ibm.tw.thsrc.bsm.cronjob.service.impl.FutureVersionControlService;
import com.ibm.tw.thsrc.bsm.exception.ConflictException;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.RefundFeeRuleInput;
import com.ibm.tw.thsrc.bsm.fa.dto.RegularTicketRuleInput;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketProductPolicyInput;
import com.ibm.tw.thsrc.bsm.fa.enums.RefundFeeCategory;
import com.ibm.tw.thsrc.bsm.fare.domain.event.RegularTicketRuleCronReplaced;
import com.ibm.tw.thsrc.bsm.fare.domain.event.RegularTicketRuleReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.RefundFeeRule;
import com.ibm.tw.thsrc.bsm.fare.entity.RefundFeeRuleLevel;
import com.ibm.tw.thsrc.bsm.fare.entity.RefundFeeRuleLevelTemp;
import com.ibm.tw.thsrc.bsm.fare.entity.RegularTicketRuleFutureVersionControl;
import com.ibm.tw.thsrc.bsm.fare.entity.RegularTicketRuleTemp;
import com.ibm.tw.thsrc.bsm.fare.entity.TicketProductPolicy;
import com.ibm.tw.thsrc.bsm.fare.repository.RefundFeeRuleLevelRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.RefundFeeRuleRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.RegularTicketRuleFutureVersionControlRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.RegularTicketRuleTempRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.TicketProductPolicyRepository;
import com.ibm.tw.thsrc.bsm.message.fare.RefundFeeRuleLevelDetail;
import com.ibm.tw.thsrc.bsm.message.fare.RegularTicketRuleData;
import com.ibm.tw.thsrc.bsm.message.fare.TicketProductPolicyDetail;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Transactional
@Service
public class RegularTicketRuleCommandService extends
    AbstractCommandService<TicketProductPolicy, RegularTicketRuleInput> {

  public static final String JOB_BSM_REGULAR_TICKET_RULE = "regularTicketRuleJob";


  private static final LocalTime CRON_JOB_EXEC_TIME = LocalTime.of(23, 59);

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private RefundFeeRuleRepository ruleRepo;

  @Autowired
  private RefundFeeRuleLevelRepository ruleLevelRepo;

  @Autowired
  private TicketProductPolicyRepository policyRepo;

  @Autowired
  private RegularTicketRuleFutureVersionControlRepository futureVersionRepo;
  @Autowired
  private RegularTicketRuleTempRepository tempRepo;

  @Autowired
  private EventStore eventStore;

  @Autowired
  private FutureVersionControlService<RegularTicketRuleFutureVersionControl> futureVersionControlService;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Override
  public Long create(RegularTicketRuleInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void update(Long id, RegularTicketRuleInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void delete(Long id) {
    throw new UnsupportedOperationException();
  }

  public Pair<List<Long>, Set<Long>> upsert(RegularTicketRuleInput input) {

    // upsert policy
    TicketProductPolicyInput policyInput = input.getTicketProductPolicy();
    TicketProductPolicy policyEntity = policyInput.getId() == null ? new TicketProductPolicy()
        : policyRepo.findById(policyInput.getId()).orElseThrow(ResourceNotFoundException::new);

    // 確認是否有排程正在跑
    validateFutureVersionControlStepRunning(policyEntity);

    if (policyEntity.getDataVersion() != null) {
      this.validateConcurrency(policyEntity, policyInput.getDataVersion());
    }
    writeAggregateRootValue(policyEntity, policyInput);
    this.validateInvariants(policyEntity);
    TicketProductPolicy changedPolicy = policyRepo.save(policyEntity);

    // upsert rules
    List<RefundFeeRuleInput> ruleInputs = input.getRefundFeeRules();
    List<RefundFeeRuleLevel> changedRuleLevels = new ArrayList<>();
    ruleInputs.forEach(ruleInput -> {

      RefundFeeRule ruleEntity = ruleRepo.findById(ruleInput.getId())
          .orElseThrow(ResourceNotFoundException::new);
      validateConcurrency(ruleEntity, ruleInput.getDataVersion());
      List<RefundFeeRuleLevel> diffLevels = writeAggregateRootValue(ruleEntity, ruleInput);
      validateInvariants(diffLevels);
      ruleLevelRepo.saveAll(diffLevels);
      ruleRepo.save(ruleEntity);

      changedRuleLevels.addAll(diffLevels);
    });

    List<Long> upsertId = Collections.singletonList(changedPolicy.getId());
    Set<Long> upsertIds =
        changedRuleLevels.stream().map(BaseEntity::getId).collect(Collectors.toSet());

    // 現在版->下 RES & Meig / 未來版->只會下MEIG
    RegularTicketRuleData msgPayload = translateMsg(changedPolicy);
    translateMsg(msgPayload, changedRuleLevels);
    RegularTicketRuleReplaced event = new RegularTicketRuleReplaced(
        interceptor.getUserId(),
        interceptor.getCorrelationId());
    event.setMsgPayload(msgPayload);
    event.setOperationId(OperationFunction.REGULAR_TICKET.name());
    event.setAssociationId(interceptor.getCorrelationId());
    eventStore.publishEvent(event);

    // 未來版->設排程
    if (policyEntity.getEffDate().isAfter(LocalDate.now())) {
      upsertFutureVersionControl(policyEntity, changedRuleLevels);

    }

    return Pair.of(upsertId, upsertIds);
  }

  protected void validateConcurrency(RefundFeeRule aggregateRoot, Long inputVersion) {
    if (!aggregateRoot.getDataVersion().equals(inputVersion)) {
      throw new ConflictException();
    }

    // force JPA to update data-version of aggregate-root even no property changed
    aggregateRoot.setUpdateTimestamp(ZonedDateTime.now());
  }

  @Override
  protected void writeAggregateRootValue(TicketProductPolicy entity,
      RegularTicketRuleInput input) {
    // no use now
  }

  protected void writeAggregateRootValue(TicketProductPolicy entity,
      TicketProductPolicyInput input) {

    entity.setEffDate(input.getEffDate());
    entity.setIsPlatFormTktActivated(input.getIsPlatFormTktActivated());
    entity.setPlatformTktUnitPrice(input.getPlatformTktUnitPrice());
    entity.setRoundTripTktOutboundValidDays(input.getRoundTripTktOutboundValidDays());
    entity.setRoundTripTktInboundValidDays(input.getRoundTripTktInboundValidDays());
    entity.setOnewayTktRefundFeeAmt(input.getOnewayTktRefundFeeAmt());
    entity.setOnewayTktValidDays(input.getOnewayTktValidDays());
    entity.setReservedSeatTktValidDays(input.getReservedSeatTktValidDays());
    entity.setCscCardMarketingFeeAmt(input.getCscCardMarketingFeeAmt());
    entity.setCscCardDepositAmt(input.getCscCardDepositAmt());
  }

  protected List<RefundFeeRuleLevel> writeAggregateRootValue(RefundFeeRule entity,
      RefundFeeRuleInput input) {

    entity.setCategory(input.getCategory());
    entity.setType(input.getType());
    List<RefundFeeRuleLevel> changedLevels = new ArrayList<>();
    List<RefundFeeRuleLevel> newLevels = new ArrayList<>();

    // for update existing version
    if (input.getRuleLevels().get(0).getId() != null) {

      LocalDate inputEffDate = input.getRuleLevels().get(0).getEffDate();
      boolean isFuture = inputEffDate.compareTo(LocalDate.now()) > 0;
      if (isFuture) {
        entity.getRuleLevels().forEach(levelEntity -> {
          if (levelEntity.getEffDate().compareTo(LocalDate.now()) <= 0) {
            newLevels.add(levelEntity);
          }
        });

      } else {
        entity.getRuleLevels().forEach(levelEntity -> {
          if (levelEntity.getEffDate().compareTo(inputEffDate) != 0) {
            newLevels.add(levelEntity);
          }
        });
      }

      input.getRuleLevels().forEach(levelInput -> {
        RefundFeeRuleLevel ruleLevel = new RefundFeeRuleLevel();
        ruleLevel.setRefundFeeRule(entity);
        ruleLevel.setEffDate(levelInput.getEffDate());
        ruleLevel.setFeePct(levelInput.getFeePct());
        ruleLevel.setFeeAmt(levelInput.getFeeAmt());
        ruleLevel.setMinPreDepDays(levelInput.getMinPreDepDays());
        ruleLevel.setMaxPreDepDays(levelInput.getMaxPreDepDays());
        newLevels.add(ruleLevel);
        changedLevels.add(ruleLevel);
      });
      entity.getRuleLevels().clear();

      // for create new version
    } else {
      input.getRuleLevels().forEach(levelInput -> {
        RefundFeeRuleLevel ruleLevel = new RefundFeeRuleLevel();
        ruleLevel.setRefundFeeRule(entity);
        ruleLevel.setEffDate(levelInput.getEffDate());
        ruleLevel.setFeePct(levelInput.getFeePct());
        ruleLevel.setFeeAmt(levelInput.getFeeAmt());
        ruleLevel.setMinPreDepDays(levelInput.getMinPreDepDays());
        ruleLevel.setMaxPreDepDays(levelInput.getMaxPreDepDays());

        newLevels.add(ruleLevel);
        changedLevels.add(ruleLevel);
      });
    }

    entity.getRuleLevels().addAll(newLevels);

    return changedLevels;
  }

  @Override
  protected void validateInvariants(TicketProductPolicy entity)
      throws UnprocessableException {
    // not needed for now
  }

  protected void validateInvariants(List<RefundFeeRuleLevel> levelEntities)
      throws UnprocessableException {
    final int[] lastMaxDay = {0};

    levelEntities.forEach(level -> {
      if (lastMaxDay[0] == 0 && level.getMinPreDepDays() == 0) {
        if (level.getMaxPreDepDays() < level.getMinPreDepDays()) {
          throw FareErrorCode.FA_REFUND_FEE_RULE_LEVELS_OVERLAP.toException();
        }

      } else if (level.getMinPreDepDays() <= lastMaxDay[0]) {
        throw FareErrorCode.FA_REFUND_FEE_RULE_LEVELS_OVERLAP.toException();

      } else if (level.getMinPreDepDays() != lastMaxDay[0] + 1) {
        throw FareErrorCode.FA_REFUND_FEE_RULE_LEVELS_ARE_NOT_CONTINUOUS.toException();
      }

      lastMaxDay[0] = level.getMaxPreDepDays();
    });
  }

  private RegularTicketRuleData translateMsg(TicketProductPolicy policyEntity) {
    RegularTicketRuleData msg = new RegularTicketRuleData();
    msg.setEffDate(policyEntity.getEffDate());
    TicketProductPolicyDetail policyDetail = new TicketProductPolicyDetail();
    policyDetail.setIsPlatFormTktActivated(policyEntity.getIsPlatFormTktActivated());
    policyDetail.setPlatformTktUnitPrice(policyEntity.getPlatformTktUnitPrice());
    policyDetail.setRoundTripTktOutboundValidDays(policyEntity.getRoundTripTktOutboundValidDays());
    policyDetail.setRoundTripTktInboundValidDays(policyEntity.getRoundTripTktInboundValidDays());
    policyDetail.setOnewayTktRefundFeeAmt(policyEntity.getOnewayTktRefundFeeAmt());
    policyDetail.setOnewayTktValidDays(policyEntity.getOnewayTktValidDays());
    policyDetail.setReservedSeatTktValidDays(policyEntity.getReservedSeatTktValidDays());
    policyDetail.setCscCardMarketingFeeAmt(policyEntity.getCscCardMarketingFeeAmt());
    policyDetail.setCscCardDepositAmt(policyEntity.getCscCardDepositAmt());

    msg.setTicketProductPolicy(policyDetail);
    return msg;
  }

  private void translateMsg(RegularTicketRuleData msg, List<RefundFeeRuleLevel> levelEntities) {
    levelEntities.forEach(level -> {
      RefundFeeRuleLevelDetail ruleDetail = new RefundFeeRuleLevelDetail();
      ruleDetail.setFeePct(level.getFeePct());
      ruleDetail.setFeeAmt(level.getFeeAmt());
      ruleDetail.setMinPreDepDays(level.getMinPreDepDays());
      ruleDetail.setMaxPreDepDays(level.getMaxPreDepDays());

      if (level.getRefundFeeRule().getCategory()
          .equals(RefundFeeCategory.RESERVED_SEAT_INDIVIDUAL)) {
        msg.getRegularRule().add(ruleDetail);
      } else if (level.getRefundFeeRule().getCategory()
          .equals(RefundFeeCategory.RESERVED_SEAT_GROUP)) {
        msg.getGroupRule().add(ruleDetail);
      }
    });
  }

  private void translateCronMsg(RegularTicketRuleData msg,
      List<RefundFeeRuleLevelTemp> levelEntities) {
    levelEntities.forEach(level -> {
      RefundFeeRuleLevelDetail ruleDetail = new RefundFeeRuleLevelDetail();
      ruleDetail.setFeePct(level.getFeePct());
      ruleDetail.setFeeAmt(level.getFeeAmt());
      ruleDetail.setMinPreDepDays(level.getMinPreDepDays());
      ruleDetail.setMaxPreDepDays(level.getMaxPreDepDays());

      if (level.getRefundFeeRule().getCategory()
          .equals(RefundFeeCategory.RESERVED_SEAT_INDIVIDUAL)) {
        msg.getRegularRule().add(ruleDetail);
      } else if (level.getRefundFeeRule().getCategory()
          .equals(RefundFeeCategory.RESERVED_SEAT_GROUP)) {
        msg.getGroupRule().add(ruleDetail);
      }
    });
  }

  public void cronJob(Long id) {

    RegularTicketRuleTemp tempTable = tempRepo.findById(id)
        .orElseThrow(ResourceNotFoundException::new);

    RegularTicketRuleData msgPayload = translateMsg(tempTable.getTicketProductPolicy());
    translateCronMsg(msgPayload, tempTable.getRefundFeeRuleLevels());
    RegularTicketRuleCronReplaced event = new RegularTicketRuleCronReplaced(
        tempTable.getUserId(),
        tempTable.getCorrelationId());
    event.setMsgPayload(msgPayload);
    applicationEventPublisher.publishEvent(event);
  }

  private void upsertFutureVersionControl(TicketProductPolicy entity,
      List<RefundFeeRuleLevel> levelEntities) {

    // 未來版 delete
    List<RegularTicketRuleTemp> tempTables = tempRepo.findByTicketProductPolicy(entity);

    if (!tempTables.isEmpty()) {
      List<RegularTicketRuleFutureVersionControl> activeFutureVersionControls =
          futureVersionRepo.findByRegularTicketRuleTemp_IdAndStatusInAndDeleted(
              tempTables.get(0).getId(), Arrays.asList(FutureVersionControlStatus.ACCEPTED,
                  FutureVersionControlStatus.SCHEDULED), false);
      if (!activeFutureVersionControls.isEmpty()) {
        futureVersionControlService.deleteFutureVersionControls(activeFutureVersionControls);
      }

      tempRepo.deleteAll(tempTables);
    }

    // 未來版 create
    RegularTicketRuleTemp newTempTable = new RegularTicketRuleTemp();
    newTempTable.setTicketProductPolicy(entity);
    newTempTable.setUserId(interceptor.getUserId());
    newTempTable.setCorrelationId(interceptor.getCorrelationId());

    List<RefundFeeRuleLevelTemp> tempLevels = levelEntities.stream().map(e -> {
      RefundFeeRuleLevelTemp tempLevel = new RefundFeeRuleLevelTemp();
      tempLevel.setRegularTicketRuleTemp(newTempTable);
      tempLevel.setRefundFeeRule(e.getRefundFeeRule());
      tempLevel.setEffDate(e.getEffDate());
      tempLevel.setFeePct(e.getFeePct());
      tempLevel.setFeeAmt(e.getFeeAmt());
      tempLevel.setMinPreDepDays(e.getMinPreDepDays());
      tempLevel.setMaxPreDepDays(e.getMaxPreDepDays());
      return tempLevel;
    }).collect(Collectors.toList());
    newTempTable.getRefundFeeRuleLevels().addAll(tempLevels);
    tempRepo.save(newTempTable);

    RegularTicketRuleFutureVersionControl futureVersionControl =
        futureVersionControlService.init(
            entity.getEffDate().minusDays(1).atTime(CRON_JOB_EXEC_TIME),
            RegularTicketRuleFutureVersionControl.class);

    futureVersionControl.setRegularTicketRuleTemp(newTempTable);
    futureVersionControl = futureVersionRepo.save(futureVersionControl);
    futureVersionControlService.scheduleFutureVersionControl(futureVersionControl,
        JOB_BSM_REGULAR_TICKET_RULE, interceptor.getCorrelationId());
  }

  private void validateFutureVersionControlStepRunning(TicketProductPolicy entity) {
    if (Objects.nonNull(entity.getId())) {
      List<RegularTicketRuleTemp> tempTables = tempRepo.findByTicketProductPolicy(entity);

      if (!tempTables.isEmpty()) {
        List<RegularTicketRuleFutureVersionControl> result =
            futureVersionRepo.findByRegularTicketRuleTemp_IdAndStatus(tempTables.get(0).getId(),
                FutureVersionControlStatus.STARTED);
        if (!result.isEmpty()) {
          throw FareErrorCode.FA_REGULAR_TICKET_RULE_STEP_IS_RUNNING.toException();
        }
      }
    }

  }
}