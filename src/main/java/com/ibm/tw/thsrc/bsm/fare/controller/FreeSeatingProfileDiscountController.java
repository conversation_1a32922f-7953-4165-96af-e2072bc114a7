/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source
 * code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.api.FreeSeatingProfileDiscountApi;
import com.ibm.tw.thsrc.bsm.fa.dto.FreeSeatingProfileDiscountInput;
import com.ibm.tw.thsrc.bsm.fa.dto.FreeSeatingProfileDiscountOutput;
import com.ibm.tw.thsrc.bsm.fare.service.FreeSeatingProfileDiscountCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.FreeSeatingProfileDiscountQueryService;

@RestController
@RequestMapping("/FreeSeatingProfileDiscounts")
public class FreeSeatingProfileDiscountController implements FreeSeatingProfileDiscountApi {

  @Autowired
  private FreeSeatingProfileDiscountCommandService commandService;
  @Autowired
  private FreeSeatingProfileDiscountQueryService queryService;

  @Autowired
  private EventStore eventStore;

  @Override
  public void replace(FreeSeatingProfileDiscountInput input) {
    eventStore.validateFunctionLock(OperationFunction.FREE_SEATING_PROFILE_DISCOUNT);
    commandService.replace(input);
  }

  @Override
  public Page<FreeSeatingProfileDiscountOutput> search(Search search) {
    return queryService.search(search);
  }
}
