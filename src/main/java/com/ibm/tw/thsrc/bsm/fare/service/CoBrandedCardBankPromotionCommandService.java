/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.service.AbstractCommandService;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.exception.SharedErrorCode;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardBankPromotionInput;
import com.ibm.tw.thsrc.bsm.fare.domain.event.BatchCoBrandedCardBankPromotionReplaced;
import com.ibm.tw.thsrc.bsm.fare.entity.CardIssuingBank;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCard;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardBankPromotion;
import com.ibm.tw.thsrc.bsm.fare.repository.CardIssuingBankRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardBankPromotionRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardRepository;
import com.ibm.tw.thsrc.bsm.message.fare.CoBrandedCardBankPromotionData;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

@Transactional
@Service
public class CoBrandedCardBankPromotionCommandService extends
    AbstractCommandService<CoBrandedCardBankPromotion, CoBrandedCardBankPromotionInput> {

  @Autowired
  private HttpHeaderInterceptor interceptor;

  @Autowired
  private CoBrandedCardBankPromotionRepository repo;

  @Autowired
  private CoBrandedCardRepository coBrandedCardRepo;

  @Autowired
  private CardIssuingBankRepository bankRepo;

  @Autowired
  private ApplicationEventPublisher applicationEventPublisher;

  @Override
  public Long create(CoBrandedCardBankPromotionInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void update(Long id, CoBrandedCardBankPromotionInput input) {
    throw new UnsupportedOperationException();
  }

  @Override
  public void delete(Long id) {
    throw new UnsupportedOperationException();
  }

  public Set<Long> patch(CollectionModificationInput<CoBrandedCardBankPromotionInput> changeSet) {

    List<CoBrandedCardBankPromotionData> msgList = new ArrayList<>();

    changeSet.getDeletions().forEach(e -> {
      CoBrandedCardBankPromotion entity = repo.findById(e)
          .orElseThrow(() ->
              SharedErrorCode.COLLECTION_PATCH_INPUT_ID_NOT_EXIST.toException(String.valueOf(e)));
      repo.delete(entity);
    });

    changeSet.getReplacements().forEach((k, v) -> {
      CoBrandedCardBankPromotion entity = repo.findById(k)
          .orElseThrow(() ->
              SharedErrorCode.COLLECTION_PATCH_INPUT_ID_NOT_EXIST.toException(String.valueOf(k)));
      validateConcurrency(entity, v.getDataVersion());
      writeAggregateRootValue(entity, v);
      validateInvariants(entity);
      repo.save(entity);

      msgList.add(translatePubMsg(entity));
    });

    Set<Long> ids = changeSet.getCreations().stream().map(input -> {
      CoBrandedCardBankPromotion newEntity = new CoBrandedCardBankPromotion();
      writeAggregateRootValue(newEntity, input);
      validateInvariants(newEntity);
      CoBrandedCardBankPromotion savedEntity = repo.save(newEntity);

      msgList.add(translatePubMsg(savedEntity));
      return savedEntity.getId();

    }).collect(Collectors.toSet());

    publishReplacedEvent(msgList);

    return ids;
  }

  @Override
  protected void validateInvariants(CoBrandedCardBankPromotion entity)
      throws UnprocessableException {
    List<CoBrandedCardBankPromotion> sameBankPromotions = repo.findByBankAndCoBrandedCard(
        entity.getBank(), entity.getCoBrandedCard());

    if (Objects.isNull(entity.getId())) {
      if (!sameBankPromotions.isEmpty()) {
        sameBankPromotions.forEach(e -> {
          if (isDateRangeOverlap(entity, e)) {
            throw FareErrorCode.FA_CO_BRANDED_CARD_BANK_PROMOTION_SALE_DATE_RANGE_CANNOT_OVERLAP_WITH_SAME_BANK_AND_CO_BRANDED_CARD.toException(
                entity.getBank().getName());
          }
        });
      }
    } else {
      if (sameBankPromotions.size() > 1) {
        sameBankPromotions.forEach(e -> {
          if ((!e.getId().equals(entity.getId())) && isDateRangeOverlap(entity, e)) {
            throw FareErrorCode.FA_CO_BRANDED_CARD_BANK_PROMOTION_SALE_DATE_RANGE_CANNOT_OVERLAP_WITH_SAME_BANK_AND_CO_BRANDED_CARD.toException(
                entity.getBank().getName());
          }
        });
      }
    }
  }

  @Override
  protected void writeAggregateRootValue(CoBrandedCardBankPromotion entity,
      CoBrandedCardBankPromotionInput input) {
    CoBrandedCard card = coBrandedCardRepo.findById(input.getCoBrandedCardId()).orElseThrow(
        FareErrorCode.FA_CO_BRANDED_CARD_BANK_PROMOTION_CANNOT_ASSOCIATE_TO_THE_CO_BRANDED_CARD::toException);
    CardIssuingBank bank = bankRepo.findById(input.getBankId()).orElseThrow(
        FareErrorCode.FA_CO_BRANDED_CARD_BANK_PROMOTION_CANNOT_ASSOCIATE_TO_THE_CARD_ISSUING_BANK::toException);
    entity.setCoBrandedCard(card);
    entity.setBank(bank);
    entity.setEffSaleDate(input.getEffSaleDate());
    entity.setDscSaleDate(input.getDscSaleDate());
    entity.setDiscountPct(input.getDiscountPct());
    entity.setFrequency(input.getFrequency());
  }

  private boolean isDateRangeOverlap(CoBrandedCardBankPromotion entity,
      CoBrandedCardBankPromotion refEntity) {
    Pair<LocalDate, LocalDate> target = Pair.of(entity.getEffSaleDate(), entity.getDscSaleDate());
    Pair<LocalDate, LocalDate> reference = Pair.of(refEntity.getEffSaleDate(),
        refEntity.getDscSaleDate());

    boolean isOverlap = false;

    // 起日<=參照起日 & (迄日>=參照起日 or 參照起日<=迄日<=參照迄日)
    if ((!target.getLeft().isAfter(reference.getLeft()) && (
        !target.getRight().isBefore(reference.getLeft()) || (
            !target.getRight().isBefore(reference.getLeft())
                && !target.getRight().isAfter(reference.getRight())))) ||
        // 迄日>=參照迄日 & (起日<=參照迄日 or 參照迄日>=起日>=參照起日)
        (!target.getRight().isBefore(reference.getRight()) && (
            !target.getLeft().isAfter(reference.getRight()) || (
                !target.getLeft().isAfter(reference.getRight())
                    && !target.getLeft().isBefore(reference.getLeft()))))) {

      isOverlap = true;
    }

    return isOverlap;
  }

  private CoBrandedCardBankPromotionData translatePubMsg(CoBrandedCardBankPromotion entity) {
    CoBrandedCardBankPromotionData msg = new CoBrandedCardBankPromotionData();

    msg.setFrequency(entity.getFrequency());
    msg.setDiscountPct(entity.getDiscountPct());
    msg.setEffSaleDate(entity.getEffSaleDate());
    msg.setDscSaleDate(entity.getDscSaleDate());
    msg.setBankCode(entity.getBank().getCode());
    msg.setBankName(entity.getBank().getName());
    msg.setElectronicMoneyType(entity.getCoBrandedCard().getElectronicMoneyType().toString());

    return msg;
  }

  private void publishReplacedEvent(List<CoBrandedCardBankPromotionData> msgPayloads) {
    BatchCoBrandedCardBankPromotionReplaced replaced = new BatchCoBrandedCardBankPromotionReplaced(
        interceptor.getUserId(), interceptor.getCorrelationId());
    replaced.setMsgPayload(msgPayloads);
    applicationEventPublisher.publishEvent(replaced);
  }
}
