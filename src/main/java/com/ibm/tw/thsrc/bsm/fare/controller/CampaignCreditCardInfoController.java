/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.api.CampaignCreditCardInfoApi;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardInfoOutput;
import com.ibm.tw.thsrc.bsm.fare.service.CampaignCreditCardInfoQueryService;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/CampaignCreditCardInfo")
public class CampaignCreditCardInfoController implements CampaignCreditCardInfoApi {

  @Autowired
  private CampaignCreditCardInfoQueryService queryService;

  @Override
  public Page<CampaignCreditCardInfoOutput> search(@Valid Search search) {
    return queryService.search(search);
  }

}
