/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fare.entity.PassengerProfile;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface PassengerProfileRepository
    extends JpaRepository<PassengerProfile, Long>, JpaSpecificationExecutor<PassengerProfile> {

  List<PassengerProfile> findByIdNotIn(List<Long> excludeIds);

  Optional<PassengerProfile> findByCode(String code);
}
