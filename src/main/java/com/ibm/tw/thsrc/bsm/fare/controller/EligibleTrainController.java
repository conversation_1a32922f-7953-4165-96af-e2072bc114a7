/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.audit.OperationFunction;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationOutput;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.fa.api.EligibleTrainApi;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainInput;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainOutput;
import com.ibm.tw.thsrc.bsm.fare.service.EligibleTrainBulkService;
import com.ibm.tw.thsrc.bsm.fare.service.EligibleTrainCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.EligibleTrainQueryService;
import java.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/EligibleTrains")
public class EligibleTrainController implements EligibleTrainApi {

  @Autowired
  private EligibleTrainCommandService commandService;

  @Autowired
  private EligibleTrainBulkService bulkService;

  @Autowired
  private EligibleTrainQueryService queryService;

  @Autowired
  private EventStore eventStore;

  @Override
  public Page<EligibleTrainOutput> search(Search search) {
    return queryService.search(search);
  }

  @Override
  public void delete(Long id) {
    eventStore.validateFunctionLock(OperationFunction.ELIGIBLE_TRAIN);
    commandService.delete(id);
  }

  @Override
  public EligibleTrainOutput replace(Long id, EligibleTrainInput input) {
    eventStore.validateFunctionLock(OperationFunction.ELIGIBLE_TRAIN);
    commandService.update(id, input);
    return queryService.read(id);
  }

  @Override
  public EligibleTrainOutput read(Long id) {
    return queryService.read(id);
  }

  @Override
  public CollectionModificationOutput bulk(CollectionModificationInput<EligibleTrainInput> input) {
    eventStore.validateFunctionLock(OperationFunction.ELIGIBLE_TRAIN);
    return bulkService.bulk(input);
  }

  public void clean(LocalDate expiryDate) {
    commandService.clean(expiryDate);
  }
}
