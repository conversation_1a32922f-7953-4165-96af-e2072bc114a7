/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source
 * code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.cronjob.enums.FutureVersionControlStatus;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainDetailDto;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainFareOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.EligibleTrainFarePlanType;
import com.ibm.tw.thsrc.bsm.fa.enums.EligibleTrainStatus;
import com.ibm.tw.thsrc.bsm.fa.enums.FarePlanType;
import com.ibm.tw.thsrc.bsm.fa.enums.FareProjectType;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrain;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainDetail;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainDetailTrain;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainDetailTrain_;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainDetail_;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainFutureVersionControl;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainTemp;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrain_;
import com.ibm.tw.thsrc.bsm.fare.entity.FarePlan;
import com.ibm.tw.thsrc.bsm.fare.entity.FarePlan_;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject_;
import com.ibm.tw.thsrc.bsm.fare.repository.EligibleTrainFutureVersionControlRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.EligibleTrainRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.EligibleTrainTempRepository;
import com.ibm.tw.thsrc.bsm.fare.vo.EligibleTrainFilter;
import com.ibm.tw.thsrc.bsm.fare.vo.EligibleTrainSort;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import javax.transaction.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.TypedSort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class EligibleTrainQueryService extends
    AbstractQueryService<EligibleTrain, EligibleTrainOutput, EligibleTrainFilter, EligibleTrainSort> implements
    EligibleTrainQueryApi {

  @Autowired
  private EligibleTrainRepository repo;

  @Autowired
  private EligibleTrainTempRepository tempRepo;

  @Autowired
  private EligibleTrainFutureVersionControlRepository futureVersionRepo;

  @Autowired
  private ObjectMapper objectMapper;

  @Override
  public EligibleTrainOutput read(Long id) {
    EligibleTrain entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    return translate(entity);
  }

  @Override
  public Page<EligibleTrainOutput> search(Search searchRequest) {
    PageRequest pr = translateToPageRequest(searchRequest);
    Specification<EligibleTrain> spec = translateToSpecification(searchRequest);
    Page<EligibleTrain> page = repo.findAll(spec, pr);
    return page.map(this::translate);
  }

  @Override
  protected EligibleTrainOutput translate(EligibleTrain entity) {
    EligibleTrainOutput response = new EligibleTrainOutput();
    response.setId(entity.getId());
    response.setDataVersion(entity.getDataVersion());
    response.setEffDate(entity.getEffDate());
    response.setDscDate(entity.getDscDate());
    response.setEffSaleDate(entity.getEffSaleDate());
    response.setRemark(entity.getRemark());
    response.setPriority(entity.getPriority());
    response.setIsAllTrainEligible(entity.getIsAllTrainEligible());
    response.setIsHolidayIneligible(entity.getIsHolidayIneligible());
    response.setCreateTimestamp(entity.getCreateTimestamp().toLocalDateTime());
    response.setTgExecutionTime(entity.getTgExecutionTime());

    if (Objects.nonNull(entity.getProject())) {
      EligibleTrainFareOutput project = new EligibleTrainFareOutput();
      project.setId(entity.getProject().getId());
      project.setCode(entity.getProject().getCode());
      project.setName(entity.getProject().getName());

      response.setFareProject(project);
    }

    if (entity.getFarePlan().getType().equals(FarePlanType.BASIC_FARE_PLAN)) {
      EligibleTrainFareOutput basicFarePlan = new EligibleTrainFareOutput();
      basicFarePlan.setId(entity.getFarePlan().getId());
      basicFarePlan.setCode(entity.getFarePlan().getCode());
      basicFarePlan.setName(entity.getFarePlan().getName());

      response.setBasicFarePlan(basicFarePlan);
    }

    if (entity.getFarePlan().getType().equals(FarePlanType.PROMOTION)) {
      EligibleTrainFareOutput promotion = new EligibleTrainFareOutput();
      promotion.setId(entity.getFarePlan().getId());
      promotion.setCode(entity.getFarePlan().getCode());
      promotion.setName(entity.getFarePlan().getName());

      response.setPromotion(promotion);
    }

    List<EligibleTrainDetailDto> details = new ArrayList<>();
    entity.getDetails().forEach(detail -> {

      EligibleTrainDetailDto detailDto = new EligibleTrainDetailDto();
      detailDto.setDayOfWeek(detail.getDayOfWeek());
      List<String> trains = detail.getTrains().stream().map(EligibleTrainDetailTrain::getTrainNum)
          .collect(Collectors.toList());
      detailDto.setTrains(trains);
      details.add(detailDto);
    });

    response.setDetails(details);

    return response;
  }

  @Override
  protected Sort translate(EligibleTrainSort sortBy, Direction direction) {
    TypedSort<EligibleTrain> typedSort = Sort.sort(EligibleTrain.class);
    setTypeSort(sortBy, typedSort);

    if (typedSort.isSorted()) {
      if (Sort.Direction.DESC == direction) {
        return typedSort.descending();
      } else {
        return typedSort.ascending();
      }
    } else {
      return typedSort;
    }
  }

  private void setTypeSort(EligibleTrainSort sortBy, TypedSort<EligibleTrain> typedSort) {
    if (sortBy.isProjectCode()) {
      typedSort//
          .by(EligibleTrain::getProject).by(FareProject::getCode);
    } else if (sortBy.isProjectName()) {
      typedSort//
          .by(EligibleTrain::getProject).by(FareProject::getName);
    } else if (sortBy.isEffDate()) {
      typedSort//
          .by(EligibleTrain::getEffDate);
    } else if (sortBy.isDscDate()) {
      typedSort//
          .by(EligibleTrain::getDscDate);
    } else if (sortBy.isEffSaleDate()) {
      typedSort//
          .by(EligibleTrain::getEffSaleDate);
    } else if (sortBy.isFareType()) {
      typedSort//
          .by(EligibleTrain::getFarePlan).by(FarePlan::getType);
    } else if (sortBy.isFarePlanCode()) {
      typedSort//
          .by(EligibleTrain::getFarePlan).by(FarePlan::getCode);
    } else if (sortBy.isFarePlanName()) {
      typedSort//
          .by(EligibleTrain::getFarePlan).by(FarePlan::getName);
    } else if (sortBy.isPriority()) {
      typedSort//
          .by(EligibleTrain::getPriority);
    } else if (sortBy.isAllTrainEligible()) {
      typedSort//
          .by(EligibleTrain::getIsAllTrainEligible);
    } else if (sortBy.isHolidayIneligible()) {
      typedSort//
          .by(EligibleTrain::getIsHolidayIneligible);
    } else if (sortBy.isCreateTimestamp()) {
      typedSort//
          .by(EligibleTrain::getCreateTimestamp);
    } else if (sortBy.isTgExecutionTime()) {
      typedSort//
          .by(EligibleTrain::getTgExecutionTime);
    }
  }

  @Override
  protected Predicate translate(ComparisonExpression<EligibleTrainFilter> expression,
      Root<EligibleTrain> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    ComparisonOperator op = expression.getOperator();

    if (expression.getAttributeValue().getBusinessProjectCode() != null) {

      Path<FareProjectType> type = root.join(EligibleTrain_.PROJECT).get(FareProject_.TYPE);
      Path<String> code = root.join(EligibleTrain_.PROJECT).get(FareProject_.CODE);
      String value = expression.getAttributeValue().getBusinessProjectCode();
      return criteriaBuilder.and(
          translate(type, ComparisonOperator.EQ, FareProjectType.BUSINESS, criteriaBuilder),
          translate(code, op, value, criteriaBuilder));

    } else if (expression.getAttributeValue().getStandardProjectCode() != null) {

      Path<FareProjectType> type = root.join(EligibleTrain_.PROJECT).get(FareProject_.TYPE);
      Path<String> code = root.join(EligibleTrain_.PROJECT).get(FareProject_.CODE);
      String value = expression.getAttributeValue().getStandardProjectCode();
      return criteriaBuilder.and(
          translate(type, ComparisonOperator.EQ, FareProjectType.STANDARD, criteriaBuilder),
          translate(code, op, value, criteriaBuilder));

    } else if (expression.getAttributeValue().getProjectName() != null) {

      Path<String> path = root.join(EligibleTrain_.PROJECT).get(FareProject_.NAME);
      String value = expression.getAttributeValue().getProjectName();
      return translate(path, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getFarePlanType() != null) {

      Path<FarePlanType> path = root.join(EligibleTrain_.FARE_PLAN).get(FarePlan_.TYPE);
      EligibleTrainFarePlanType type = expression.getAttributeValue().getFarePlanType();
      FarePlanType value = null;
      if (type.equals(EligibleTrainFarePlanType.BASIC_FARE_PLAN)) {
        value = FarePlanType.BASIC_FARE_PLAN;
      } else if (type.equals(EligibleTrainFarePlanType.PROMOTION)) {
        value = FarePlanType.PROMOTION;
      }

      return Objects.nonNull(value) ? translate(path, ComparisonOperator.EQ, value, criteriaBuilder)
          : null;

    } else if (expression.getAttributeValue().getFarePlanCode() != null) {

      Path<String> path = root.join(EligibleTrain_.FARE_PLAN).get(FarePlan_.CODE);
      String value = expression.getAttributeValue().getFarePlanCode();
      return translate(path, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getFarePlanCodes() != null && StringUtils.isNotBlank(
        expression.getAttributeValue().getFarePlanCodes())) {
      return this.generateFarePlanCodesFilter(expression, root);

      // 結束日在條件之後=>之後都有效 ge
    } else if (expression.getAttributeValue().getStartTrainDate() != null) {

      Path<LocalDate> dscDate = root.get(EligibleTrain_.DSC_DATE);
      LocalDate value = expression.getAttributeValue().getStartTrainDate();
      return translate(dscDate, op, value, criteriaBuilder);

      // 起始日在條件之前=>之前都有效 le
    } else if (expression.getAttributeValue().getEndTrainDate() != null) {

      Path<LocalDate> effDate = root.get(EligibleTrain_.EFF_DATE);
      LocalDate value = expression.getAttributeValue().getEndTrainDate();
      return translate(effDate, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getTrainNumber() != null) {

      Subquery<Integer> subQuery = query.subquery(Integer.class);
      Root<EligibleTrainDetailTrain> subQueryRoot2 = subQuery.from(EligibleTrainDetailTrain.class);
      Root<EligibleTrainDetail> subQueryRoot1 = subQuery.from(EligibleTrainDetail.class);

      Path<String> trainNumber = subQueryRoot2.get(EligibleTrainDetailTrain_.TRAIN_NUM);
      Path<Boolean> isAllTrains = root.get(EligibleTrain_.IS_ALL_TRAIN_ELIGIBLE);

      String value = expression.getAttributeValue().getTrainNumber();

      Predicate p2 = translate(trainNumber, op, value, criteriaBuilder);
      Predicate p1 = criteriaBuilder.equal(
          subQueryRoot2.get(EligibleTrainDetailTrain_.ELIGIBLE_TRAIN_DETAIL), subQueryRoot1);

      Predicate p0 = criteriaBuilder.equal(subQueryRoot1.get(EligibleTrainDetail_.ELIGIBLE_TRAIN),
          root);

      subQuery.select(criteriaBuilder.literal(1)).where(p1, p2, p0);

      return criteriaBuilder.or(
          translate(isAllTrains, ComparisonOperator.EQ, Boolean.TRUE, criteriaBuilder),
          criteriaBuilder.exists(subQuery));

    } else if (expression.getAttributeValue().getStatus() != null) {
      Path<LocalDate> dscPath = root.get(EligibleTrain_.DSC_DATE);
      Path<LocalDateTime> tgExecutionTimePath = root.get(EligibleTrain_.TG_EXECUTION_TIME);
      EligibleTrainStatus status = expression.getAttributeValue().getStatus();

      Predicate outdatedPredicate = translate(dscPath, ComparisonOperator.LT, LocalDate.now(),
          criteriaBuilder);

      Predicate nullTgExecutionPredicate = criteriaBuilder.isNull(tgExecutionTimePath);

      if (EligibleTrainStatus.SCHEDULED.equals(status)) {
        return criteriaBuilder.and(nullTgExecutionPredicate);
      } else if (EligibleTrainStatus.EFFECTIVE.equals(status)) {
        return criteriaBuilder.and(nullTgExecutionPredicate.not(), outdatedPredicate.not());
      } else if (EligibleTrainStatus.EXPIRED.equals(status)) {
        return criteriaBuilder.and(outdatedPredicate);
      } else {
        return null;
      }

    } else {
      return null;
    }
  }

  @Override
  protected Function<Map<String, Boolean>, EligibleTrainSort> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, EligibleTrainSort.class);
  }

  @Override
  protected Function<Map<String, Object>, EligibleTrainFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, EligibleTrainFilter.class);
  }

  // 只查詢需要下 TG 的
  @Override
  public List<EligibleTrainOutput> getEligibleTrain(String farePlanCode, LocalDate effSaleDate) {
    return repo.findByFarePlan_CodeAndEffSaleDateBeforeAndIsAllTrainEligibleFalse(farePlanCode,
        effSaleDate.plusDays(1)).stream().map(this::translate).collect(Collectors.toList());
  }

  private Predicate generateFarePlanCodesFilter(
      ComparisonExpression<EligibleTrainFilter> expression, Root<EligibleTrain> root) {
    List<String> farePlanCodes = Arrays.asList(
        expression.getAttributeValue().getFarePlanCodes().split(","));
    Path<String> path = root.get(EligibleTrain_.FARE_PLAN).get(FarePlan_.CODE);

    return path.in(farePlanCodes);
  }
}
