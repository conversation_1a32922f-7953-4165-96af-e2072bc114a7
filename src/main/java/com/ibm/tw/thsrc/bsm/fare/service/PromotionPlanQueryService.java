/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.DateRangeDto;
import com.ibm.tw.thsrc.bsm.core.dto.filter.ComparisonOperator;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.AbstractQueryService;
import com.ibm.tw.thsrc.bsm.core.service.ComparisonExpression;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.AdditionalProfileDiscountOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.PassengerProfileOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.EligiblePassengerProfileType;
import com.ibm.tw.thsrc.bsm.fa.enums.EligibleTrainType;
import com.ibm.tw.thsrc.bsm.fa.enums.FareRule;
import com.ibm.tw.thsrc.bsm.fa.enums.PromotionServiceType;
import com.ibm.tw.thsrc.bsm.fare.entity.FarePlan_;
import com.ibm.tw.thsrc.bsm.fare.entity.FareRuleSetting;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerProfile;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan_;
import com.ibm.tw.thsrc.bsm.fare.repository.PassengerProfileRepository;
import com.ibm.tw.thsrc.bsm.fare.repository.PromotionPlanRepository;
import com.ibm.tw.thsrc.bsm.fare.translate.PassengerProfileTranslator;
import com.ibm.tw.thsrc.bsm.fare.vo.PromotionPlanFilter;
import com.ibm.tw.thsrc.bsm.fare.vo.PromotionPlanSort;
import com.ibm.tw.thsrc.bsm.sc.dto.ClassOutput;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.TypedSort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

@Service
@Transactional
public class PromotionPlanQueryService extends
    AbstractQueryService<PromotionPlan, PromotionOutput, PromotionPlanFilter, PromotionPlanSort> {

  private static final String OPEN_DATE = "#";

  @Autowired
  private ObjectMapper objectMapper;

  @Autowired
  private PromotionPlanRepository repo;

  @Autowired
  private PassengerProfileRepository profileRepo;

  @Override
  public PromotionOutput read(Long id) {
    PromotionPlan entity = repo.findById(id).orElseThrow(ResourceNotFoundException::new);
    return this.translate(entity);
  }

  @Override
  public Page<PromotionOutput> search(Search searchRequest) {
    PageRequest pr = translateToPageRequest(searchRequest);
    Specification<PromotionPlan> spec = translateToSpecification(searchRequest);
    Page<PromotionPlan> page = repo.findAll(spec, pr);
    return page.map(this::translate);
  }

  @Override
  public PromotionOutput translate(PromotionPlan entity) {
    PromotionOutput response = new PromotionOutput();
    response.setId(entity.getId());
    response.setCode(entity.getCode());
    response.setName(entity.getName());

    response.setEffDate(entity.getEffDate());
    response.setDscDate(entity.getDscDate());
    response.setType(entity.getType());
    response.setServiceType(entity.getServiceType());

    response.setDataVersion(entity.getDataVersion());

    response.setDiscountAmt(entity.getDiscountAmt());
    response.setDiscountPct(entity.getDiscountPct());
    response.setIsSmallGroup(entity.isSmallGroup());

    response.setAllClassesValid(entity.isAllClassesValid());

    entity.getValidClasses().forEach(e -> {
      ClassOutput output = new ClassOutput();
      output.setBaseClass(e.getBaseClass().getCode());
      output.setCode(e.getCode());
      output.setDisplayOrder(e.getDisplayOrder());
      output.setId(e.getId());
      output.setIsBaseClass(e.getIsBaseClass());
      output.setIsDisplayable(e.getIsDisplayable());
      response.getValidClasses().add(output);
    });

    List<PassengerProfile> validPassengerProfiles;

    if (EligiblePassengerProfileType.INVALID.equals(entity.getEligiblePassengerProfileType())) {
      validPassengerProfiles = new ArrayList<>(profileRepo.findAll());
      validPassengerProfiles.removeAll(entity.getPassengerProfiles());
    } else if (EligiblePassengerProfileType.ALL.equals(entity.getEligiblePassengerProfileType())) {
      validPassengerProfiles = profileRepo.findAll();
    } else {
      validPassengerProfiles = entity.getPassengerProfiles();
    }
    validPassengerProfiles.forEach(p -> {
      PassengerProfileOutput output = PassengerProfileTranslator.toPassengerProfileOutput(p);
      response.getValidPassengerProfiles().add(output);
    });

    entity.getAdditionalProfileDiscount().stream().forEach(extraDiscount -> {
      AdditionalProfileDiscountOutput extraDiscountOutput = new AdditionalProfileDiscountOutput();
      extraDiscountOutput.setPassengerProfileId(extraDiscount.getPassengerProfile().getId());
      extraDiscountOutput.setPassengerProfileName(
          extraDiscount.getPassengerProfile().getZhPrintName());
      extraDiscountOutput.setPctOff(extraDiscount.getAdditionalDiscount());

      response.getAdditionalProfileDiscounts().add(extraDiscountOutput);
    });

    entity.getInvalidDateRanges().forEach(d -> {
      DateRangeDto dto = DateRangeDto.builder()//
          .beginDate(d.getBeginDate())//
          .endDate(d.getEndDate())//
          .build();
      response.getInvalidDateRanges().add(dto);
    });
    
    response.getInvalidDateRanges().sort(Comparator.comparing(DateRangeDto::getBeginDate));
    this.translateFareRuleSetting(response, entity);

    return response;
  }

  private void translateFareRuleSetting(PromotionOutput response, PromotionPlan entity) {
    List<FareRuleSetting> settings = entity.getFareRuleSettings();

    DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    settings.forEach(s -> {
      if (FareRule.R08_CHANGE_RULE.equals(s.getFareRule())) {
        response.setModifiable(Boolean.parseBoolean(s.getSetting()));
      } else if (FareRule.R09_DSC_SALE_DATE.equals(s.getFareRule())) {
        setR09Response(response, dtf, s);
      } else if (FareRule.R12_VALID_WEEKDAY.equals(s.getFareRule())) {
        response.setFrequency(s.getSetting());
      } else if (FareRule.R16_IMMEDIATE_PAYMENT.equals(s.getFareRule())) {
        response.setImmediatePayment(Boolean.parseBoolean(s.getSetting()));
      } else if (FareRule.R20_CONTENT.equals(s.getFareRule())) {
        response.setContent(s.getSetting());
      } else if (FareRule.R21_DESCRIPTION.equals(s.getFareRule())) {
        response.setDescription(s.getSetting());
      } else if (FareRule.R34_ELIGIBLE_TRAIN.equals(s.getFareRule())) {
        response.setEligibleTrainType(EligibleTrainType.valueOf(s.getSetting()));
      } else if (FareRule.R51_EFF_SALE_DATE.equals(s.getFareRule())) {
        setR51Response(response, dtf, s);
      } else if (FareRule.R61_CHANGE_RULE.equals(s.getFareRule())) {
        response.setModifiable(Boolean.parseBoolean(s.getSetting()));
      }
    });
  }

  private void setR51Response(PromotionOutput response, DateTimeFormatter dtf, FareRuleSetting s) {
    response.setEffSaleDate(
        s.getSetting().equals(OPEN_DATE) ? null : LocalDate.parse(s.getSetting(), dtf));
  }

  private void setR09Response(PromotionOutput response, DateTimeFormatter dtf, FareRuleSetting s) {
    response.setDscSaleDate(
        s.getSetting().equals(OPEN_DATE) ? null : LocalDate.parse(s.getSetting(), dtf));
  }

  @Override
  protected Sort translate(PromotionPlanSort sortBy, Sort.Direction direction) {
    TypedSort<PromotionPlan> typedSort = Sort.sort(PromotionPlan.class);

    if (sortBy.isCode()) {
      typedSort//
          .by(PromotionPlan::getCode);
    } else if (sortBy.isEffDate()) {
      typedSort//
          .by(PromotionPlan::getEffDate);
    } else if (sortBy.isDscDate()) {
      typedSort//
          .by(PromotionPlan::getDscDate);
    } else if (sortBy.isServiceType()) {
      typedSort//
          .by(PromotionPlan::getServiceType);
    }

    if (typedSort.isSorted()) {
      if (Sort.Direction.DESC == direction) {
        return typedSort.descending();
      } else {
        return typedSort.ascending();
      }
    } else {
      return typedSort;
    }
  }

  @Override
  protected Predicate translate(ComparisonExpression<PromotionPlanFilter> expression,
      Root<PromotionPlan> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {

    if (expression.getAttributeValue().getIsEffective() != null) {

      Path<LocalDate> effPath = root.get(FarePlan_.EFF_DATE);
      Path<LocalDate> dscPath = root.get(FarePlan_.DSC_DATE);
      Boolean isEffective = expression.getAttributeValue().getIsEffective();

      Predicate effectivePredicate = translate(effPath, ComparisonOperator.LE, LocalDate.now(),
          criteriaBuilder);
      Predicate outdatedPredicate = translate(dscPath, ComparisonOperator.LT, LocalDate.now(),
          criteriaBuilder);
      Predicate nullEffDatePredicate = criteriaBuilder.isNull(effPath);
      Predicate nullDscDatePredicate = criteriaBuilder.isNull(dscPath);
      return Boolean.FALSE.equals(isEffective) ? criteriaBuilder.and(outdatedPredicate)
          : criteriaBuilder.or(//
              criteriaBuilder.and(nullEffDatePredicate, nullDscDatePredicate),
              criteriaBuilder.and(effectivePredicate,
                  criteriaBuilder.or(outdatedPredicate.not(), nullDscDatePredicate))//
          );

    } else if (expression.getAttributeValue().getCode() != null) {

      ComparisonOperator op = expression.getOperator();
      Path<String> code = root.get(FarePlan_.CODE);
      String value = expression.getAttributeValue().getCode();

      return translate(code, op, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getIsSmallGroup() != null) {

      Path<Boolean> isSmallGroup = root.get(PromotionPlan_.IS_SMALL_GROUP);
      boolean value = expression.getAttributeValue().getIsSmallGroup();

      return translate(isSmallGroup, ComparisonOperator.EQ, value, criteriaBuilder);

    } else if (expression.getAttributeValue().getServiceType() != null) {

      Path<PromotionServiceType> path = root.get(PromotionPlan_.SERVICE_TYPE);
      PromotionServiceType value = expression.getAttributeValue().getServiceType();

      return Objects.nonNull(value) ? translate(path, ComparisonOperator.EQ, value, criteriaBuilder)
          : null;
    } else {
      return null;
    }
  }

  @Override
  protected Function<Map<String, Boolean>, PromotionPlanSort> getSortByTranslator() {
    return data -> objectMapper.convertValue(data, PromotionPlanSort.class);
  }

  @Override
  protected Function<Map<String, Object>, PromotionPlanFilter> getFilterAttributeValueTranslator() {
    return data -> objectMapper.convertValue(data, PromotionPlanFilter.class);
  }
}
