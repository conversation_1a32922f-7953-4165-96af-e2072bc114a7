/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.repository;

import com.ibm.tw.thsrc.bsm.fare.entity.FreeSeatingProfileDiscount;
import java.util.Collection;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface FreeSeatingProfileDiscountRepository extends
    JpaRepository<FreeSeatingProfileDiscount, Long>,
    JpaSpecificationExecutor<FreeSeatingProfileDiscount> {
  Collection<FreeSeatingProfileDiscount> findByFreeSeatingTicketType_IdIn(Set<Long> ids);
}
