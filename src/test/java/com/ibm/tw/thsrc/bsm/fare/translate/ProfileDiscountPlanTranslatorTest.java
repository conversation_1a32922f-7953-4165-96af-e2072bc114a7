package com.ibm.tw.thsrc.bsm.fare.translate;

import static org.junit.jupiter.api.Assertions.*;

import com.ibm.tw.thsrc.bsm.fa.dto.ProfileDiscountPlanOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerProfile;
import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountPlan;
import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountType;
import java.math.BigDecimal;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ProfileDiscountPlanTranslatorTest {

  @Test
  void toProfileDiscountPlanOutput() {

    PassengerProfile profile = new PassengerProfile();
    profile.setCode("F");
    profile.setZhName("成人");
    profile.setDisplayOrder(1);

    ProfileDiscountType type = new ProfileDiscountType();
    type.setCode("code");
    type.setName("name");

    ProfileDiscountPlan entity = new ProfileDiscountPlan();
    entity.setPassengerProfile(profile);
    entity.setProfileDiscountType(type);
    entity.setDiscountPct(BigDecimal.ONE);

    ProfileDiscountPlanOutput output = ProfileDiscountPlanTranslator.toProfileDiscountPlanOutput(
        entity);
    Assertions.assertEquals(output.getDiscountTypeCode(),
        entity.getProfileDiscountType().getCode());
    Assertions.assertEquals(output.getProfileCode(), entity.getPassengerProfile().getCode());
    Assertions.assertEquals(output.getDiscountPct(), entity.getDiscountPct());
  }
}