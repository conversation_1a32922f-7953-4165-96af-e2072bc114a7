/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryMappingOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryValidationInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryValidationOutput;
import com.ibm.tw.thsrc.bsm.fare.service.PromotionCategoryMappingCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.PromotionCategoryMappingQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.PromotionCategoryMappingTestData;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@SpringBootTest(classes = {FareApplication.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@AutoConfigureMockMvc
class PromotionCategoryMappingControllerTest {

  private PromotionCategoryMappingTestData TEST_DATA = new PromotionCategoryMappingTestData();
  @Autowired
  private PromotionCategoryMappingController controller;
  @MockBean
  private PromotionCategoryMappingCommandService commandService;
  @MockBean
  private PromotionCategoryMappingQueryService queryService;

  public PromotionCategoryMappingControllerTest() throws Exception {
  }

  @Test
  void read() {
    Long testId = Long.MAX_VALUE - 1;
    Mockito.when(queryService.read(testId)).thenReturn(TEST_DATA.RECENT_OUTPUTS.get(0));

    PromotionCategoryMappingOutput expected = TEST_DATA.RECENT_OUTPUTS.get(0);
    PromotionCategoryMappingOutput actual = controller.read(testId);

    Assertions.assertEquals(expected.getId(), actual.getId());
    Assertions.assertEquals(expected.getEffDate(), actual.getEffDate());
    Assertions.assertEquals(expected.getIsTweDisplay(), actual.getIsTweDisplay());
    Assertions.assertEquals(expected.getPromotionCategoryId(), actual.getPromotionCategoryId());
    Assertions.assertEquals(expected.getPromotionCategoryName(), actual.getPromotionCategoryName());
    Assertions.assertEquals(expected.getPromotionId(), actual.getPromotionId());
    Assertions.assertEquals(expected.getPromotionCode(), actual.getPromotionCode());
    Assertions.assertEquals(expected.getPromotionDscDate(), actual.getPromotionDscDate());
    Assertions.assertEquals(expected.getProjects().size(), actual.getProjects().size());
  }

  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any()))
        .thenReturn(new PageImpl<>(TEST_DATA.RECENT_OUTPUTS));

    List<PromotionCategoryMappingOutput> expected = TEST_DATA.RECENT_OUTPUTS;
    Search search = new Search();
    Page<PromotionCategoryMappingOutput> actual = controller.search(search);

    Assertions.assertEquals(expected.size(), actual.getContent().size());
  }

  @Test
  void patch() {
    Mockito.when(commandService.patch(ArgumentMatchers.any())).thenReturn(new HashSet<>());
    Mockito.when(queryService.read(ArgumentMatchers.anySet()))
        .thenReturn(new HashSet<>(TEST_DATA.RECENT_OUTPUTS));

    Set<PromotionCategoryMappingOutput> actual = controller.modify(TEST_DATA.CREATE_INPUT);

    Assertions.assertEquals(1, actual.size());
  }

  @Test
  void validate() {
    PromotionCategoryValidationInput input = TEST_DATA.VALIDATE_INPUT;
    Mockito.when(queryService.validate(ArgumentMatchers.any())).thenReturn(TEST_DATA.VALIDATION_OUTPUT);
    PromotionCategoryValidationOutput expected = TEST_DATA.VALIDATION_OUTPUT;
    PromotionCategoryValidationOutput actual = controller.validate(input);
    Assertions.assertEquals(expected.getCurrentEffDate(), actual.getCurrentEffDate());
    Assertions.assertEquals(expected.getFutureEffDate(), actual.getFutureEffDate());
    Assertions.assertEquals(expected.getCurrentMapping().size(), actual.getCurrentMapping().size());
    Assertions.assertEquals(expected.getFutureMapping().size(), actual.getFutureMapping().size());
  }
}
