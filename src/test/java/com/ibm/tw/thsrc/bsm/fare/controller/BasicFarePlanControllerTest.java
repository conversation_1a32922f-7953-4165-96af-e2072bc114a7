/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.controller;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.BasicFarePlanOutput;
import com.ibm.tw.thsrc.bsm.fare.service.BasicFarePlanCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.BasicFarePlanQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.BasicFarePlanTestData;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest(classes = {FareApplication.class})
@AutoConfigureMockMvc
class BasicFarePlanControllerTest {

  private static final String API_PATH = "/BasicFarePlans";
  private static final String API_PATH_WITH_ID = "/BasicFarePlans/2";
  @Autowired
  ObjectMapper mapper;
  @Autowired
  private BasicFarePlanController controller;
  @Autowired
  private MockMvc mockMvc;
  @MockBean
  private BasicFarePlanCommandService commandService;
  @MockBean
  private BasicFarePlanQueryService queryService;
  private BasicFarePlanTestData TEST_DATA = new BasicFarePlanTestData();

  public BasicFarePlanControllerTest() throws Exception {
  }

  @AfterEach
  void resetMock() {
    Mockito.reset(commandService);
    Mockito.reset(queryService);
  }

  @Test
  void read() {
    Mockito.when(queryService.read(ArgumentMatchers.anyLong())).thenReturn(TEST_DATA.OUTPUT);

    BasicFarePlanOutput expected = TEST_DATA.OUTPUT;

    Long testId = 1L;
    BasicFarePlanOutput actual = controller.read(testId);

    Assertions.assertEquals(expected.getCode(), actual.getCode());
  }

  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any()))
        .thenReturn(new PageImpl<>(Collections.singletonList(TEST_DATA.OUTPUT)));

    List<BasicFarePlanOutput> expected = Collections.singletonList(TEST_DATA.OUTPUT);
    Page<BasicFarePlanOutput> resultPage = controller.search(new Search());
    Assertions.assertEquals(expected.size(), resultPage.getContent().size());
  }

  @Test
  void create() {
    Mockito.when(queryService.read(ArgumentMatchers.any())).thenReturn(TEST_DATA.OUTPUT);

    BasicFarePlanOutput expected = TEST_DATA.OUTPUT;
    BasicFarePlanOutput actual = controller.create(TEST_DATA.VALID_INPUT);

    Assertions.assertEquals(expected.getCode(), actual.getCode());
    Assertions.assertEquals(expected.getName(), actual.getName());
    Assertions.assertEquals(expected.getEffDate(), actual.getEffDate());
    Assertions.assertEquals(expected.getDscDate(), actual.getDscDate());
  }

  @Test
  void replace() {
    Mockito.when(queryService.read(ArgumentMatchers.any())).thenReturn(TEST_DATA.OUTPUT);

    Long testId = Long.MAX_VALUE - 1;
    BasicFarePlanOutput expected = TEST_DATA.OUTPUT;
    BasicFarePlanOutput actual = controller.replace(testId, TEST_DATA.VALID_INPUT);

    Assertions.assertEquals(expected.getCode(), actual.getCode());
    Assertions.assertEquals(expected.getName(), actual.getName());
    Assertions.assertEquals(expected.getEffDate(), actual.getEffDate());
    Assertions.assertEquals(expected.getDscDate(), actual.getDscDate());
  }

  @Test
  void delete() throws Exception {
    Mockito.doNothing().when(commandService).delete(ArgumentMatchers.any());

    mockMvc.perform(MockMvcRequestBuilders.delete(API_PATH_WITH_ID)).andExpect(status().isOk())
        .andReturn();

    Mockito.verify(commandService, Mockito.times(1)).delete(ArgumentMatchers.any());
  }

  @Test
  void inputValidation() throws Exception {
    mockMvc
        .perform(
            MockMvcRequestBuilders.post(API_PATH)
                .content(mapper.writeValueAsString(TEST_DATA.INVALID_INPUT))
                .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  void resourceNotFound() throws Exception {
    Mockito.when(queryService.read(ArgumentMatchers.anyLong()))
        .thenThrow(ResourceNotFoundException.class);

    mockMvc.perform(MockMvcRequestBuilders.get(API_PATH_WITH_ID)).andExpect(status().isNotFound());
  }
}
