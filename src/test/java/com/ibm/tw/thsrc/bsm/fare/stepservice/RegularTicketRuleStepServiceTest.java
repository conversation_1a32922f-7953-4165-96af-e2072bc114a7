package com.ibm.tw.thsrc.bsm.fare.stepservice;

import static org.junit.jupiter.api.Assertions.*;

import com.ibm.tw.thsrc.bsm.cronjob.value.StepResult;
import com.ibm.tw.thsrc.bsm.cronjob.value.TaskExecution;
import com.ibm.tw.thsrc.bsm.fare.entity.RegularTicketRuleFutureVersionControl;
import com.ibm.tw.thsrc.bsm.fare.entity.RegularTicketRuleTemp;
import com.ibm.tw.thsrc.bsm.fare.repository.RegularTicketRuleFutureVersionControlRepository;
import com.ibm.tw.thsrc.bsm.fare.service.RegularTicketRuleCommandService;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class RegularTicketRuleStepServiceTest {

  RegularTicketRuleStepService service;

  @Mock
  RegularTicketRuleCommandService commandService;

  @Mock
  RegularTicketRuleFutureVersionControlRepository repo;


  @BeforeEach
  void init() throws IllegalAccessException {
    MockitoAnnotations.openMocks(this);
    service = new RegularTicketRuleStepService();

    FieldUtils.writeField(service, "commandService", commandService, true);
    FieldUtils.writeField(service, "repo", repo, true);

    Mockito.doNothing().when(commandService).cronJob(Mockito.any());
    RegularTicketRuleFutureVersionControl control = new RegularTicketRuleFutureVersionControl();

    RegularTicketRuleTemp temp = new RegularTicketRuleTemp();
    temp.setCorrelationId(UUID.randomUUID().toString());

    control.setRegularTicketRuleTemp(temp);

    Mockito.when(repo.save(Mockito.any())).thenReturn(control);
    Mockito.when(repo.findById(Mockito.any())).thenReturn(Optional.of(control));
  }

  @Test
  void testExecute() {
    Map<String, Object> map = new HashMap<>();
    map.put("correlationId", UUID.randomUUID().toString());
    map.put("futureVersionControlId", String.valueOf(1));

    TaskExecution execution = new TaskExecution();
    execution.setParameters(map);

    StepResult<String> resultStep = service.execute(execution);
    Assertions.assertNotNull(resultStep);
  }
}