/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.fa.dto.FareProjectMappingOutput;
import com.ibm.tw.thsrc.bsm.fare.service.FareProjectMappingCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.FareProjectMappingQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.FareProjectMappingTestData;
import com.ibm.tw.thsrc.bsm.res.connection.ResCommunicationService;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest(classes = FareApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@ActiveProfiles("test")
class FareProjectMappingControllerTest {

  private static final String API_PATH = "/FareProjectMapping";
  private FareProjectMappingTestData TEST_DATA = new FareProjectMappingTestData();
  @Autowired
  private FareProjectMappingController controller;
  @MockBean
  private FareProjectMappingCommandService commandService;
  @MockBean
  private FareProjectMappingQueryService queryService;

  @MockBean
  private ResCommunicationService resCommunicationService;
  @MockBean
  private HttpHeaderInterceptor interceptor;
  @Autowired
  private MockMvc mockMvc;

  @Autowired
  private ObjectMapper mapper;

  public FareProjectMappingControllerTest() throws Exception {
  }

  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any())).thenReturn(new PageImpl<>(TEST_DATA.OUTPUTS));

    List<FareProjectMappingOutput> expected = TEST_DATA.OUTPUTS;
    Search search = new Search();
    Page<FareProjectMappingOutput> actual = controller.search(search);

    Assertions.assertEquals(expected.size(), actual.getContent().size());
  }

  @Test
  void create() {
    Long testId = Long.MAX_VALUE - 1;
    Mockito.when(commandService.create(ArgumentMatchers.any())).thenReturn(testId);
    Mockito.when(queryService.read(testId)).thenReturn(TEST_DATA.OUTPUTS.get(0));

    FareProjectMappingOutput expected = TEST_DATA.OUTPUTS.get(0);
    FareProjectMappingOutput actual = controller.create(TEST_DATA.INPUT);

    Assertions.assertEquals(expected.getId(), actual.getId());
    Assertions.assertEquals(expected.getFareProjectId(), actual.getFareProjectId());
    Assertions.assertEquals(expected.getFareProjectCode(), actual.getFareProjectCode());
    Assertions.assertEquals(expected.getPromotionPlanId(), actual.getPromotionPlanId());
    Assertions.assertEquals(expected.getPromotionPlanCode(), actual.getPromotionPlanCode());
  }

  @Test
  void delete() {
    Long testId = Long.MAX_VALUE - 1;
    Assertions.assertDoesNotThrow(() -> controller.delete(testId));
  }

  @Test
  void inputValidation() throws Exception {

    MvcResult invalid = mockMvc
        .perform(MockMvcRequestBuilders.post(API_PATH)
            .content(mapper.writeValueAsString(TEST_DATA.INVALID_INPUT))
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andReturn();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(),
        invalid.getResponse().getStatus());
  }
}
