/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.StoredValueTicketInput;
import com.ibm.tw.thsrc.bsm.fa.dto.StoredValueTicketOutput;
import com.ibm.tw.thsrc.bsm.fare.service.StoredValueTicketCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.StoredValueTicketQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.StoredValueTicketTestData;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest(classes = {FareApplication.class})
@AutoConfigureMockMvc
@ActiveProfiles("test")
class StoredValueTicketControllerTest {

  private static final String API_PATH = "/StoredValueTickets/1";
  private static final StoredValueTicketTestData TEST_DATA = new StoredValueTicketTestData();
  @Autowired
  private StoredValueTicketController controller;
  @MockBean
  private StoredValueTicketCommandService commandService;
  @MockBean
  private StoredValueTicketQueryService queryService;
  @Autowired
  private MockMvc mockMvc;
  @Autowired
  private ObjectMapper mapper;

  @Test
  void read() {
    Mockito.when(queryService.read(ArgumentMatchers.anyLong())).thenReturn(TEST_DATA.OUTPUT);
    Long testId = Long.MAX_VALUE - 1;

    StoredValueTicketOutput expected = TEST_DATA.OUTPUT;

    StoredValueTicketOutput actual = controller.read(testId);
    Assertions.assertEquals(expected.getId(), actual.getId());
    Assertions.assertEquals(expected.getMinAmt(), actual.getMinAmt());
    Assertions.assertEquals(expected.getMaxAmt(), actual.getMaxAmt());
    Assertions.assertEquals(expected.getExpiryType(), actual.getExpiryType());
    Assertions.assertEquals(expected.getDataVersion(), actual.getDataVersion());
    Assertions.assertEquals(expected.getDiscountPct(), actual.getDiscountPct());
    Assertions.assertEquals(expected.getRefundFeeAmt(), actual.getRefundFeeAmt());
    Assertions.assertEquals(expected.getMaxAmtPerTxn(), actual.getMaxAmtPerTxn());
    Assertions.assertEquals(expected.getMaxAmtPerLoad(), actual.getMaxAmtPerLoad());
    Assertions.assertEquals(expected.getLoadAmount().size(), actual.getLoadAmount().size());
    Assertions.assertEquals(expected.getMagnetTktValidDays(), actual.getMagnetTktValidDays());
  }

  @Test
  void search() {
    Mockito.when(queryService.search())
        .thenReturn(new PageImpl<>(Collections.singletonList(TEST_DATA.OUTPUT)));

    List<StoredValueTicketOutput> expected = Collections.singletonList(TEST_DATA.OUTPUT);

    Page<StoredValueTicketOutput> actual = controller.search(new Search());
    Assertions.assertEquals(expected.size(), actual.getContent().size());
  }

  @Test
  void replace() {
    Mockito.when(queryService.read(ArgumentMatchers.anyLong())).thenReturn(TEST_DATA.OUTPUT);
    Long testId = Long.MAX_VALUE - 1;
    StoredValueTicketOutput expected = TEST_DATA.OUTPUT;
    StoredValueTicketOutput actual = controller.replace(testId, TEST_DATA.INPUT);

    Assertions.assertEquals(expected.getId(), actual.getId());
    Assertions.assertEquals(expected.getDataVersion(), actual.getDataVersion());
  }

  @Test
  void inputValidation() throws Exception {
    for (StoredValueTicketInput invalidInput : TEST_DATA.INVALID_INPUTS) {
      MvcResult mvcResult = mockMvc
          .perform(MockMvcRequestBuilders.put(API_PATH)
              .content(mapper.writeValueAsString(invalidInput))
              .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
          .andReturn();

      Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(),
          mvcResult.getResponse().getStatus());

    }
  }
}
