/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.core.dto.DateRangeDto;
import com.ibm.tw.thsrc.bsm.core.entity.AggregateRoot;
import com.ibm.tw.thsrc.bsm.core.entity.BaseEntity;
import com.ibm.tw.thsrc.bsm.core.entity.DateRange;
import com.ibm.tw.thsrc.bsm.fa.dto.AdditionalProfileDiscountInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.EligiblePassengerProfileType;
import com.ibm.tw.thsrc.bsm.fa.enums.EligibleTrainType;
import com.ibm.tw.thsrc.bsm.fa.enums.FarePlanType;
import com.ibm.tw.thsrc.bsm.fa.enums.FareRule;
import com.ibm.tw.thsrc.bsm.fare.entity.AdditionalProfileDiscount;
import com.ibm.tw.thsrc.bsm.fare.entity.ClassProjection;
import com.ibm.tw.thsrc.bsm.fare.entity.FareRuleSetting;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerProfile;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class PromotionPlanTestData {

  public PromotionPlan ENTITY = new PromotionPlan();
  public PromotionPlan ENTITY2 = new PromotionPlan();
  public PromotionPlan ENTITY3 = new PromotionPlan();
  public ClassProjection CLASS_ENTITY = new ClassProjection();
  public PassengerProfile PROFILE_ENTITY = new PassengerProfile();

  public PromotionInput CREATE_INPUT = new PromotionInput();
  public PromotionInput UPDATE_INPUT = new PromotionInput();
  public PromotionInput EFF_NULL_WHILE_DSC_NOT_INPUT = new PromotionInput();
  public PromotionInput EFF_SALE_NULL_WHILE_DSC_NOT_INPUT = new PromotionInput();
  public PromotionInput DATE_RANGE_OVERLAP_INPUT = new PromotionInput();
  public List<PromotionInput> INVALID_INPUTS = new ArrayList<>();
  public PromotionOutput OUTPUT = new PromotionOutput();
  public PromotionOutput OUTPUT2 = new PromotionOutput();

  public PromotionPlanTestData() throws Exception {
    createEntityTestData();
    createOutputTestData();
    createInputTestData();
  }

  void createEntityTestData() throws Exception {
    setPROFILE_ENTITY();
    setCLASS_ENTITY();

    ENTITY = genSameSettingEntity(1L, "P000", LocalDate.now().minusDays(10),
        LocalDate.now().plusDays(10), true, EligiblePassengerProfileType.VALID);

    ENTITY2 = genSameSettingEntity(2L, "P001", LocalDate.now().minusDays(10),
        LocalDate.now().minusDays(1), false, EligiblePassengerProfileType.ALL);

    ENTITY3 = genSameSettingEntity(3L, "P002", LocalDate.now().minusDays(10),
        LocalDate.now().minusDays(1), false, EligiblePassengerProfileType.INVALID);
  }

  private void setPROFILE_ENTITY() throws Exception {
    Field id = BaseEntity.class.getDeclaredField("id");
    id.setAccessible(true);
    id.set(PROFILE_ENTITY, Long.valueOf(0));
    Field dataVersion = AggregateRoot.class.getDeclaredField("dataVersion");
    dataVersion.setAccessible(true);
    dataVersion.set(PROFILE_ENTITY, Long.valueOf(0));
    PROFILE_ENTITY.setCode("F");
    PROFILE_ENTITY.setIsDisplay(true);
    PROFILE_ENTITY.setDisplayOrder(0);
    PROFILE_ENTITY.setAllowedExtraDiscount(true);
    PROFILE_ENTITY.setZhName("F");
    PROFILE_ENTITY.setZhPrintName("F");
    PROFILE_ENTITY.setEnName("F");
    PROFILE_ENTITY.setEnPrintName("F");
    PROFILE_ENTITY.setTbLightColor("橘");
  }

  private void setCLASS_ENTITY() throws Exception {
    Field id = BaseEntity.class.getDeclaredField("id");
    id.setAccessible(true);
    Field dataVersion = AggregateRoot.class.getDeclaredField("dataVersion");
    dataVersion.setAccessible(true);
    // Open field accessibility
    Field code = ClassProjection.class.getDeclaredField("code");
    code.setAccessible(true);
    Field displayOrder = ClassProjection.class.getDeclaredField("displayOrder");
    displayOrder.setAccessible(true);
    Field isBaseClass = ClassProjection.class.getDeclaredField("isBaseClass");
    isBaseClass.setAccessible(true);
    Field isDisplayable = ClassProjection.class.getDeclaredField("isDisplayable");
    isDisplayable.setAccessible(true);
    Field baseClass = ClassProjection.class.getDeclaredField("baseClass");
    baseClass.setAccessible(true);

    // class entity
    id.set(CLASS_ENTITY, Long.valueOf(0));
    code.set(CLASS_ENTITY, "Y");
    displayOrder.set(CLASS_ENTITY, 1);
    isBaseClass.set(CLASS_ENTITY, true);
    isDisplayable.set(CLASS_ENTITY, true);
    baseClass.set(CLASS_ENTITY, CLASS_ENTITY);
  }


  private PromotionPlan genSameSettingEntity(long entityId, String code, LocalDate effDate,
      LocalDate dscDate, Boolean isSmall, EligiblePassengerProfileType profileType)
      throws Exception {

    PromotionPlan entity = new PromotionPlan();
    entity.setCode(code);
    entity.setType(FarePlanType.PROMOTION);
    entity.setAllClassesValid(false);
    entity.setSmallGroup(isSmall);
    entity.setDiscountAmt(BigDecimal.valueOf(100));
    entity.setEffDate(effDate);
    entity.setDscDate(dscDate);
    entity.setEligiblePassengerProfileType(profileType);

    AdditionalProfileDiscount extraDiscount = new AdditionalProfileDiscount();
    extraDiscount.setFarePlan(entity);
    extraDiscount.setPassengerProfile(PROFILE_ENTITY);
    extraDiscount.setAdditionalDiscount(BigDecimal.valueOf(30));

    DateRange dateRange = new DateRange(effDate, dscDate);

    entity.setAdditionalProfileDiscount(Collections.singletonList(extraDiscount));
    entity.setPassengerProfiles(Arrays.asList(PROFILE_ENTITY));
    entity.setValidClasses(Arrays.asList(CLASS_ENTITY));
    entity.setInvalidDateRanges(Collections.singletonList(dateRange));

    FareRuleSetting rule8 = new FareRuleSetting(FareRule.R08_CHANGE_RULE, "true", entity);
    FareRuleSetting rule9 = new FareRuleSetting(FareRule.R09_DSC_SALE_DATE, dscDate.toString(),
        entity);
    FareRuleSetting rule12 = new FareRuleSetting(FareRule.R12_VALID_WEEKDAY, "SMTWTFS", entity);
    FareRuleSetting rule16 = new FareRuleSetting(FareRule.R16_IMMEDIATE_PAYMENT, "false", entity);
    FareRuleSetting rule20 =
        new FareRuleSetting(FareRule.R20_CONTENT, "TEST PROMOTION", entity);
    FareRuleSetting rule21 =
        new FareRuleSetting(FareRule.R21_DESCRIPTION, "TEST PROMOTION", entity);
    FareRuleSetting rule26 = new FareRuleSetting(FareRule.R26_EFF_DATE, effDate.toString(), entity);
    FareRuleSetting rule34 = new FareRuleSetting(FareRule.R34_ELIGIBLE_TRAIN, isSmall ? "TS" : "TG",
        entity);
    FareRuleSetting rule51 = new FareRuleSetting(FareRule.R51_EFF_SALE_DATE, effDate.toString(),
        entity);
    FareRuleSetting rule61 = new FareRuleSetting(FareRule.R61_CHANGE_RULE, "true", entity);

    entity.getFareRuleSettings().add(rule8);
    entity.getFareRuleSettings().add(rule9);
    entity.getFareRuleSettings().add(rule12);
    entity.getFareRuleSettings().add(rule16);
    entity.getFareRuleSettings().add(rule20);
    entity.getFareRuleSettings().add(rule21);
    entity.getFareRuleSettings().add(rule26);
    entity.getFareRuleSettings().add(rule34);
    entity.getFareRuleSettings().add(rule51);
    entity.getFareRuleSettings().add(rule61);

    Field id = BaseEntity.class.getDeclaredField("id");
    id.setAccessible(true);
    id.set(entity, entityId);
    Field dataVersion = AggregateRoot.class.getDeclaredField("dataVersion");
    dataVersion.setAccessible(true);
    dataVersion.set(entity, Long.valueOf(0));
    return entity;
  }

  void createOutputTestData() {
    OUTPUT.setId(1L);
    OUTPUT.setCode("P000");
    OUTPUT.setName("標準廂全票");
    OUTPUT.setEffDate(LocalDate.now().minusDays(10));
    OUTPUT.setDscDate(LocalDate.now().plusDays(10));

    OUTPUT2.setId(2L);
    OUTPUT2.setCode("P001");
    OUTPUT2.setName("商務廂全票");
    OUTPUT2.setEffDate(LocalDate.now().minusDays(10));
    OUTPUT2.setDscDate(LocalDate.now().plusDays(10));
  }

  void createInputTestData() {
    CREATE_INPUT = genSameSettingInput("P002");
    UPDATE_INPUT = genSameSettingInput("P001");

    // error input(for service)
    EFF_NULL_WHILE_DSC_NOT_INPUT = genSameSettingInput("P001");
    EFF_NULL_WHILE_DSC_NOT_INPUT.setEffSaleDate(null);

    EFF_SALE_NULL_WHILE_DSC_NOT_INPUT = genSameSettingInput("P001");
    EFF_SALE_NULL_WHILE_DSC_NOT_INPUT.setEffSaleDate(null);

    DATE_RANGE_OVERLAP_INPUT = genSameSettingInput("P001");
    DATE_RANGE_OVERLAP_INPUT.setInvalidDateRanges(
        Arrays.asList(new DateRangeDto(LocalDate.of(2018, 2, 8), LocalDate.of(2018, 2, 13)),
            new DateRangeDto(LocalDate.of(2018, 2, 10), LocalDate.of(2018, 3, 13))));

    // invalid input(for controller)
    PromotionInput inputWithWrongCodePattern = genSameSettingInput("YOFC");
    PromotionInput inputWithNullFieldNotAllowed = genSameSettingInput("P001");
    inputWithNullFieldNotAllowed.setModifiable(null);
    PromotionInput inputWithBlankFreq = genSameSettingInput("P001");
    inputWithBlankFreq.setFrequency("");
    PromotionInput inputWithPctLessThan0 = genSameSettingInput("P001");
    inputWithPctLessThan0.setDiscountPct(BigDecimal.valueOf(-1));
    PromotionInput inputWithAmtLessThan0 = genSameSettingInput("P001");
    inputWithAmtLessThan0.setDiscountAmt(BigDecimal.valueOf(-1));
    PromotionInput inputWithPctOver100 = genSameSettingInput("P001");
    inputWithPctOver100.setDiscountPct(BigDecimal.valueOf(101));
    PromotionInput inputWithAmtOver9999 = genSameSettingInput("P001");
    inputWithAmtOver9999.setDiscountAmt(BigDecimal.valueOf(10000));
    PromotionInput inputWithDateRangeOver24 = genSameSettingInput("P001");
    LocalDate startDate = LocalDate.of(2018, 3, 14);
    LocalDate endDate = LocalDate.of(2018, 3, 15);
    List<DateRangeDto> addDateRanges = new ArrayList<>();
    for (int i = 1; i < 26; i++) {
      addDateRanges.add(new DateRangeDto(startDate.plusDays(i * 2), endDate.plusDays(i * 2)));
    }
    inputWithDateRangeOver24.setInvalidDateRanges(addDateRanges);

    INVALID_INPUTS.addAll(Arrays.asList(
        inputWithWrongCodePattern, inputWithNullFieldNotAllowed, inputWithBlankFreq,
        inputWithPctLessThan0, inputWithAmtLessThan0, inputWithPctOver100, inputWithAmtOver9999,
        inputWithDateRangeOver24
    ));
  }

  private PromotionInput genSameSettingInput(String code) {
    PromotionInput input = new PromotionInput();
    input.setDataVersion(0L);
    input.setCode(code);
    input.setName("標準廂全票");
    input.setEffDate(LocalDate.now().minusDays(10));
    input.setDscDate(LocalDate.now().plusDays(10));
    input.setEffSaleDate(LocalDate.now().minusDays(10));
    input.setDscSaleDate(LocalDate.now().plusDays(10));
    input.setType(FarePlanType.PROMOTION);
    input.setContent("ONE-WAY SPECIAL FARE");
    input.setDescription(" RULE21 TEST ");
    input.setModifiable(Boolean.TRUE);
    input.setImmediatePayment(Boolean.FALSE);
    input.setIsSmallGroup(true);
    input.setAllClassesValid(Boolean.FALSE);
    input.setValidClassIds(Arrays.asList(2L, 9L, 14L, 15L, 10L, 6L));
    input.setValidPassengerProfileIds(Arrays.asList(9L, 10L, 11L, 12L, 13L, 14L, 15L));
    input.setEligibleTrainType(EligibleTrainType.TG);
    input.setInvalidDateRanges(
        Arrays.asList(new DateRangeDto(LocalDate.of(2018, 2, 8), LocalDate.of(2018, 2, 13)),
            new DateRangeDto(LocalDate.of(2018, 2, 15), LocalDate.of(2018, 3, 13))));
    input.setFrequency("SMTW--S");
    AdditionalProfileDiscountInput addInput1 = new AdditionalProfileDiscountInput();
    addInput1.setPassengerProfileId(9L);
    addInput1.setPctOff(BigDecimal.valueOf(100));
    input.setAdditionalProfileDiscounts(Collections.singletonList(addInput1));

    return input;
  }
}
