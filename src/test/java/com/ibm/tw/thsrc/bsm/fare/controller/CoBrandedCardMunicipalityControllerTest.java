/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardMunicipalityOutput;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardMunicipalityQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.CoBrandedCardMunicipalityTestData;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@SpringBootTest(classes = {FareApplication.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@AutoConfigureMockMvc
class CoBrandedCardMunicipalityControllerTest {


  private CoBrandedCardMunicipalityTestData TEST_DATA = new CoBrandedCardMunicipalityTestData();
  @Autowired
  private CoBrandedCardMunicipalityController controller;
  @MockBean
  private CoBrandedCardMunicipalityQueryService queryService;

  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any()))
        .thenReturn(new PageImpl<>(TEST_DATA.OUTPUTS));

    List<CoBrandedCardMunicipalityOutput> expected = TEST_DATA.OUTPUTS;

    Page<CoBrandedCardMunicipalityOutput> resultPage = controller.search(new Search());
    Assertions.assertEquals(expected.size(), resultPage.getContent().size());
  }
}
