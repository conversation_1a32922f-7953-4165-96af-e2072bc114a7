/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationOutput;
import com.ibm.tw.thsrc.bsm.core.entity.AggregateRoot;
import com.ibm.tw.thsrc.bsm.core.entity.BaseEntity;
import com.ibm.tw.thsrc.bsm.cronjob.enums.FutureVersionControlStatus;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainDetailDto;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainFareOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainInput;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.FarePlanType;
import com.ibm.tw.thsrc.bsm.fa.enums.FareProjectType;
import com.ibm.tw.thsrc.bsm.fa.enums.ServiceType;
import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePlan;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrain;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainDetail;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainDetailTrain;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainFutureVersionControl;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainTemp;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject;
import com.ibm.tw.thsrc.bsm.fare.entity.OperatingTrainProjection;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan;
import com.ibm.tw.thsrc.bsm.util.BeanUtils;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

public class EligibleTrainTestData {

  public final String BULK_SUCCESS_MSG = "OK";
  public final String BULK_ERROR_MSG = "Err:";
  public final List<String> trainNumbers = Arrays.asList("109", "666", "664", "299");
  public List<EligibleTrain> ENTITIES = new LinkedList<>();
  public EligibleTrain SAME_PROJECT_ENTITY = new EligibleTrain();
  public EligibleTrain SAME_PLAN_ENTITY = new EligibleTrain();
  public List<EligibleTrain> SAME_SALE_DATE_ENTITIES = new ArrayList<>();
  public EligibleTrain CREATED_ENTITY = new EligibleTrain();
  public EligibleTrain UPDATED_ENTITY = new EligibleTrain();
  public EligibleTrainTemp TEMP_DATA = new EligibleTrainTemp();
  public EligibleTrainFutureVersionControl FUTURE_VERSION_CONTROL = new EligibleTrainFutureVersionControl();
  public List<FareProject> PROJECTS = new LinkedList<>();
  public List<PromotionPlan> PROMOTIONS = new LinkedList<>();
  public List<BasicFarePlan> BASIC_PLANS = new LinkedList<>();


  public List<EligibleTrainOutput> OUTPUTS = new LinkedList<>();
  public CollectionModificationOutput BULK_OUTPUT = new CollectionModificationOutput();

  public EligibleTrainInput UPDATE_PROMOTION_INPUT = new EligibleTrainInput();
  public EligibleTrainInput CREATE_WITH_OVERLAP_INPUT = new EligibleTrainInput();
  public EligibleTrainInput CREATE_WITH_DATE_RANGE_WRONG_INPUT = new EligibleTrainInput();
  public EligibleTrainInput UPDATE_WITH_ALL_TRAIN_INPUT = new EligibleTrainInput();
  public EligibleTrainInput NO_FARE_PLAN_INPUT = new EligibleTrainInput();
  public CollectionModificationInput<EligibleTrainInput> BULK_CREATE_BASIC_PLAN_INPUT = new CollectionModificationInput<>();
  public CollectionModificationInput<EligibleTrainInput> BULK_URGENT_CREATE_PROMOTION_INPUT = new CollectionModificationInput<>();
  public CollectionModificationInput<EligibleTrainInput> BULK_DELETE_INPUT = new CollectionModificationInput<>();

  public EligibleTrainTestData() throws Exception {
    setEntities();
    setOutputs();
    setInputs();
  }

  private void setEntities() throws Exception {
    Field id = BaseEntity.class.getDeclaredField("id");
    id.setAccessible(true);
    Field dataVersion = AggregateRoot.class.getDeclaredField("dataVersion");
    dataVersion.setAccessible(true);

    FareProject project1 = new FareProject();
    id.set(project1, 1L);
    dataVersion.set(project1, 0L);
    project1.setCode("Y200");
    project1.setType(FareProjectType.STANDARD);
    project1.setName("測試專案");
    project1.setZhName("測試專案");
    PROJECTS.add(project1);

    FareProject project2 = new FareProject();
    id.set(project2, 2L);
    dataVersion.set(project2, 0L);
    project2.setCode("J000");
    project2.setType(FareProjectType.BUSINESS);
    project2.setName("測試專案2");
    project2.setZhName("測試專案2");
    PROJECTS.add(project2);

    PromotionPlan promotion1 = new PromotionPlan();
    id.set(promotion1, 1L);
    dataVersion.set(promotion1, 0L);
    promotion1.setCode("P222");
    promotion1.setEffDate(LocalDate.now().minusDays(1));
    promotion1.setName("測試1");
    promotion1.setDiscountPct(BigDecimal.valueOf(30));
    promotion1.setType(FarePlanType.PROMOTION);
    promotion1.setSmallGroup(false);
    promotion1.setAllClassesValid(true);
    promotion1.setDscDate(LocalDate.now().plusDays(10));
    PROMOTIONS.add(promotion1);

    PromotionPlan promotion2 = new PromotionPlan();
    id.set(promotion2, 2L);
    dataVersion.set(promotion2, 0L);
    BeanUtils.copyProperties(promotion2, promotion1);
    promotion2.setCode("P333");
    promotion2.setName("測試2");
    PROMOTIONS.add(promotion2);

    BasicFarePlan basicPlan1 = new BasicFarePlan();
    id.set(basicPlan1, 3L);
    dataVersion.set(basicPlan1, 0L);
    basicPlan1.setCode("Y222");
    basicPlan1.setEffDate(LocalDate.now().minusDays(1));
    basicPlan1.setName("測試一般票價方案1");
    basicPlan1.setType(FarePlanType.BASIC_FARE_PLAN);
    basicPlan1.setServiceType(ServiceType.FREE_SEATING);
    basicPlan1.setDscDate(LocalDate.now().plusDays(10));
    BASIC_PLANS.add(basicPlan1);

    Field trainNum = OperatingTrainProjection.class.getDeclaredField("trainNum");
    trainNum.setAccessible(true);

    EligibleTrain entity1 = new EligibleTrain();
    id.set(entity1, 1L);
    dataVersion.set(entity1, 0L);
    entity1.setFarePlan(PROMOTIONS.get(0));
    entity1.setProject(PROJECTS.get(0));
    entity1.setPriority(1);
    entity1.setRemark("測試專案1");
    entity1.setIsAllTrainEligible(Boolean.TRUE);
    entity1.setIsHolidayIneligible(Boolean.TRUE);
    entity1.setEffDate(LocalDate.now().plusDays(1));
    entity1.setDscDate(LocalDate.now().plusDays(10));
    entity1.setEffSaleDate(LocalDate.now().plusDays(1));
    ENTITIES.add(entity1);

    BeanUtils.copyProperties(SAME_PROJECT_ENTITY, entity1);
    id.set(SAME_PROJECT_ENTITY, 10L);
    SAME_PROJECT_ENTITY.setFarePlan(BASIC_PLANS.get(0));

    BeanUtils.copyProperties(SAME_PLAN_ENTITY, entity1);
    id.set(SAME_PLAN_ENTITY, 10L);
    SAME_PLAN_ENTITY.setProject(null);

    EligibleTrain entity2 = new EligibleTrain();
    id.set(entity2, 2L);
    dataVersion.set(entity2, 0L);
    entity2.setFarePlan(BASIC_PLANS.get(0));
    entity2.setProject(PROJECTS.get(0));
    entity2.setPriority(2);
    entity2.setRemark("測試專案2");
    entity2.setIsAllTrainEligible(Boolean.TRUE);
    entity2.setIsHolidayIneligible(Boolean.TRUE);
    entity2.setEffDate(LocalDate.now().plusDays(5));
    entity2.setDscDate(LocalDate.now().plusDays(15));
    entity2.setEffSaleDate(LocalDate.now().plusDays(2));

    EligibleTrainDetail detail1 = new EligibleTrainDetail();
    detail1.setEligibleTrain(entity2);
    detail1.setDayOfWeek(1);

    EligibleTrainDetailTrain detailTrain1 = new EligibleTrainDetailTrain();
    detailTrain1.setEligibleTrainDetail(detail1);
    detailTrain1.setTrainNum(trainNumbers.get(0));
    detail1.getTrains().add(detailTrain1);
    entity2.getDetails().add(detail1);
    ENTITIES.add(entity2);

    EligibleTrain sameSale1 = new EligibleTrain();
    BeanUtils.copyProperties(sameSale1, entity1);
    id.set(sameSale1, 3L);
    sameSale1.setEffSaleDate(LocalDate.now().plusDays(1));
    sameSale1.setEffDate(LocalDate.now().plusDays(3));
    sameSale1.setDscDate(LocalDate.now().plusDays(10));
    SAME_SALE_DATE_ENTITIES.add(sameSale1);

    EligibleTrain sameSale2 = new EligibleTrain();
    BeanUtils.copyProperties(sameSale2, sameSale1);
    id.set(sameSale2, 4L);
    sameSale2.setEffDate(LocalDate.now().plusDays(11));
    sameSale2.setDscDate(LocalDate.now().plusDays(13));
    SAME_SALE_DATE_ENTITIES.add(sameSale1);

    BeanUtils.copyProperties(CREATED_ENTITY, entity1);
    id.set(CREATED_ENTITY, 3L);
    dataVersion.set(CREATED_ENTITY, 0L);
    CREATED_ENTITY.setProject(project2);
    CREATED_ENTITY.setFarePlan(promotion2);
    CREATED_ENTITY.setPriority(1);
    CREATED_ENTITY.setIsAllTrainEligible(Boolean.FALSE);
    CREATED_ENTITY.setIsHolidayIneligible(Boolean.TRUE);
    EligibleTrainDetail detail = new EligibleTrainDetail();
    detail.setEligibleTrain(CREATED_ENTITY);
    detail.setDayOfWeek(1);
    EligibleTrainDetailTrain trainDetail = new EligibleTrainDetailTrain();
    trainDetail.setEligibleTrainDetail(detail);
    trainDetail.setTrainNum(trainNumbers.get(3));
    detail.getTrains().add(trainDetail);
    CREATED_ENTITY.getDetails().add(detail);
    CREATED_ENTITY.setEffDate(ENTITIES.get(1).getEffDate().minusDays(1));
    CREATED_ENTITY.setDscDate(ENTITIES.get(1).getDscDate().minusDays(1));
    CREATED_ENTITY.setEffSaleDate(ENTITIES.get(1).getEffSaleDate().minusDays(1));

    BeanUtils.copyProperties(UPDATED_ENTITY, entity2);
    id.set(UPDATED_ENTITY, 4L);
    dataVersion.set(UPDATED_ENTITY, 0L);
    UPDATED_ENTITY.getDetails().add(detail);
    UPDATED_ENTITY.setEffDate(LocalDate.now().plusDays(2));
    UPDATED_ENTITY.setDscDate(LocalDate.now().plusDays(13));
    UPDATED_ENTITY.setEffSaleDate(LocalDate.now().plusDays(1));

    // cronjob related entities
    id.set(TEMP_DATA, 1L);
    TEMP_DATA.setFarePlanCode(entity1.getFarePlan().getCode());
    TEMP_DATA.setEffSaleDate(entity1.getEffSaleDate());
    TEMP_DATA.setCreateUser("User");
    TEMP_DATA.setCorrelationId("1234-5678-9012-3456");
    TEMP_DATA.setCreateUserEmail("<EMAIL>");

    id.set(FUTURE_VERSION_CONTROL, 1L);
    FUTURE_VERSION_CONTROL.setEligibleTrainTemp(TEMP_DATA);
    FUTURE_VERSION_CONTROL.setStatus(FutureVersionControlStatus.SCHEDULED);
  }

  private void setOutputs() {
    ENTITIES.forEach(e -> {
      EligibleTrainOutput output = new EligibleTrainOutput();
      output.setId(e.getId());
      output.setDataVersion(e.getDataVersion());
      output.setEffDate(e.getEffDate());
      EligibleTrainFareOutput project = new EligibleTrainFareOutput();
      project.setId(1L);
      project.setCode(e.getProject().getCode());
      project.setName(e.getProject().getName());

      EligibleTrainFareOutput farePlan = new EligibleTrainFareOutput();
      farePlan.setId(1L);
      farePlan.setCode(e.getFarePlan().getCode());
      farePlan.setName(e.getFarePlan().getName());
      if (e.getFarePlan().getType().equals(FarePlanType.BASIC_FARE_PLAN)) {
        output.setBasicFarePlan(farePlan);
      } else if (e.getFarePlan().getType().equals(FarePlanType.PROMOTION)) {
        output.setPromotion(farePlan);
      }

      output.setPriority(1);
      output.setRemark("測試專案1");
      output.setIsAllTrainEligible(Boolean.TRUE);
      output.setIsHolidayIneligible(Boolean.TRUE);
      output.setEffDate(e.getEffDate());
      output.setDscDate(e.getDscDate());
      output.setEffSaleDate(e.getEffSaleDate());

      e.getDetails().forEach(d -> {
        EligibleTrainDetailDto detail = new EligibleTrainDetailDto();
        detail.setDayOfWeek(d.getDayOfWeek());
        d.getTrains().forEach(t -> detail.getTrains().add(t.getTrainNum()));
        output.getDetails().add(detail);
      });

      OUTPUTS.add(output);
    });

    BULK_OUTPUT.getCreations()
        .add("ADD,YP50,,2023-11-17,2023-11-23,2023-11-17,,HOLIDAY_NA,,THU,666;SAT,109,OK;");
    BULK_OUTPUT.getDeletions().put(1L,
        "DEL,P302,,2023-01-01,2023-03-31,2023-01-01,,HOLIDAY_NA,User_ABCDEFG,MON,664;TUE,664,Err:Eligible train cannot create or update or delete the effective eligible train.");
  }

  private void setInputs() {
    EligibleTrainInput input = new EligibleTrainInput();
    input.setFareProjectId(2L);
    input.setBasicFarePlanId(3L);
    input.setDataVersion(0L);
    input.setIsAllTrainEligible(Boolean.FALSE);
    input.setIsHolidayIneligible(Boolean.TRUE);
    input.setRemark("bulk");
    EligibleTrainDetailDto detail = new EligibleTrainDetailDto();
    detail.setDayOfWeek(1);
    detail.getTrains().add(trainNumbers.get(3));
    input.getDetails().add(detail);
    input.setEffDate(ENTITIES.get(1).getEffDate());
    input.setDscDate(ENTITIES.get(1).getDscDate());
    input.setEffSaleDate(ENTITIES.get(1).getEffSaleDate());

    BULK_CREATE_BASIC_PLAN_INPUT.getCreations().add(input);
    EligibleTrainInput urgentInput = new EligibleTrainInput();
    BeanUtils.copyProperties(urgentInput, input);
    urgentInput.setBasicFarePlanId(null);
    urgentInput.setPromotionId(PROMOTIONS.get(1).getId());
    urgentInput.setEffSaleDate(LocalDate.now());
    urgentInput.setEffDate(LocalDate.now().plusDays(1));
    urgentInput.setDscDate(LocalDate.now().plusDays(3));
    BULK_URGENT_CREATE_PROMOTION_INPUT.getCreations().add(urgentInput);

    BeanUtils.copyProperties(UPDATE_PROMOTION_INPUT, input);
    UPDATE_PROMOTION_INPUT.setPromotionId(1L);
    UPDATE_PROMOTION_INPUT.setBasicFarePlanId(null);
    UPDATE_PROMOTION_INPUT.setEffDate(ENTITIES.get(1).getEffDate().minusDays(2));
    UPDATE_PROMOTION_INPUT.setDscDate(ENTITIES.get(1).getEffDate().minusDays(1));
    UPDATE_PROMOTION_INPUT.setEffSaleDate(ENTITIES.get(1).getEffSaleDate().minusDays(1));

    UPDATE_WITH_ALL_TRAIN_INPUT.setDataVersion(0L);
    UPDATE_WITH_ALL_TRAIN_INPUT.setBasicFarePlanId(3L);
    UPDATE_WITH_ALL_TRAIN_INPUT.setRemark("no details");
    UPDATE_WITH_ALL_TRAIN_INPUT.setIsAllTrainEligible(Boolean.TRUE);
    UPDATE_WITH_ALL_TRAIN_INPUT.setIsHolidayIneligible(Boolean.TRUE);
    UPDATE_WITH_ALL_TRAIN_INPUT.setEffDate(input.getEffDate());
    UPDATE_WITH_ALL_TRAIN_INPUT.setDscDate(input.getDscDate());
    UPDATE_WITH_ALL_TRAIN_INPUT.setEffSaleDate(input.getEffSaleDate());

    BeanUtils.copyProperties(CREATE_WITH_OVERLAP_INPUT, input);
    CREATE_WITH_OVERLAP_INPUT.setPromotionId(1L);
    CREATE_WITH_OVERLAP_INPUT.setEffDate(LocalDate.now().plusDays(9));
    CREATE_WITH_OVERLAP_INPUT.setDscDate(LocalDate.now().plusDays(12));
    CREATE_WITH_OVERLAP_INPUT.setEffSaleDate(SAME_SALE_DATE_ENTITIES.get(0).getEffSaleDate());

    BeanUtils.copyProperties(CREATE_WITH_DATE_RANGE_WRONG_INPUT, input);
    CREATE_WITH_DATE_RANGE_WRONG_INPUT.setEffDate(input.getDscDate());
    CREATE_WITH_DATE_RANGE_WRONG_INPUT.setDscDate(input.getEffDate());

    BeanUtils.copyProperties(NO_FARE_PLAN_INPUT, input);
    NO_FARE_PLAN_INPUT.setBasicFarePlanId(null);
    NO_FARE_PLAN_INPUT.setPromotionId(null);

    BULK_DELETE_INPUT.getDeletions().add(2L);
  }
}
