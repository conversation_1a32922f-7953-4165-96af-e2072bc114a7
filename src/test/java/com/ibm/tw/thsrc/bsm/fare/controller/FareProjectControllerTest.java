/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.fa.dto.FareProjectInput;
import com.ibm.tw.thsrc.bsm.fa.dto.FareProjectOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.ProjectOverviewOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.ProjectSalesChannelFlagSettingInput;
import com.ibm.tw.thsrc.bsm.fare.service.FareProjectCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.FareProjectQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.FareProjectTestData;
import com.ibm.tw.thsrc.bsm.res.connection.ResCommunicationService;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;


@SpringBootTest(classes = FareApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@ActiveProfiles("test")
class FareProjectControllerTest {


  private static final String API_PATH = "/FareProjects";
  private FareProjectTestData TEST_DATA = new FareProjectTestData();
  @Autowired
  private FareProjectController controller;
  @MockBean
  private FareProjectCommandService commandService;
  @MockBean
  private FareProjectQueryService queryService;

  @MockBean
  private ResCommunicationService resCommunicationService;
  @MockBean
  private HttpHeaderInterceptor interceptor;
  @Autowired
  private MockMvc mockMvc;

  @Autowired
  private ObjectMapper mapper;

  public FareProjectControllerTest() throws Exception {

  }

  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any())).thenReturn(new PageImpl<>(TEST_DATA.OUTPUTS));

    List<FareProjectOutput> expected = TEST_DATA.OUTPUTS;
    Search search = new Search();
    Page<FareProjectOutput> actual = controller.search(search);

    Assertions.assertEquals(expected.size(), actual.getContent().size());
  }

  @Test
  void read() {
    Long testId = Long.MAX_VALUE - 1;
    Mockito.when(queryService.read(testId)).thenReturn(TEST_DATA.OUTPUTS.get(0));

    FareProjectOutput expected = TEST_DATA.OUTPUTS.get(0);
    FareProjectOutput actual = controller.read(testId);

    Assertions.assertEquals(expected.getId(), actual.getId());
    Assertions.assertEquals(expected.getCode(), actual.getCode());
    Assertions.assertEquals(expected.getType(), actual.getType());
  }

  @Test
  void create() {
    Long testId = Long.MAX_VALUE - 1;
    Mockito.when(commandService.create(ArgumentMatchers.any())).thenReturn(testId);
    Mockito.when(queryService.read(testId)).thenReturn(TEST_DATA.OUTPUTS.get(0));

    FareProjectOutput expected = TEST_DATA.OUTPUTS.get(0);
    FareProjectOutput actual = controller.create(TEST_DATA.CREATE_INPUT);

    Assertions.assertEquals(expected.getId(), actual.getId());
    Assertions.assertEquals(expected.getCode(), actual.getCode());
    Assertions.assertEquals(expected.getType(), actual.getType());
  }

  @Test
  void replace() {

    Long testId = Long.MAX_VALUE - 1;
    FareProjectInput input = TEST_DATA.UPDATE_INPUT;
    Mockito.when(queryService.read(ArgumentMatchers.anyLong())).thenReturn(TEST_DATA.OUTPUTS.get(0));
    FareProjectOutput expected = TEST_DATA.OUTPUTS.get(0);
    FareProjectOutput actual = controller.replace(testId, input);

    Assertions.assertEquals(expected.getId(), actual.getId());
    Assertions.assertEquals(expected.getCode(), actual.getCode());
    Assertions.assertEquals(expected.getType(), actual.getType());
  }

  @Test
  void delete() {
    Long testId = Long.MAX_VALUE - 1;
    Assertions.assertDoesNotThrow(() -> controller.delete(testId));
  }

  @Test
  void replaceSalesFlagSettings() {

    Long testId = Long.MAX_VALUE - 1;
    ProjectSalesChannelFlagSettingInput input = TEST_DATA.UPSERT_CHANNEL_FLAG_INPUT;
    Mockito.when(queryService.read(ArgumentMatchers.anyLong())).thenReturn(TEST_DATA.OUTPUTS.get(0));
    FareProjectOutput expected = TEST_DATA.OUTPUTS.get(0);
    FareProjectOutput actual = controller.replaceSalesFlagSettings(testId, input);

    Assertions.assertEquals(expected.getId(), actual.getId());
    Assertions.assertEquals(expected.getCode(), actual.getCode());
    Assertions.assertEquals(expected.getType(), actual.getType());
  }

  @Test
  void deleteSalesFlagSettings() {
    Long testId = Long.MAX_VALUE - 1;
    Assertions.assertDoesNotThrow(() -> controller.deleteSalesFlagSettings(testId));
  }

  @Test
  void validate() {
    Mockito.when(queryService.validate()).thenReturn(TEST_DATA.OVERVIEW_OUTPUTS);
    List<ProjectOverviewOutput> expected = TEST_DATA.OVERVIEW_OUTPUTS;
    List<ProjectOverviewOutput> actual = controller.validate();
    Assertions.assertEquals(expected.size(), actual.size());
  }

  @Test
  void inputValidation() throws Exception {

    MvcResult invalidColor = mockMvc
        .perform(MockMvcRequestBuilders.post(API_PATH)
            .content(mapper.writeValueAsString(FareProjectTestData.INVALID_COLOR_INPUT))
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andReturn();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(),
        invalidColor.getResponse().getStatus());

    MvcResult aiValueEmpty = mockMvc
        .perform(MockMvcRequestBuilders.post(API_PATH)
            .content(mapper.writeValueAsString(FareProjectTestData.INVALID_AI_VALUE_INPUT))
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andReturn();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(),
        aiValueEmpty.getResponse().getStatus());
  }
}