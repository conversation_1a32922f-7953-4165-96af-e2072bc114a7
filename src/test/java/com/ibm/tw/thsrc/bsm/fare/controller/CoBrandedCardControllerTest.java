/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardOutput;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.CoBrandedCardTestData;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest(classes = {FareApplication.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@AutoConfigureMockMvc
class CoBrandedCardControllerTest {

  private static final String API_PATH = "/CoBrandedCards/1";
  private CoBrandedCardTestData TEST_DATA = new CoBrandedCardTestData();
  @Autowired
  private CoBrandedCardController controller;
  @MockBean
  private CoBrandedCardCommandService commandService;
  @MockBean
  private CoBrandedCardQueryService queryService;
  @Autowired
  private MockMvc mockMvc;
  @Autowired
  private ObjectMapper mapper;

  public CoBrandedCardControllerTest() throws Exception {

  }

  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any()))
        .thenReturn(new PageImpl<>(TEST_DATA.COMMON_OUTPUTS));

    List<CoBrandedCardOutput> expected = TEST_DATA.COMMON_OUTPUTS;

    Page<CoBrandedCardOutput> resultPage = controller.search(new Search());
    Assertions.assertEquals(expected.size(), resultPage.getContent().size());
  }

  @Test
  void replace() {
    Long testId = Long.MAX_VALUE - 1;
    Mockito.when(queryService.read(ArgumentMatchers.anyLong())).thenReturn(TEST_DATA.COMMON_OUTPUTS.get(0));

    CoBrandedCardOutput expected = TEST_DATA.COMMON_OUTPUTS.get(0);
    CoBrandedCardOutput actual = controller.replace(testId, TEST_DATA.COMMON_INPUT);

    Assertions.assertEquals(expected.getId(), actual.getId());
    Assertions.assertEquals(expected.getType(), actual.getType());
    Assertions.assertEquals(expected.getIsAutoReloaded(), actual.getIsAutoReloaded());
    Assertions.assertEquals(expected.getDiffStationEntryExitSecsLimit(),
        actual.getDiffStationEntryExitSecsLimit());
    Assertions.assertEquals(expected.getSameStationEntryExitSecsLimit(),
        actual.getSameStationEntryExitSecsLimit());
    Assertions.assertEquals(expected.getTransferBonusMinsLimit(),
        actual.getTransferBonusMinsLimit());
    Assertions.assertEquals(expected.getRoutes().size(), actual.getRoutes().size());
  }

  @Test
  void inputValidation() throws Exception {
    for (CoBrandedCardInput invalidInput : TEST_DATA.INVALID_COMMON_INPUT) {
      MvcResult mvcResult = mockMvc
          .perform(MockMvcRequestBuilders.put(API_PATH)
              .content(mapper.writeValueAsString(invalidInput))
              .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
          .andReturn();

      Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), mvcResult.getResponse().getStatus());
    }
  }
}
