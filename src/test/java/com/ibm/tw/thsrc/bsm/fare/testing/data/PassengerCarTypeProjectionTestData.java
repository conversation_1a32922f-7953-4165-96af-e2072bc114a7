/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.core.entity.BaseEntity;
import com.ibm.tw.thsrc.bsm.fa.dto.PassengerCarTypeProjectionOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerCarTypeProjection;
import com.ibm.tw.thsrc.bsm.sc.enums.CompartmentType;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;


public class PassengerCarTypeProjectionTestData {


  public List<PassengerCarTypeProjection> ENTITIES = new ArrayList<>();

  public PassengerCarTypeProjection ENTITY1 = new PassengerCarTypeProjection();
  public PassengerCarTypeProjection ENTITY2 = new PassengerCarTypeProjection();

  public PassengerCarTypeProjectionOutput OUTPUT = new PassengerCarTypeProjectionOutput();
  public PassengerCarTypeProjectionOutput OUTPUT2 = new PassengerCarTypeProjectionOutput();


  public PassengerCarTypeProjectionTestData() throws Exception {
    createEntityTestData();
    createOutputTestData();
  }

  private void createEntityTestData() throws Exception {
    Field id = BaseEntity.class.getDeclaredField("id");
    id.setAccessible(true);
    id.set(ENTITY1, Long.valueOf(1));

    Field code = PassengerCarTypeProjection.class.getDeclaredField("code");
    code.setAccessible(true);
    code.set(ENTITY1, "Y");

    Field compartmentType = PassengerCarTypeProjection.class.getDeclaredField("compartmentType");
    compartmentType.setAccessible(true);
    compartmentType.set(ENTITY1, CompartmentType.STANDARD);

    Field id2 = BaseEntity.class.getDeclaredField("id");
    id2.setAccessible(true);
    id2.set(ENTITY2, Long.valueOf(2));

    Field code2 = PassengerCarTypeProjection.class.getDeclaredField("code");
    code2.setAccessible(true);
    code2.set(ENTITY2, "J");

    Field compartmentType2 = PassengerCarTypeProjection.class.getDeclaredField("compartmentType");
    compartmentType2.setAccessible(true);
    compartmentType2.set(ENTITY2, CompartmentType.BUSINESS);

    ENTITIES.add(ENTITY1);
    ENTITIES.add(ENTITY2);
  }


  private void createOutputTestData() {
    OUTPUT.setCode("Y");
    OUTPUT.setEnName("STANDARD");
    OUTPUT.setZhName("標準廂");
    OUTPUT.setCompartmentType(CompartmentType.STANDARD);
    OUTPUT.setPassengerCarTypeId(Long.valueOf(2));

    OUTPUT2.setCode("J");
    OUTPUT2.setEnName("BUSINESS");
    OUTPUT2.setZhName("商務廂");
    OUTPUT2.setCompartmentType(CompartmentType.BUSINESS);
    OUTPUT2.setPassengerCarTypeId(Long.valueOf(2));
  }

}
