/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationOutput;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardBatchInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardBatchOutput;
import com.ibm.tw.thsrc.bsm.fare.service.CampaignCreditCardBatchCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.CampaignCreditCardBatchQueryService;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest(classes = {FareApplication.class})
@AutoConfigureMockMvc
@ActiveProfiles("test")
@TestInstance(TestInstance.Lifecycle.PER_METHOD)
class CampaignCreditCardBatchControllerTest {

  @Autowired
  CampaignCreditCardBatchController controller;

  @MockBean
  CampaignCreditCardBatchQueryService queryService;
  @MockBean
  CampaignCreditCardBatchCommandService commandService;

  @Test
  final void testBulk() {

    CollectionModificationOutput messages = new CollectionModificationOutput();
    messages.getCreations().add("SUCCESS");
    messages.getCreations().add("FAILED");
    when(commandService.bulk(any())).thenReturn(messages);

    CollectionModificationOutput result = controller.bulk(new CollectionModificationInput<CampaignCreditCardBatchInput>());
    Assertions.assertEquals(messages.getCreations().size(), result.getCreations().size());
  }

  @Test
  final void testSearch() {
    Long testId = 1L;

    CampaignCreditCardBatchOutput output = new CampaignCreditCardBatchOutput();
    output.setId(testId);

    List<CampaignCreditCardBatchOutput> outputs = new ArrayList<>();
    outputs.add(output);

    when(queryService.search(any())).thenReturn(new PageImpl<>(outputs));

    Page<CampaignCreditCardBatchOutput> resultPage = controller.search(new Search());
    Assertions.assertEquals(outputs.size(), resultPage.getContent().size());
  }

  @Test
  final void testRead() {
    Long testId = 1L;
    CampaignCreditCardBatchOutput output = new CampaignCreditCardBatchOutput();
    output.setId(testId);

    when(queryService.read(any())).thenReturn(output);
    CampaignCreditCardBatchOutput result = controller.read(testId);
    Assertions.assertEquals(testId, result.getId());
  }
}
