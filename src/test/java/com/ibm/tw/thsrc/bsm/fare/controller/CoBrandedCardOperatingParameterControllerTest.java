/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardOperatingParameterOutput;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardOperatingParameterCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardOperatingParameterQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.CoBrandedCardOperatingParameterTestData;
import java.util.Collections;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@SpringBootTest(classes = {FareApplication.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@AutoConfigureMockMvc
class CoBrandedCardOperatingParameterControllerTest {

  private CoBrandedCardOperatingParameterTestData TEST_DATA = new CoBrandedCardOperatingParameterTestData();
  @Autowired
  private CoBrandedCardOperatingParameterController controller;
  @MockBean
  private CoBrandedCardOperatingParameterCommandService commandService;
  @MockBean
  private CoBrandedCardOperatingParameterQueryService queryService;

  public CoBrandedCardOperatingParameterControllerTest() throws Exception {

  }

  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any()))
        .thenReturn(new PageImpl<>(Collections.singletonList(TEST_DATA.OUTPUT)));

    Page<CoBrandedCardOperatingParameterOutput> resultPage = controller.search(new Search());
    Assertions.assertEquals(1, resultPage.getContent().size());
  }

  @Test
  void replace() {
    Mockito.when(queryService.read(ArgumentMatchers.anyLong())).thenReturn(TEST_DATA.OUTPUT);

    CoBrandedCardOperatingParameterOutput expected = TEST_DATA.OUTPUT;
    CoBrandedCardOperatingParameterOutput actual = controller.replace(TEST_DATA.INPUT);

    Assertions.assertEquals(expected.getCoBrandedCardId(), actual.getCoBrandedCardId());
    Assertions.assertEquals(expected.getCoBrandedCardType(), actual.getCoBrandedCardType());
    Assertions.assertEquals(expected.getRoutes().size(), actual.getRoutes().size());
    Assertions.assertEquals(expected.getTransferBonuses().size(),
        actual.getTransferBonuses().size());
    Assertions.assertEquals(expected.getProfileDiscounts().size(),
        actual.getProfileDiscounts().size());
    Assertions.assertEquals(expected.getPenaltyPolicy().getId(), actual.getPenaltyPolicy().getId());
    Assertions.assertEquals(expected.getPenaltyPolicy().getDataVersion(),
        actual.getPenaltyPolicy().getDataVersion());
    Assertions.assertEquals(expected.getPenaltyPolicy().getOvertimePenalty(),
        actual.getPenaltyPolicy().getOvertimePenalty());
    Assertions.assertEquals(expected.getPenaltyPolicy().getSameStationEntryExitPenalty(),
        actual.getPenaltyPolicy().getSameStationEntryExitPenalty());
    Assertions.assertEquals(expected.getPenaltyPolicy().getEntryExitCodeMismatchPenalty(),
        actual.getPenaltyPolicy().getEntryExitCodeMismatchPenalty());
  }
}
