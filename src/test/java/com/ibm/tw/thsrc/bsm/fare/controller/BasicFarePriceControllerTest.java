/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.ResourceNotFoundException;
import com.ibm.tw.thsrc.bsm.fa.dto.BasicFarePriceOutput;
import com.ibm.tw.thsrc.bsm.fare.service.BasicFarePriceCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.BasicFarePriceQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.BasicFarePriceTestData;
import com.ibm.tw.thsrc.bsm.res.connection.ResCommunicationService;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest(classes = FareApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@ActiveProfiles("test")
class BasicFarePriceControllerTest {

  private static final String BASIC_FARE_PRICE_API_PATH = "/BasicFarePrices";
  private BasicFarePriceTestData TEST_DATA = new BasicFarePriceTestData();

  @Autowired
  private BasicFarePriceController controller;

  @MockBean
  private BasicFarePriceCommandService commandService;

  @MockBean
  private BasicFarePriceQueryService queryService;

  @Autowired
  private MockMvc mockMvc;

  @Autowired
  private ObjectMapper mapper;

  @MockBean
  private ResCommunicationService resCommunicationService;

  @MockBean
  private HttpHeaderInterceptor interceptor;


  public BasicFarePriceControllerTest() throws Exception {
  }

  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any()))
        .thenReturn(new PageImpl<>(TEST_DATA.OUTPUTS));

    List<BasicFarePriceOutput> expected = TEST_DATA.OUTPUTS;

    Page<BasicFarePriceOutput> resultPage = controller.search(new Search());
    Assertions.assertEquals(expected.size(), resultPage.getContent().size());
  }

  @Test
  void read() {
    Long testId = TEST_DATA.OUTPUT.getId();
    Mockito.when(queryService.read(testId)).thenReturn(TEST_DATA.OUTPUT);

    BasicFarePriceOutput expected = TEST_DATA.OUTPUT;
    BasicFarePriceOutput result = controller.read(testId);

    Assertions.assertEquals(expected.getId(), result.getId());
    Assertions.assertEquals(expected.getEffDate(), result.getEffDate());
    Assertions.assertEquals(expected.getDscDate(), result.getDscDate());
    Assertions.assertEquals(expected.getFarePlan(), result.getFarePlan());
    Assertions.assertEquals(expected.getBasicFareMatrix().size(),
        result.getBasicFareMatrix().size());

  }

  @Test
  void create() {
    Long testId = TEST_DATA.OUTPUT.getId();

    Mockito.when(commandService.create(ArgumentMatchers.any())).thenReturn(testId);

    Mockito.when(queryService.read(testId)).thenReturn(TEST_DATA.OUTPUT);

    BasicFarePriceOutput result = controller.create(TEST_DATA.VALID_INPUT);
    Assertions.assertEquals(testId, result.getId());
  }

  @Test
  void replace() {
    Mockito.doNothing().when(commandService).update(ArgumentMatchers.any(), ArgumentMatchers.any());

    Mockito.when(queryService.read(ArgumentMatchers.any(Long.class))).thenReturn(TEST_DATA.OUTPUT);

    Long testId = TEST_DATA.OUTPUT.getId();
    BasicFarePriceOutput result = controller.replace(testId, TEST_DATA.VALID_INPUT);
    Assertions.assertEquals(testId, result.getId());

  }

  @Test
  void replaceError() {
    Mockito.doThrow(new ResourceNotFoundException()).when(commandService)
        .update(ArgumentMatchers.any(),
            ArgumentMatchers.any());

    Long testId = Long.valueOf(3);

    Assertions.assertThrows(ResourceNotFoundException.class,
        () -> commandService.update(testId, TEST_DATA.VALID_INPUT));
  }

  @Test
  void delete() {
    Mockito.doNothing().when(commandService).delete(ArgumentMatchers.any());
    commandService.delete(Long.valueOf(0));
    Mockito.verify(commandService, Mockito.times(1)).delete(ArgumentMatchers.any());
  }

  @Test
  void deleteError() {
    Mockito.doThrow(new ResourceNotFoundException()).when(commandService)
        .delete(ArgumentMatchers.any());

    Long testId = Long.valueOf(3);

    Assertions.assertThrows(ResourceNotFoundException.class, () -> commandService.delete(testId));
  }

  @Test
  void modifyCreations() {

    Set<Long> ids = TEST_DATA.OUTPUTS.stream().map(o -> o.getId()).collect(Collectors.toSet());

    Mockito.when(commandService.patch(ArgumentMatchers.any())).thenReturn(ids);

    Set<BasicFarePriceOutput> prices = TEST_DATA.OUTPUTS.stream().collect(Collectors.toSet());

    Mockito.when(queryService.read(ArgumentMatchers.anySet())).thenReturn(prices);

    Set<BasicFarePriceOutput> results = controller.modify(TEST_DATA.PATCH_INPUT_CREATION);

    for (BasicFarePriceOutput expected : TEST_DATA.OUTPUTS) {
      Assertions.assertTrue(results.contains(expected));
    }

  }

  @Test
  void modifyReplacements() {

    Mockito.when(commandService.patch(ArgumentMatchers.any())).thenReturn(new HashSet<>());

    Set<BasicFarePriceOutput> prices = TEST_DATA.OUTPUTS.stream().collect(Collectors.toSet());

    Mockito.when(queryService.read(ArgumentMatchers.anySet())).thenReturn(prices);

    Set<BasicFarePriceOutput> results = controller.modify(TEST_DATA.PATCH_INPUT_REPLACEMENT);

    for (BasicFarePriceOutput expected : TEST_DATA.OUTPUTS) {
      Assertions.assertTrue(results.contains(expected));
    }
  }

  @Test
  void inputValidation() throws Exception {
    MvcResult farePlanIdNull = mockMvc
        .perform(MockMvcRequestBuilders.post(BASIC_FARE_PRICE_API_PATH)
            .content(mapper.writeValueAsString(TEST_DATA.INVALID_INPUT_NULL_PLAN_ID))
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andReturn();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(),
        farePlanIdNull.getResponse().getStatus());

    MvcResult nullDetails = mockMvc
        .perform(MockMvcRequestBuilders.post(BASIC_FARE_PRICE_API_PATH)
            .content(mapper.writeValueAsString(TEST_DATA.INVALID_INPUT_NULL_DETAIL))
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andReturn();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), nullDetails.getResponse().getStatus());

    MvcResult emptyDetails = mockMvc
        .perform(MockMvcRequestBuilders.post(BASIC_FARE_PRICE_API_PATH)
            .content(mapper.writeValueAsString(TEST_DATA.INVALID_INPUT_EMPTY_DETAIL))
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andReturn();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), emptyDetails.getResponse().getStatus());

    MvcResult negativePrice = mockMvc
        .perform(MockMvcRequestBuilders.post(BASIC_FARE_PRICE_API_PATH)
            .content(mapper.writeValueAsString(TEST_DATA.INVALID_INPUT_NEG_PRICE))
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andReturn();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(),
        negativePrice.getResponse().getStatus());

    MvcResult nullPrice = mockMvc
        .perform(MockMvcRequestBuilders.post(BASIC_FARE_PRICE_API_PATH)
            .content(mapper.writeValueAsString(TEST_DATA.INVALID_INPUT_NULL_PRICE))
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andReturn();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), nullPrice.getResponse().getStatus());

    MvcResult nullStationId = mockMvc
        .perform(MockMvcRequestBuilders.post(BASIC_FARE_PRICE_API_PATH)
            .content(mapper.writeValueAsString(TEST_DATA.INVALID_INPUT_NULL_STATION_ID))
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andReturn();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(),
        nullStationId.getResponse().getStatus());

  }


}
