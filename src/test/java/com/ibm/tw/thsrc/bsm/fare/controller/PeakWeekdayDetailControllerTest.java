/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.PeakWeekdayOutput;
import com.ibm.tw.thsrc.bsm.fare.service.PeakWeekdayDetailQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.PeakWeekdayTestData;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

@SpringBootTest(classes = {FareApplication.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@AutoConfigureMockMvc
class PeakWeekdayDetailControllerTest {

  private static final PeakWeekdayTestData TEST_DATA = new PeakWeekdayTestData();
  @Autowired
  private PeakWeekdayDetailController controller;
  @MockBean
  private PeakWeekdayDetailQueryService queryService;
  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any()))
        .thenReturn(new PageImpl<>(TEST_DATA.RECENT_OUTPUTS));

    List<PeakWeekdayOutput> expected = TEST_DATA.RECENT_OUTPUTS;
    Search search = new Search();
    Page<PeakWeekdayOutput> actual = controller.search(search);

    Assertions.assertEquals(expected.size(), actual.getContent().size());
  }
}
