/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.fa.dto.PeakCalendarDateInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PeakCalendarDateOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.PeakCalendarDate;
import java.time.LocalDate;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.IntStream;

public class PeakCalendarDateTestData {

  public final LocalDate DATE1 = LocalDate.now();
  public final LocalDate DATE2 = LocalDate.now().plusDays(1);
  public final LocalDate DATE3 = LocalDate.now().plusDays(2);
  public final LocalDate DATE4 = LocalDate.now().plusDays(3);
  public PeakWeekdayTestData PEAK_WEEKDAY_DATA = new PeakWeekdayTestData();
  public List<PeakCalendarDate> ENTITIES = new LinkedList<>();
  public List<PeakCalendarDateOutput> RECENT_OUTPUTS = new LinkedList<>();
  public List<PeakCalendarDateInput> UPDATE_RECENT_INPUTS = new LinkedList<>();
  public List<PeakCalendarDateInput> CREATE_FUTURE_INPUTS = new LinkedList<>();
  public List<PeakCalendarDateInput> INVALID_INPUTS = new LinkedList<>();
  public PeakCalendarDateInput INPUT_WITH_DUPLICATE_EFF_DATE;

  public PeakCalendarDateTestData() {
    setEntities();
    setOutput();
    setInput(true);
  }

  public void resetInput() {
    setInput(false);
  }

  private void setEntities() {

    IntStream.range(1, 7).forEach(e -> {
      PeakCalendarDate calendar = new PeakCalendarDate();
      calendar.setCalendarDate(LocalDate.now().plusDays(e));
      calendar.setPeakTime(PEAK_WEEKDAY_DATA.TIME_ENTITIES.get(0));
      calendar.setPeakWeekday(null);
      calendar.setEffDate(DATE1);

      ENTITIES.add(calendar);
    });

    IntStream.range(1, 7).forEach(e -> {
      PeakCalendarDate calendar = new PeakCalendarDate();
      calendar.setCalendarDate(LocalDate.now().plusDays(e));
      calendar.setPeakTime(PEAK_WEEKDAY_DATA.TIME_ENTITIES.get(1));
      calendar.setPeakWeekday(null);
      calendar.setEffDate(DATE2);

      ENTITIES.add(calendar);
    });
  }

  private void setOutput() {
    IntStream.range(1, 7).forEach(e -> {
      PeakCalendarDateOutput calendar = new PeakCalendarDateOutput();
      calendar.setId(e);
      calendar.setEffDate(DATE1);
      calendar.setCalendarDate(LocalDate.now().plusDays(e));
      calendar.setPeakWeekday(Boolean.FALSE);
      calendar.setPeakTimeId(1L);

      RECENT_OUTPUTS.add(calendar);
    });
  }

  private void setInput(boolean isInit) {
    if (!isInit) {
      UPDATE_RECENT_INPUTS.clear();
      CREATE_FUTURE_INPUTS.clear();
      INVALID_INPUTS.clear();
    }

    IntStream.range(1, 7)
        .forEach(e -> UPDATE_RECENT_INPUTS.add(getBasicInput(e, 0, DATE1, isInit)));

    IntStream.range(1, 7).forEach(e -> {
      PeakCalendarDateInput input = getBasicInput(e, 2, DATE4, isInit);
      input.setId(null);
      CREATE_FUTURE_INPUTS.add(input);
    });

    // invalid for controller
    PeakCalendarDateInput inputWithNull = getBasicInput(1, 0, DATE1, isInit);
    inputWithNull.setEffDate(null);
    INVALID_INPUTS.add(inputWithNull);

    // invalid for service
    INPUT_WITH_DUPLICATE_EFF_DATE = getBasicInput(1, 0, DATE2, isInit);
  }

  // e start with 1, dataVersion start with 0
  private PeakCalendarDateInput getBasicInput(int e, Integer dateVersion, LocalDate effDate,
      boolean isInit) {
    PeakCalendarDateInput calendar = new PeakCalendarDateInput();
    if (isInit) {
      calendar.setId(dateVersion * 2L + e);

    } else {
      if (dateVersion > 1) {
        calendar.setId(null);
      } else {
        calendar.setId(ENTITIES.get(dateVersion * 2 + e - 1).getId());
      }
    }
    calendar.setDataVersion(0L);
    calendar.setEffDate(effDate);
    calendar.setCalendarDate(LocalDate.now().plusDays(e));
    calendar.setPeakTimeId(null);
    calendar.setPeakWeekday(Boolean.TRUE);

    return calendar;
  }
}
