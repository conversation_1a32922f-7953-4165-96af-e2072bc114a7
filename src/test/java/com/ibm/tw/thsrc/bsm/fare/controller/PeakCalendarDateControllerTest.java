/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.PeakCalendarDateInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PeakCalendarDateOutput;
import com.ibm.tw.thsrc.bsm.fare.service.PeakCalendarDateCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.PeakCalendarDateQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.PeakCalendarDateTestData;
import java.util.HashSet;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest(classes = {FareApplication.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@AutoConfigureMockMvc
class PeakCalendarDateControllerTest {

  private static final String API_PATH = "/PeakCalenderDates/VersionUpserts";
  private static final PeakCalendarDateTestData TEST_DATA = new PeakCalendarDateTestData();
  @Autowired
  private PeakCalendarDateController controller;
  @MockBean
  private PeakCalendarDateCommandService commandService;
  @MockBean
  private PeakCalendarDateQueryService queryService;
  @Autowired
  private MockMvc mockMvc;
  @Autowired
  private ObjectMapper mapper;

  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any()))
        .thenReturn(new PageImpl<>(TEST_DATA.RECENT_OUTPUTS));

    List<PeakCalendarDateOutput> expected = TEST_DATA.RECENT_OUTPUTS;
    Search search = new Search();
    Page<PeakCalendarDateOutput> actual = controller.search(search);

    Assertions.assertEquals(expected.size(), actual.getContent().size());
  }

  @Test
  void upsert() {
    Mockito.when(commandService.upsert(ArgumentMatchers.any())).thenReturn(new HashSet<>());
    Mockito.when(queryService.read(ArgumentMatchers.anySet())).thenReturn(TEST_DATA.RECENT_OUTPUTS);

    List<PeakCalendarDateOutput> expected = TEST_DATA.RECENT_OUTPUTS;
    List<PeakCalendarDateOutput> actual = controller.upsert(TEST_DATA.UPDATE_RECENT_INPUTS);

    Assertions.assertEquals(expected.size(), actual.size());
  }

  @Test
  void inputValidation() throws Exception {
    for (PeakCalendarDateInput invalidInput : TEST_DATA.INVALID_INPUTS) {
      MvcResult mvcResult = mockMvc
          .perform(MockMvcRequestBuilders.post(API_PATH)
              .content(mapper.writeValueAsString(invalidInput))
              .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
          .andReturn();

      Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), mvcResult.getResponse().getStatus());
    }
  }

  @Test
  void dataCreation() {
    Assertions.assertDoesNotThrow(() -> controller.dataCreation());
  }
}
