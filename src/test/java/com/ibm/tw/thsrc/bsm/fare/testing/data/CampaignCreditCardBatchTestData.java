/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardBatchInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardBatchOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardInfoInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardInfoOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.CreditCardCampaignType;
import com.ibm.tw.thsrc.bsm.fare.entity.CampaignCreditCardBatch;
import com.ibm.tw.thsrc.bsm.fare.entity.CampaignCreditCardInfo;
import com.ibm.tw.thsrc.bsm.fare.entity.CreditCardWeekdayPromotion;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.IntStream;

public class CampaignCreditCardBatchTestData {

  public List<Long> IDS = Arrays.asList(1L, 2L, 3L, 4L, 5L);

  public List<CampaignCreditCardBatch> ENTITIES = new ArrayList<>();

  public List<CampaignCreditCardBatchInput> INPUTS = new ArrayList<>();
  public List<CampaignCreditCardBatchOutput> OUTPUS = new ArrayList<>();

  public List<String> bizbankNames = Arrays.asList("合庫", "彰化銀行", "台灣銀行", "玉山銀行",
      "台新銀行");
  public List<String> bizCardNos = Arrays.asList("111111", "222222", "333333", "444444", "555555");

  public List<String> stdbankNames = Arrays.asList("兆豐商銀", "星展銀行");
  public List<String> stdCardNos = Arrays.asList("123456", "234567");
  public List<String> promotionCodes =
      Arrays.asList("P101", "P101", "P101", "P101", "P101", "P101", "P101");

  public PromotionPlan PROMOTION = new PromotionPlan();
  public List<CampaignCreditCardBatch> QUERY_ENTITIES = new ArrayList<>();

  public CampaignCreditCardBatchTestData() {
    setBusinessEntity();
    setInput();
    setOutput();
  }

  public void setBusinessEntity() {

    // 商務升等
    IntStream.range(0, bizbankNames.size()).forEach(e -> {
      CampaignCreditCardBatch batch = new CampaignCreditCardBatch();
      batch.setBankName(bizbankNames.get(e));
      batch.setImportDate(LocalDate.of(2023, 1, 1));
      List<CampaignCreditCardInfo> infos = new ArrayList<>();

      IntStream.range(0, bizCardNos.size()).forEach(c -> {
        CampaignCreditCardInfo info = new CampaignCreditCardInfo();
        info.setCreditCardCampaignType(CreditCardCampaignType.BUSINESS_UPGRADE);
        info.setCardNo(bizCardNos.get(c));
        info.setEffDate(LocalDate.of(2023, 1, 1));
        info.setDscDate(LocalDate.now().plusDays(1));
        info.setEffSaleDate(LocalDate.of(2023, 1, 1));
        info.setDscSaleDate(LocalDate.now().plusDays(1));
        info.setBatch(batch);

        List<CreditCardWeekdayPromotion> promotions = new ArrayList<>();
        IntStream.range(1, 7).forEach(day -> {
          CreditCardWeekdayPromotion promo = new CreditCardWeekdayPromotion();
          PromotionPlan plan = new PromotionPlan();
          plan.setCode(promotionCodes.get(day - 1));
          plan.setName(promotionCodes.get(day - 1));
          promo.setDayOfWeek(day);
          promo.setPromotionPlan(plan);
          promo.setCreditCardInfo(info);
          promotions.add(promo);
        });
        info.setPromotions(promotions);

        infos.add(info);
      });
      batch.setCardNos(infos);
      ENTITIES.add(batch);
    });
    // 一般卡友
    IntStream.range(0, stdbankNames.size()).forEach(e -> {
      CampaignCreditCardBatch batch = new CampaignCreditCardBatch();
      batch.setBankName(stdbankNames.get(e));
      batch.setImportDate(LocalDate.of(2023, 1, 1));

      List<CampaignCreditCardInfo> infos = new ArrayList<>();
      IntStream.range(0, stdCardNos.size()).forEach(c -> {
        CampaignCreditCardInfo info = new CampaignCreditCardInfo();
        info.setCreditCardCampaignType(CreditCardCampaignType.STANDARD_PROMOTION);
        info.setCardNo(stdCardNos.get(c));
        info.setEffDate(LocalDate.of(2023, 1, 1));
        info.setDscDate(LocalDate.now().plusDays(1));
        info.setEffSaleDate(LocalDate.of(2023, 1, 1));
        info.setDscSaleDate(LocalDate.now().plusDays(1));
        info.setBatch(batch);

        List<CreditCardWeekdayPromotion> promotions = new ArrayList<>();
        IntStream.range(1, 7).forEach(day -> {
          CreditCardWeekdayPromotion promo = new CreditCardWeekdayPromotion();
          PromotionPlan plan = new PromotionPlan();
          plan.setCode(promotionCodes.get(day - 1));
          plan.setName(promotionCodes.get(day - 1));
          promo.setDayOfWeek(day);
          promo.setPromotionPlan(plan);
          promo.setCreditCardInfo(info);
          promotions.add(promo);
        });
        info.setPromotions(promotions);

        infos.add(info);
      });
      batch.setCardNos(infos);
      ENTITIES.add(batch);
    });

    // for real query
    // 一般卡友
    PROMOTION.setCode(promotionCodes.get(0));
    PROMOTION.setName(promotionCodes.get(0));
    IntStream.range(0, stdbankNames.size()).forEach(e -> {
      CampaignCreditCardBatch batch = new CampaignCreditCardBatch();
      batch.setBankName(stdbankNames.get(e));
      batch.setImportDate(LocalDate.of(2023, 1, 1));

      List<CampaignCreditCardInfo> infos = new ArrayList<>();
      IntStream.range(0, stdCardNos.size()).forEach(c -> {
        CampaignCreditCardInfo info = new CampaignCreditCardInfo();
        info.setCreditCardCampaignType(CreditCardCampaignType.STANDARD_PROMOTION);
        info.setCardNo(stdCardNos.get(c));
        info.setEffDate(LocalDate.of(2023, 1, 1));
        info.setDscDate(LocalDate.now().plusDays(1));
        info.setEffSaleDate(LocalDate.of(2023, 1, 1));
        info.setDscSaleDate(LocalDate.now().plusDays(1));
        info.setBatch(batch);

        List<CreditCardWeekdayPromotion> promotions = new ArrayList<>();
        IntStream.range(1, 7).forEach(day -> {
          CreditCardWeekdayPromotion promo = new CreditCardWeekdayPromotion();
          promo.setDayOfWeek(day);
          promo.setPromotionPlan(PROMOTION);
          promo.setCreditCardInfo(info);
          promotions.add(promo);
        });
        info.setPromotions(promotions);

        infos.add(info);
      });
      batch.setCardNos(infos);
      QUERY_ENTITIES.add(batch);
    });
  }

  public void setInput() {
    IntStream.range(0, bizbankNames.size()).forEach(i -> {
      CampaignCreditCardBatchInput input = new CampaignCreditCardBatchInput();
      input.setBankName(bizbankNames.get(i));
      input.setImportDate(LocalDate.of(2023, 1, 1));

      List<CampaignCreditCardInfoInput> infos = new ArrayList<>();

      IntStream.range(0, bizCardNos.size()).forEach(c -> {
        CampaignCreditCardInfoInput info = new CampaignCreditCardInfoInput();
        info.setCreditCardCampaignType(CreditCardCampaignType.BUSINESS_UPGRADE);
        info.setCardNo(bizCardNos.get(c));
        info.setEffDate(LocalDate.of(2023, 1, 1));
        info.setDscDate(LocalDate.now().plusDays(1));
        info.setEffSaleDate(LocalDate.of(2023, 1, 1));
        info.setDscSaleDate(LocalDate.now().plusDays(1));
        infos.add(info);
      });
      input.setCampaignCreditCardInfos(infos);
      INPUTS.add(input);
    });
  }

  public void setOutput() {
    IntStream.range(0, bizbankNames.size()).forEach(i -> {
      CampaignCreditCardBatchOutput output = new CampaignCreditCardBatchOutput();
      output.setId(Long.valueOf(i) + 1L);
      output.setBankName(bizbankNames.get(i));
      output.setImportDate(LocalDate.of(2023, 1, 1));

      List<CampaignCreditCardInfoOutput> infos = new ArrayList<>();

      IntStream.range(0, bizCardNos.size()).forEach(c -> {
        CampaignCreditCardInfoOutput info = new CampaignCreditCardInfoOutput();
        info.setId(Long.valueOf(c) + 1L);
        info.setCreditCardCampaignType(CreditCardCampaignType.BUSINESS_UPGRADE);
        info.setCardNo(bizCardNos.get(c));
        info.setEffDate(LocalDate.of(2023, 1, 1));
        info.setDscDate(LocalDate.now().plusDays(1));
        info.setEffSaleDate(LocalDate.of(2023, 1, 1));
        info.setDscSaleDate(LocalDate.now().plusDays(1));
        infos.add(info);
      });
      output.setCampaignCreditCardInfos(infos);
      OUTPUS.add(output);
    });
  }
}
