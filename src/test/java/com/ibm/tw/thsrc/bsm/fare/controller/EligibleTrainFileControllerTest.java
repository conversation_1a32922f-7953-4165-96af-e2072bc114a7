/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainFileInput;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainFileOutput;
import com.ibm.tw.thsrc.bsm.fare.service.EligibleTrainFileCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.EligibleTrainFileQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.EligibleTrainFileTestData;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest(classes = {FareApplication.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@AutoConfigureMockMvc
class EligibleTrainFileControllerTest {

  private final String API_PATH = "/EligibleTrainFiles";

  private EligibleTrainFileTestData TEST_DATA = new EligibleTrainFileTestData();
  @Autowired
  private EligibleTrainFileController controller;
  @MockBean
  private EligibleTrainFileCommandService commandService;
  @MockBean
  private EligibleTrainFileQueryService queryService;

  @Autowired
  private MockMvc mockMvc;
  @Autowired
  private ObjectMapper mapper;

  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any())).thenReturn(new PageImpl<>(TEST_DATA.OUTPUTS));

    List<EligibleTrainFileOutput> expected = TEST_DATA.OUTPUTS;

    Page<EligibleTrainFileOutput> resultPage = controller.search(new Search());
    Assertions.assertEquals(expected.size(), resultPage.getContent().size());
  }

  @Test
  void create() {
    Mockito.when(queryService.read(ArgumentMatchers.anyLong())).thenReturn(TEST_DATA.OUTPUTS.get(0));
    EligibleTrainFileOutput expected = TEST_DATA.OUTPUTS.get(0);
    EligibleTrainFileOutput actual = controller.create(TEST_DATA.CREATE_INPUT);
    Assertions.assertEquals(expected.getId(), actual.getId());
    Assertions.assertEquals(expected.getFileName(), actual.getFileName());
    Assertions.assertEquals(expected.getFilePath(), actual.getFilePath());
    Assertions.assertEquals(expected.getUploadEmployeeId(), actual.getUploadEmployeeId());
    Assertions.assertEquals(expected.getUploadTimestamp(), actual.getUploadTimestamp());
    Assertions.assertEquals(expected.getLatestDownloadEmployeeId(),
        actual.getLatestDownloadEmployeeId());
    Assertions.assertEquals(expected.getLatestDownloadTimestamp(),
        actual.getLatestDownloadTimestamp());
  }

  @Test
  void replace() {
    Long testId = TEST_DATA.OUTPUTS.get(1).getId();
    Mockito.when(queryService.read(ArgumentMatchers.anyLong())).thenReturn(TEST_DATA.OUTPUTS.get(1));
    EligibleTrainFileOutput expected = TEST_DATA.OUTPUTS.get(1);
    EligibleTrainFileOutput actual = controller.replace(testId, TEST_DATA.UPDATE_INPUT);
    Assertions.assertEquals(expected.getId(), actual.getId());
    Assertions.assertEquals(expected.getFileName(), actual.getFileName());
    Assertions.assertEquals(expected.getFilePath(), actual.getFilePath());
    Assertions.assertEquals(expected.getUploadEmployeeId(), actual.getUploadEmployeeId());
    Assertions.assertEquals(expected.getUploadTimestamp(), actual.getUploadTimestamp());
    Assertions.assertEquals(expected.getLatestDownloadEmployeeId(),
        actual.getLatestDownloadEmployeeId());
    Assertions.assertEquals(expected.getLatestDownloadTimestamp(),
        actual.getLatestDownloadTimestamp());
  }

  @Test
  void inputValidation() throws Exception {
    EligibleTrainFileInput input = TEST_DATA.INPUT_WITH_NULL;
    MvcResult mvcResult = mockMvc
        .perform(MockMvcRequestBuilders.post(API_PATH)
            .content(mapper.writeValueAsString(input))
            .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
        .andReturn();

    Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), mvcResult.getResponse().getStatus());
  }
}
