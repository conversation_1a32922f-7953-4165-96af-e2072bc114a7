/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.fa.dto.PeriodicTicketInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PeriodicTicketOptionDto;
import com.ibm.tw.thsrc.bsm.fa.dto.PeriodicTicketOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.IssueTravelCardExpiryType;
import com.ibm.tw.thsrc.bsm.fare.entity.PeriodicTicket;
import com.ibm.tw.thsrc.bsm.fare.entity.PeriodicTicketOption;
import java.math.BigDecimal;
import java.util.Arrays;

public class PeriodicTicketTestData {

  public final PeriodicTicket INVALID_ENTITY = new PeriodicTicket();
  public final PeriodicTicketInput INPUT = new PeriodicTicketInput();
  public final PeriodicTicketOutput OUTPUT = new PeriodicTicketOutput();

  public PeriodicTicketTestData() {
    createInputTestData();
    createInput();
    createOutput();
  }

  public void createInputTestData() {

    PeriodicTicketOption option = new PeriodicTicketOption();
    option.setValidDays(Integer.valueOf(999));
    option.setDiscountPct(BigDecimal.valueOf(66));
    option.setIsAppSale(Boolean.TRUE);
    option.setAppEffSaleDate(null);
    option.setDiscountPct(null);

    INVALID_ENTITY.setExpiryType(IssueTravelCardExpiryType.BY_ISSUE_DATE);
    INVALID_ENTITY.setRefundFeeAmt(BigDecimal.valueOf(*********));
    INVALID_ENTITY.setPeriodicTicketOptions(Arrays.asList(option));
  }

  public void createInput() {

    PeriodicTicketOptionDto option = new PeriodicTicketOptionDto();
    option.setValidDays(Integer.valueOf(999));
    option.setDiscountPct(BigDecimal.valueOf(66));
    option.setIsAppSale(Boolean.TRUE);
    option.setAppEffSaleDate(null);
    option.setDiscountPct(null);

    INPUT.setId(1L);
    INPUT.setDataVersion(1L);
    INPUT.setExpiryType(IssueTravelCardExpiryType.BY_ISSUE_DATE);
    INPUT.setRefundFeeAmt(BigDecimal.valueOf(*********));
    INPUT.setOptions(Arrays.asList(option));
  }

  public void createOutput() {

    PeriodicTicketOptionDto option = new PeriodicTicketOptionDto();
    option.setValidDays(Integer.valueOf(999));
    option.setDiscountPct(BigDecimal.valueOf(66));
    option.setIsAppSale(Boolean.TRUE);
    option.setAppEffSaleDate(null);
    option.setDiscountPct(null);

    OUTPUT.setId(1L);
    OUTPUT.setDataVersion(1L);
    OUTPUT.setExpiryType(IssueTravelCardExpiryType.BY_ISSUE_DATE);
    OUTPUT.setRefundFeeAmt(BigDecimal.valueOf(*********));
    OUTPUT.setOptions(Arrays.asList(option));
  }
}
