/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.test.support;

import java.sql.Connection;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.jdbc.datasource.init.ScriptUtils;
import org.springframework.stereotype.Component;

@Component
public class TestDataLoader {
  @Autowired
  DataSource dataSource;

  public void loadDataFromClassPath(String fileName) {
    loadData(new ClassPathResource(fileName));
  }

  public void loadData(Resource resource) {
    try (Connection connection = dataSource.getConnection()) {
      ScriptUtils.executeSqlScript(connection, resource);
    } catch (Exception e) {
      throw new RuntimeException("unable to load data", e);
    }
  }
}

