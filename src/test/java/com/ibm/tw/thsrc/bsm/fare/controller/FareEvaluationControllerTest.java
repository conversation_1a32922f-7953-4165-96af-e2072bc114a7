/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.exception.ResErrorCode;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableException;
import com.ibm.tw.thsrc.bsm.fa.dto.FareEvaluationInput;
import com.ibm.tw.thsrc.bsm.fa.dto.FareEvaluationOutput;
import com.ibm.tw.thsrc.bsm.fare.service.FareEvaluationQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.FareEvaluationTestData;
import java.util.List;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest(classes = {FareApplication.class})
@AutoConfigureMockMvc
@ActiveProfiles("test")
class FareEvaluationControllerTest {

  @Autowired
  private FareEvaluationController controller;
  @Autowired
  private MockMvc mockMvc;
  @Autowired
  private ObjectMapper mapper;

  @MockBean
  private FareEvaluationQueryService queryService;

  private static final FareEvaluationTestData TEST_DATA = new FareEvaluationTestData();
  private static final String API_PATH = "/FareEvaluations";

  @AfterEach
  void reset() {
    Mockito.reset(queryService);
  }

  @Test
  void evaluate() throws Exception {

    List<FareEvaluationOutput> expected = TEST_DATA.OUTPUTS;
    Mockito.when(
        queryService.evaluate(TEST_DATA.INPUT)
    ).thenReturn(TEST_DATA.OUTPUTS);

    mockMvc.perform(MockMvcRequestBuilders
            .post(API_PATH)
            .content(mapper.writeValueAsString(TEST_DATA.INPUT))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());

    List<FareEvaluationOutput> actual = controller.evaluate(TEST_DATA.INPUT);

    Assertions.assertEquals(expected.size(), actual.size());
    for (int i = 0; i < expected.size(); i++) {

      Assertions.assertEquals(expected.get(i).getUnitPrice(), actual.get(i).getUnitPrice());
      Assertions.assertEquals(expected.get(i).getStationPair().getId(),
          actual.get(i).getStationPair().getId());
      Assertions.assertEquals(expected.get(i).getStationPair().getDepartureStationCode(),
          actual.get(i).getStationPair().getDepartureStationCode());
      Assertions.assertEquals(expected.get(i).getStationPair().getArrivalStationCode(),
          actual.get(i).getStationPair().getArrivalStationCode());
      Assertions.assertEquals(expected.get(i).getStationPair().getDepartureStationName(),
          actual.get(i).getStationPair().getDepartureStationName());
      Assertions.assertEquals(expected.get(i).getStationPair().getArrivalStationName(),
          actual.get(i).getStationPair().getArrivalStationName());
    }
  }

  @Test
  void inputValidation() throws Exception {
    for (FareEvaluationInput errInput : TEST_DATA.INVALID_INPUTS) {
      mockMvc.perform(MockMvcRequestBuilders
              .post(API_PATH)
              .content(mapper.writeValueAsString(errInput))
              .contentType(MediaType.APPLICATION_JSON)
              .accept(MediaType.APPLICATION_JSON))
          .andExpect(status().isBadRequest());
    }
  }

  @Test
  void methodNotAllowed() throws Exception {
    mockMvc.perform(MockMvcRequestBuilders
            .get(API_PATH))
        .andExpect(status().isMethodNotAllowed());

    mockMvc.perform(MockMvcRequestBuilders
            .put(API_PATH)
            .content(mapper.writeValueAsString(TEST_DATA.INPUT))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isMethodNotAllowed());

    mockMvc.perform(MockMvcRequestBuilders
            .delete(API_PATH))
        .andExpect(status().isMethodNotAllowed());
  }

  @Test
  void unprocessableEntity() throws Exception {
    Mockito.doThrow(
        new UnprocessableException(
            ResErrorCode.RES_CONNECTION_PAYLOAD_ERROR.getMessage(), "")
    ).when(queryService).evaluate(ArgumentMatchers.any());

    mockMvc.perform(MockMvcRequestBuilders
            .post(API_PATH)
            .content(mapper.writeValueAsString(TEST_DATA.INPUT))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnprocessableEntity())
        .andReturn();

    Mockito.doThrow(
        new UnprocessableException(
            ResErrorCode.RES_FARE_EVALUATION_FARE_PLAN_AND_PROFILE_NOT_EXIST.getMessage(), "")
    ).when(queryService).evaluate(ArgumentMatchers.any());

    mockMvc.perform(MockMvcRequestBuilders
            .post(API_PATH)
            .content(mapper.writeValueAsString(TEST_DATA.INPUT))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnprocessableEntity())
        .andReturn();

    Mockito.doThrow(
        new UnprocessableException(
            ResErrorCode.RES_FARE_EVALUATION_PROFILE_NOT_EXIST.getMessage(), "")
    ).when(queryService).evaluate(ArgumentMatchers.any());

    mockMvc.perform(MockMvcRequestBuilders
            .post(API_PATH)
            .content(mapper.writeValueAsString(TEST_DATA.INPUT))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnprocessableEntity())
        .andReturn();
  }
}
