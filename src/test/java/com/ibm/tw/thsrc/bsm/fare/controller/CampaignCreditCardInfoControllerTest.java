/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardInfoOutput;
import com.ibm.tw.thsrc.bsm.fare.service.CampaignCreditCardInfoQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.CampaignCreditCardInfoTestData;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

@SpringBootTest(classes = FareApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
@ActiveProfiles("test")
class CampaignCreditCardInfoControllerTest {

  private static final String API_PATH = "/CampaignCreditCardInfo";
  private CampaignCreditCardInfoTestData TEST_DATA = new CampaignCreditCardInfoTestData();

  @Autowired
  private CampaignCreditCardInfoController controller;

  @MockBean
  private CampaignCreditCardInfoQueryService queryService;

  @Autowired
  private MockMvc mockMvc;

  @Autowired
  private ObjectMapper mapper;

  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any()))
        .thenReturn(new PageImpl<>(TEST_DATA.OUTPUTS));

    List<CampaignCreditCardInfoOutput> expected = TEST_DATA.OUTPUTS;

    Page<CampaignCreditCardInfoOutput> resultPage = controller.search(new Search());
    Assertions.assertEquals(expected.size(), resultPage.getContent().size());
  }
}
