package com.ibm.tw.thsrc.bsm.fare.stepservice;

import static org.junit.jupiter.api.Assertions.*;

import com.ibm.tw.thsrc.bsm.cronjob.value.StepResult;
import com.ibm.tw.thsrc.bsm.cronjob.value.TaskExecution;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainFutureVersionControl;
import com.ibm.tw.thsrc.bsm.fare.entity.EligibleTrainTemp;
import com.ibm.tw.thsrc.bsm.fare.repository.EligibleTrainFutureVersionControlRepository;
import com.ibm.tw.thsrc.bsm.fare.service.EligibleTrainCommandService;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class EligibleTrainStepServiceTest {

  EligibleTrainStepService service;

  @Mock
  EligibleTrainCommandService commandService;
  @Mock
  EligibleTrainFutureVersionControlRepository repo;


  @BeforeEach
  void init() throws IllegalAccessException {
    MockitoAnnotations.openMocks(this);
    service = new EligibleTrainStepService();

    FieldUtils.writeField(service, "commandService", commandService, true);
    FieldUtils.writeField(service, "repo", repo, true);

    Mockito.doNothing().when(commandService).cronCreate(Mockito.any());
    EligibleTrainFutureVersionControl control = new EligibleTrainFutureVersionControl();

    EligibleTrainTemp temp = new EligibleTrainTemp();
    temp.setCorrelationId(UUID.randomUUID().toString());
    temp.setCreateUser("user");
    temp.setEffSaleDate(LocalDate.now());
    temp.setFarePlanCode("code");
    temp.setCreateUserEmail("email");

    control.setEligibleTrainTemp(temp);

    Mockito.when(repo.save(Mockito.any())).thenReturn(control);
    Mockito.when(repo.findById(Mockito.any())).thenReturn(Optional.of(control));
  }

  @Test
  void testExecute() {
    Map<String, Object> map = new HashMap<>();
    map.put("correlationId", UUID.randomUUID().toString());
    map.put("futureVersionControlId", String.valueOf(1));

    TaskExecution execution = new TaskExecution();
    execution.setParameters(map);

    StepResult<String> resultStep = service.execute(execution);
    Assertions.assertNotNull(resultStep);
  }
}