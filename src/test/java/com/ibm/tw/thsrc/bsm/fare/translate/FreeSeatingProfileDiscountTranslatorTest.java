package com.ibm.tw.thsrc.bsm.fare.translate;

import static org.junit.jupiter.api.Assertions.*;

import com.ibm.tw.thsrc.bsm.fa.dto.FreeSeatingProfileDiscountOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.FreeSeatingProfileDiscount;
import com.ibm.tw.thsrc.bsm.fare.entity.FreeSeatingTicketType;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerProfile;
import java.math.BigDecimal;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class FreeSeatingProfileDiscountTranslatorTest {


  @Test
  void toFreeSeatingProfileDiscountOutput() throws IllegalAccessException {

    PassengerProfile profile = new PassengerProfile();
    profile.setCode("F");
    profile.setZhName("成人");
    profile.setDisplayOrder(1);

    FreeSeatingTicketType type = new FreeSeatingTicketType();
    type.setCode("1");
    type.setName("定期票");

    FreeSeatingProfileDiscount entity = new FreeSeatingProfileDiscount();
    entity.setPassengerProfile(profile);
    entity.setFreeSeatingTicketType(type);
    entity.setDiscountPct(BigDecimal.ONE);

    FreeSeatingProfileDiscountOutput output =
        FreeSeatingProfileDiscountTranslator.toFreeSeatingProfileDiscountOutput(entity);

    Assertions.assertEquals(output.getDiscountPct(), entity.getDiscountPct());

  }
}