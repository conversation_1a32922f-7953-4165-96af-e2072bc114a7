/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.core.entity.BaseEntity;
import com.ibm.tw.thsrc.bsm.fare.entity.StationPairProjection;
import com.ibm.tw.thsrc.bsm.sc.dto.StationPairOutput;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class StationPairProjectionTestData {

  public List<StationPairProjection> ENTITIES = new ArrayList<>();

  public StationPairProjection ENTITY1 = new StationPairProjection();
  public StationPairProjection ENTITY2 = new StationPairProjection();

  public StationPairOutput OUTPUT = new StationPairOutput();
  public StationPairOutput OUTPUT2 = new StationPairOutput();

  public StationPairProjectionTestData() throws Exception {
    createEntityTestData();
    createOutputTestData();
  }

  void createEntityTestData() throws Exception {

    Field id2 = BaseEntity.class.getDeclaredField("id");
    id2.setAccessible(true);
    id2.set(ENTITY1, Long.valueOf(1));

    Field departureCode2 = StationPairProjection.class.getDeclaredField("departureCode");
    departureCode2.setAccessible(true);
    departureCode2.set(ENTITY1, "NAK");

    Field departureName2 = StationPairProjection.class.getDeclaredField("departureName");
    departureName2.setAccessible(true);
    departureName2.set(ENTITY1, "南港");

    Field arrivalCode2 = StationPairProjection.class.getDeclaredField("arrivalCode");
    arrivalCode2.setAccessible(true);
    arrivalCode2.set(ENTITY1, "ZUY");

    Field arrivalName2 = StationPairProjection.class.getDeclaredField("arrivalName");
    arrivalName2.setAccessible(true);
    arrivalName2.set(ENTITY1, "左營");

    Field id = BaseEntity.class.getDeclaredField("id");
    id.setAccessible(true);
    id.set(ENTITY2, Long.valueOf(2));

    Field departureCode = StationPairProjection.class.getDeclaredField("departureCode");
    departureCode.setAccessible(true);
    departureCode.set(ENTITY2, "TPE");

    Field departureName = StationPairProjection.class.getDeclaredField("departureName");
    departureName.setAccessible(true);
    departureName.set(ENTITY2, "台北");

    Field arrivalCode = StationPairProjection.class.getDeclaredField("arrivalCode");
    arrivalCode.setAccessible(true);
    arrivalCode.set(ENTITY2, "ZUY");

    Field arrivalName = StationPairProjection.class.getDeclaredField("arrivalName");
    arrivalName.setAccessible(true);
    arrivalName.set(ENTITY2, "左營");

    ENTITIES.add(ENTITY1);
    ENTITIES.add(ENTITY2);

  }

  void createOutputTestData() {
    OUTPUT.setId(1);
    OUTPUT.setArrivalStationCode("TPE");
    OUTPUT.setArrivalStationName("台北");
    OUTPUT.setDepartureStationCode("NAK");
    OUTPUT.setDepartureStationName("南港");

    OUTPUT2.setId(2);
    OUTPUT2.setArrivalStationCode("NAK");
    OUTPUT2.setArrivalStationName("南港");
    OUTPUT2.setDepartureStationCode("TPE");
    OUTPUT2.setDepartureStationName("台北");
  }
}

