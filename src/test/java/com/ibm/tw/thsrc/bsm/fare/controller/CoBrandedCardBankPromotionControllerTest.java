/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardBankPromotionInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardBankPromotionOutput;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardBankPromotionCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardBankPromotionQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.CoBrandedCardBankPromotionTestData;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest(classes = {FareApplication.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@AutoConfigureMockMvc
class CoBrandedCardBankPromotionControllerTest {

  private static final String API_PATH = "/CoBrandedCardBankPromotions";
  private CoBrandedCardBankPromotionTestData TEST_DATA = new CoBrandedCardBankPromotionTestData();
  @Autowired
  private CoBrandedCardBankPromotionController controller;
  @MockBean
  private CoBrandedCardBankPromotionCommandService commandService;
  @MockBean
  private CoBrandedCardBankPromotionQueryService queryService;
  @Autowired
  private MockMvc mockMvc;
  @Autowired
  private ObjectMapper mapper;

  public CoBrandedCardBankPromotionControllerTest() throws Exception {

  }

  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any()))
        .thenReturn(new PageImpl<>(TEST_DATA.OUTPUTS));

    List<CoBrandedCardBankPromotionOutput> expected = TEST_DATA.OUTPUTS;
    Search search = new Search();
    Page<CoBrandedCardBankPromotionOutput> actual = controller.search(search);

    Assertions.assertEquals(expected.size(), actual.getContent().size());
  }


  @Test
  void modify() {
    Mockito.when(commandService.patch(ArgumentMatchers.any())).thenReturn(new HashSet<>());
    Mockito.when(queryService.read(ArgumentMatchers.anySet()))
        .thenReturn(new HashSet<>(TEST_DATA.OUTPUTS));
    CollectionModificationInput<CoBrandedCardBankPromotionInput> input = new CollectionModificationInput<>();
    input.getCreations().add(TEST_DATA.CREATE_INPUT);

    List<CoBrandedCardBankPromotionOutput> expected = TEST_DATA.OUTPUTS;
    Set<CoBrandedCardBankPromotionOutput> actual = controller.modify(input);

    Assertions.assertEquals(expected.size(), actual.size());
  }

  @Test
  void inputValidation() throws Exception {
    for (CoBrandedCardBankPromotionInput invalidInput : TEST_DATA.INVALID_INPUTS) {
      CollectionModificationInput<CoBrandedCardBankPromotionInput> input = new CollectionModificationInput<>();
      input.getCreations().add(invalidInput);
      MvcResult mvcResult = mockMvc
          .perform(MockMvcRequestBuilders.patch(API_PATH)
              .content(mapper.writeValueAsString(input))
              .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
          .andReturn();

      Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), mvcResult.getResponse().getStatus());
    }
  }
}
