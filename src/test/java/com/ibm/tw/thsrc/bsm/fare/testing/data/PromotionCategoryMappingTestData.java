/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.entity.AggregateRoot;
import com.ibm.tw.thsrc.bsm.core.entity.BaseEntity;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryMappingInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryMappingOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryMappingValidationDetail;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryValidationInput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionCategoryValidationOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.PromotionProjectOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.FarePlanType;
import com.ibm.tw.thsrc.bsm.fa.enums.FareProjectType;
import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePlan;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProjectMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountPlan;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionCategoryMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan;
import com.ibm.tw.thsrc.bsm.util.BeanUtils;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

public class PromotionCategoryMappingTestData {

  public PromotionCategoryTestData CATEGORY_DATA = new PromotionCategoryTestData();
  public FareProjectMappingTestData MAPPING_DATA = new FareProjectMappingTestData();

  public List<PromotionCategoryMapping> ENTITIES = new LinkedList<>();

  public FareProject FAKE_PROJECT = new FareProject();
  public FareProject FAKE_PROJECT_2 = new FareProject();
  public FareProject FAKE_PROJECT_3 = new FareProject();
  public List<FareProjectMapping> PROJECT_MAPPING = new LinkedList<>();
  public List<FareProjectMapping> ONE_NO_PROFILE_PROJECT_MAPPING = new LinkedList<>();
  public List<FareProjectMapping> TWO_NO_PROFILE_PROJECT_MAPPING = new LinkedList<>();
  public List<FareProjectMapping> TWO_NO_SMALLEST_CONDITIONS_PROJECT_MAPPING = new LinkedList<>();
  public List<PromotionPlan> PROMOTIONS = new LinkedList<>();

  public List<PromotionCategoryMappingOutput> RECENT_OUTPUTS = new LinkedList<>();
  public PromotionCategoryValidationOutput VALIDATION_OUTPUT = new PromotionCategoryValidationOutput();
  public CollectionModificationInput<PromotionCategoryMappingInput> CREATE_INPUT = new CollectionModificationInput<>();
  public CollectionModificationInput<PromotionCategoryMappingInput> UPDATE_INPUT = new CollectionModificationInput<>();
  public CollectionModificationInput<PromotionCategoryMappingInput> DELETE_INPUT = new CollectionModificationInput<>();
  public PromotionCategoryValidationInput VALIDATE_INPUT = new PromotionCategoryValidationInput();
  public CollectionModificationInput<PromotionCategoryMappingInput> CREATE_DUPLICATE_INPUT = new CollectionModificationInput<>();


  public PromotionCategoryMappingTestData() throws Exception {
    setEntities();
    setOutputs();
    setInputs(true);
  }

  public void resetInput() {
    setInputs(false);
  }

  private void setEntities() throws Exception {
    PromotionPlan promotion1 = new PromotionPlan();
    promotion1.setCode("P222");
    promotion1.setEffDate(LocalDate.now().minusDays(1));
    promotion1.setName("測試1");
    promotion1.setDiscountPct(BigDecimal.valueOf(30));
    promotion1.setType(FarePlanType.PROMOTION);
    promotion1.setSmallGroup(false);
    promotion1.setAllClassesValid(true);
    promotion1.setDscDate(LocalDate.now().plusDays(10));
    PROMOTIONS.add(promotion1);

    PromotionPlan promotion2 = new PromotionPlan();
    BeanUtils.copyProperties(promotion2, promotion1);
    promotion2.setCode("P333");
    promotion2.setName("測試2");
    PROMOTIONS.add(promotion2);

    MAPPING_DATA.ENTITIES.get(0).setPromotionPlan(promotion1);

    PromotionCategoryMapping mapping1 = new PromotionCategoryMapping();
    mapping1.setCategory(CATEGORY_DATA.ENTITIES.get(0));
    mapping1.setPromotionPlan(promotion1);
    mapping1.setIsTweDisplay(Boolean.TRUE);
    mapping1.setEffDate(LocalDate.now());
    ENTITIES.add(mapping1);

    PromotionCategoryMapping mapping2 = new PromotionCategoryMapping();
    mapping2.setCategory(CATEGORY_DATA.ENTITIES.get(0));
    mapping2.setPromotionPlan(promotion1);
    mapping2.setIsTweDisplay(Boolean.TRUE);
    mapping2.setEffDate(LocalDate.now().plusDays(2));
    ENTITIES.add(mapping2);

    // set fake data for project side
    Field id = BaseEntity.class.getDeclaredField("id");
    id.setAccessible(true);
    Field dataVersion = AggregateRoot.class.getDeclaredField("dataVersion");
    dataVersion.setAccessible(true);

    id.set(FAKE_PROJECT, 1L);
    dataVersion.set(FAKE_PROJECT, 0L);
    FAKE_PROJECT.setCode("Y010");
    FAKE_PROJECT.setCodeNumber("010");
    FAKE_PROJECT.setType(FareProjectType.STANDARD);
    FAKE_PROJECT.setName("測試專案");

    FareProjectMapping projectMapping = new FareProjectMapping();
    id.set(projectMapping, 1L);
    dataVersion.set(projectMapping, 0L);
    projectMapping.setFareProject(FAKE_PROJECT);
    projectMapping.setPromotionPlan(promotion1);
    PROJECT_MAPPING.add(projectMapping);

    id.set(FAKE_PROJECT_2, 2L);
    dataVersion.set(FAKE_PROJECT_2, 0L);
    FAKE_PROJECT_2.setCode("Y000");
    FAKE_PROJECT_2.setCodeNumber("000");
    FAKE_PROJECT_2.setType(FareProjectType.STANDARD);
    FAKE_PROJECT_2.setName("測試專案");

    FareProjectMapping projectMapping2 = new FareProjectMapping();
    id.set(projectMapping2, 2L);
    dataVersion.set(projectMapping2, 0L);
    projectMapping2.setFareProject(FAKE_PROJECT_2);
    projectMapping2.setPromotionPlan(promotion1);
    projectMapping2.setProfileDiscountPlan(new ProfileDiscountPlan());

    ONE_NO_PROFILE_PROJECT_MAPPING.add(projectMapping);
    ONE_NO_PROFILE_PROJECT_MAPPING.add(projectMapping2);

    id.set(FAKE_PROJECT_3, 3L);
    dataVersion.set(FAKE_PROJECT_3, 0L);
    FAKE_PROJECT_3.setCode("Y020");
    FAKE_PROJECT_3.setCodeNumber("020");
    FAKE_PROJECT_3.setType(FareProjectType.STANDARD);
    FAKE_PROJECT_3.setName("測試專案");

    FareProjectMapping projectMapping3 = new FareProjectMapping();
    id.set(projectMapping3, 3L);
    dataVersion.set(projectMapping3, 0L);
    projectMapping3.setFareProject(FAKE_PROJECT_3);
    projectMapping3.setPromotionPlan(promotion1);
    projectMapping3.setBasicFarePlan(new BasicFarePlan());
    PROJECT_MAPPING.add(projectMapping3);

    TWO_NO_PROFILE_PROJECT_MAPPING.add(projectMapping);
    TWO_NO_PROFILE_PROJECT_MAPPING.add(projectMapping2);
    TWO_NO_PROFILE_PROJECT_MAPPING.add(projectMapping3);

    FareProjectMapping projectMapping4 = new FareProjectMapping();
    id.set(projectMapping4, 4L);
    dataVersion.set(projectMapping4, 0L);
    projectMapping4.setFareProject(FAKE_PROJECT_2);
    projectMapping4.setPromotionPlan(promotion1);
    projectMapping4.setBasicFarePlan(new BasicFarePlan());
    PROJECT_MAPPING.add(projectMapping4);

    TWO_NO_SMALLEST_CONDITIONS_PROJECT_MAPPING.add(projectMapping3);
    TWO_NO_SMALLEST_CONDITIONS_PROJECT_MAPPING.add(projectMapping4);
  }

  private void setOutputs() {
    PromotionCategoryMappingOutput mapping1 = new PromotionCategoryMappingOutput();
    mapping1.setDataVersion(0L);
    mapping1.setId(1L);
    mapping1.setPromotionCategoryId(1L);
    mapping1.setPromotionCategoryName(CATEGORY_DATA.OUTPUTS.get(0).getName());
    mapping1.setPromotionCategoryDisplayOrder(CATEGORY_DATA.OUTPUTS.get(0).getDisplayOrder());
    mapping1.setPromotionId(1L);
    mapping1.setPromotionCode(PROMOTIONS.get(0).getCode());
    mapping1.setPromotionDscDate(PROMOTIONS.get(0).getDscDate());
    mapping1.setPromotionDiscountPct(PROMOTIONS.get(0).getDiscountPct());
    mapping1.setEffDate(ENTITIES.get(0).getEffDate());
    mapping1.setIsTweDisplay(ENTITIES.get(0).getIsTweDisplay());

    PromotionProjectOutput projectOutput = new PromotionProjectOutput();
    projectOutput.setCode("Y010");
    projectOutput.setType(FareProjectType.STANDARD);
    projectOutput.setName("測試專案");
    mapping1.getProjects().add(projectOutput);

    RECENT_OUTPUTS.add(mapping1);

    // validation
    VALIDATION_OUTPUT.setCurrentEffDate(ENTITIES.get(0).getEffDate());
    PromotionCategoryMappingValidationDetail currentDetail = new PromotionCategoryMappingValidationDetail();
    currentDetail.setPromotionCode(PROMOTIONS.get(0).getCode());
    currentDetail.setProjectName(FAKE_PROJECT.getName());
    VALIDATION_OUTPUT.setCurrentMapping(Collections.singletonList(currentDetail));

    VALIDATION_OUTPUT.setFutureEffDate(ENTITIES.get(1).getEffDate());
    PromotionCategoryMappingValidationDetail future = new PromotionCategoryMappingValidationDetail();
    future.setPromotionCode(PROMOTIONS.get(0).getCode());
    future.setProjectName(FAKE_PROJECT.getName());
    VALIDATION_OUTPUT.setFutureMapping(Collections.singletonList(future));
  }

  private void setInputs(boolean isInit) {
    PromotionCategoryMappingInput input = new PromotionCategoryMappingInput();
    input.setPromotionId(2L);
    input.setDataVersion(0L);
    input.setEffDate(ENTITIES.get(1).getEffDate());
    input.setIsTweDisplay(Boolean.FALSE);
    input.setPromotionCategoryId(CATEGORY_DATA.OUTPUTS.get(0).getId());

    CREATE_INPUT.getCreations().add(input);
    PromotionCategoryMappingInput updateInput = new PromotionCategoryMappingInput();
    BeanUtils.copyProperties(updateInput, input);
    updateInput.setPromotionId(1L);
    updateInput.setPromotionCategoryId(2L);
    UPDATE_INPUT.getReplacements().put(2L, updateInput);

    DELETE_INPUT.getDeletions().add(1L);

    VALIDATE_INPUT.setCategoryId(CATEGORY_DATA.ENTITIES.get(0).getId());

    // invalid for service
    PromotionCategoryMappingInput duplicateCreate = new PromotionCategoryMappingInput();
    BeanUtils.copyProperties(duplicateCreate, input);
    duplicateCreate.setPromotionId(1L);
    duplicateCreate.setEffDate(ENTITIES.get(0).getEffDate());

    if (!isInit) {
      input.setPromotionId(PROMOTIONS.get(1).getId());
      input.setPromotionCategoryId(CATEGORY_DATA.ENTITIES.get(0).getId());
      CREATE_INPUT.getCreations().clear();
      CREATE_INPUT.getCreations().add(input);

      updateInput.setPromotionId(PROMOTIONS.get(0).getId());
      updateInput.setPromotionCategoryId(CATEGORY_DATA.ENTITIES.get(1).getId());
      UPDATE_INPUT.getCreations().clear();
      UPDATE_INPUT.getCreations().add(updateInput);

      DELETE_INPUT.getDeletions().clear();
      DELETE_INPUT.getDeletions().add(ENTITIES.get(0).getId());

      VALIDATE_INPUT.setCategoryId(CATEGORY_DATA.ENTITIES.get(0).getId());

      BeanUtils.copyProperties(duplicateCreate, input);
      duplicateCreate.setPromotionId(PROMOTIONS.get(0).getId());
      duplicateCreate.setEffDate(ENTITIES.get(0).getEffDate());
    }

    CREATE_DUPLICATE_INPUT.getCreations().add(duplicateCreate);
  }
}
