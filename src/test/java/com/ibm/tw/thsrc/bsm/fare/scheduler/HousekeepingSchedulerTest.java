/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.scheduler;

import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.verify;

import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.fare.service.CampaignCreditCardBatchCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.EligibleTrainCommandService;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.mockito.stubbing.Answer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest(classes = {FareApplication.class})
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "housekeeping.expiry.days=5",
    "housekeeping.scheduler.cron=* * * * * ?",
    "housekeeping.scheduler.zone=GMT+8"
})
class HousekeepingSchedulerTest {

  @MockBean
  CampaignCreditCardBatchCommandService campaignCreditCardBatchCommandService;
  @MockBean
  EligibleTrainCommandService eligibleTrainCommandService;
  @Autowired
  private HousekeepingScheduler scheduler;

  @Test
  void cleanSleepByOneSec() throws InterruptedException {
    final Object lock = new Object();
    Mockito.doAnswer((Answer<Void>) invocation -> {
      synchronized (lock) {
        lock.notifyAll();
      }
      return null;
    }).when(eligibleTrainCommandService).clean(ArgumentMatchers.any());

    synchronized (lock) {
      lock.wait();
    }

    verify(campaignCreditCardBatchCommandService, atLeastOnce()).clean(ArgumentMatchers.any());
    verify(eligibleTrainCommandService, atLeastOnce()).clean(ArgumentMatchers.any());
  }
}
