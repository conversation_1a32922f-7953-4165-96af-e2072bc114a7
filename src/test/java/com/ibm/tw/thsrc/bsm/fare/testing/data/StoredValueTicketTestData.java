/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.fa.dto.StoredValueTicketInput;
import com.ibm.tw.thsrc.bsm.fa.dto.StoredValueTicketOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.IssueTravelCardExpiryType;
import com.ibm.tw.thsrc.bsm.fare.entity.StoredValueTicket;
import com.ibm.tw.thsrc.bsm.fare.entity.StoredValueTicketOption;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class StoredValueTicketTestData {

  public StoredValueTicket ENTITY = new StoredValueTicket();
  public StoredValueTicketOutput OUTPUT = new StoredValueTicketOutput();
  public StoredValueTicketInput INPUT = new StoredValueTicketInput();
  public List<StoredValueTicketInput> INVALID_INPUTS = new ArrayList<>();


  public StoredValueTicketTestData() {
    setEntities();
    setOutputs();
    setInputs();
  }

  private void setEntities() {
    ENTITY.setExpiryType(IssueTravelCardExpiryType.BY_ISSUE_DATE);
    ENTITY.setRefundFeeAmt(BigDecimal.valueOf(100));
    ENTITY.setDiscountPct(BigDecimal.ZERO);
    ENTITY.setMinAmt(BigDecimal.valueOf(100));
    ENTITY.setMaxAmt(BigDecimal.valueOf(10000));
    ENTITY.setMaxAmtPerTxn(BigDecimal.valueOf(5000));
    ENTITY.setMaxAmtPerLoad(BigDecimal.valueOf(5000));
    ENTITY.setMagnetTktValidDays(BigDecimal.valueOf(90));

    StoredValueTicketOption load1 = new StoredValueTicketOption();
    load1.setStoredValueTicket(ENTITY);
    load1.setLoadAmt(BigDecimal.valueOf(10));
    StoredValueTicketOption load2 = new StoredValueTicketOption();
    load2.setStoredValueTicket(ENTITY);
    load2.setLoadAmt(BigDecimal.valueOf(100));

    ENTITY.getLoadAmount().add(load1);
    ENTITY.getLoadAmount().add(load2);
  }

  private void setOutputs() {
    OUTPUT.setDataVersion(0L);
    OUTPUT.setId(1L);
    OUTPUT.setExpiryType(IssueTravelCardExpiryType.BY_ISSUE_DATE);
    OUTPUT.setRefundFeeAmt(BigDecimal.valueOf(100));
    OUTPUT.setDiscountPct(BigDecimal.ZERO);
    OUTPUT.setMinAmt(BigDecimal.valueOf(100));
    OUTPUT.setMaxAmt(BigDecimal.valueOf(10000));
    OUTPUT.setMaxAmtPerTxn(BigDecimal.valueOf(5000));
    OUTPUT.setMaxAmtPerLoad(BigDecimal.valueOf(5000));
    OUTPUT.setMagnetTktValidDays(BigDecimal.valueOf(90));
    OUTPUT.getLoadAmount().add(10);
    OUTPUT.getLoadAmount().add(100);
  }

  private void setInputs() {
    INPUT.setDataVersion(0L);
    INPUT.setExpiryType(IssueTravelCardExpiryType.BY_USE_DATE);
    INPUT.setRefundFeeAmt(BigDecimal.valueOf(50));
    INPUT.setDiscountPct(BigDecimal.ZERO);
    INPUT.setMinAmt(BigDecimal.valueOf(100));
    INPUT.setMaxAmt(BigDecimal.valueOf(10000));
    INPUT.setMaxAmtPerTxn(BigDecimal.valueOf(5000));
    INPUT.setMaxAmtPerLoad(BigDecimal.valueOf(5000));
    INPUT.setMagnetTktValidDays(BigDecimal.valueOf(90));
    INPUT.getLoadAmount().add(10);
    INPUT.getLoadAmount().add(100);
    INPUT.getLoadAmount().add(1000);

    StoredValueTicketInput inputWithNullField = new StoredValueTicketInput();
    inputWithNullField.setDataVersion(0L);
    inputWithNullField.setExpiryType(IssueTravelCardExpiryType.BY_USE_DATE);
    inputWithNullField.setDiscountPct(BigDecimal.ZERO);
    inputWithNullField.setMinAmt(BigDecimal.valueOf(100));
    inputWithNullField.setMaxAmt(BigDecimal.valueOf(10000));
    inputWithNullField.setMaxAmtPerTxn(BigDecimal.valueOf(5000));
    inputWithNullField.setMaxAmtPerLoad(BigDecimal.valueOf(5000));
    inputWithNullField.setMagnetTktValidDays(BigDecimal.valueOf(90));
    inputWithNullField.getLoadAmount().add(10);
    inputWithNullField.getLoadAmount().add(100);
    inputWithNullField.getLoadAmount().add(1000);

    StoredValueTicketInput inputWithLoadAmountLenOver8 = new StoredValueTicketInput();
    inputWithLoadAmountLenOver8.setDataVersion(0L);
    inputWithLoadAmountLenOver8.setExpiryType(IssueTravelCardExpiryType.BY_USE_DATE);
    inputWithLoadAmountLenOver8.setRefundFeeAmt(BigDecimal.valueOf(50));
    inputWithLoadAmountLenOver8.setDiscountPct(BigDecimal.ZERO);
    inputWithLoadAmountLenOver8.setMinAmt(BigDecimal.valueOf(100));
    inputWithLoadAmountLenOver8.setMaxAmt(BigDecimal.valueOf(10000));
    inputWithLoadAmountLenOver8.setMaxAmtPerTxn(BigDecimal.valueOf(5000));
    inputWithLoadAmountLenOver8.setMaxAmtPerLoad(BigDecimal.valueOf(5000));
    inputWithLoadAmountLenOver8.setMagnetTktValidDays(BigDecimal.valueOf(90));
    inputWithLoadAmountLenOver8.getLoadAmount().add(100);
    inputWithLoadAmountLenOver8.getLoadAmount().add(200);
    inputWithLoadAmountLenOver8.getLoadAmount().add(300);
    inputWithLoadAmountLenOver8.getLoadAmount().add(400);
    inputWithLoadAmountLenOver8.getLoadAmount().add(500);
    inputWithLoadAmountLenOver8.getLoadAmount().add(600);
    inputWithLoadAmountLenOver8.getLoadAmount().add(700);
    inputWithLoadAmountLenOver8.getLoadAmount().add(800);
    inputWithLoadAmountLenOver8.getLoadAmount().add(900);

    StoredValueTicketInput inputWithoutLoadAmount = new StoredValueTicketInput();
    inputWithoutLoadAmount.setDataVersion(0L);
    inputWithoutLoadAmount.setExpiryType(IssueTravelCardExpiryType.BY_USE_DATE);
    inputWithoutLoadAmount.setRefundFeeAmt(BigDecimal.valueOf(50));
    inputWithoutLoadAmount.setDiscountPct(BigDecimal.ZERO);
    inputWithoutLoadAmount.setMinAmt(BigDecimal.valueOf(100));
    inputWithoutLoadAmount.setMaxAmt(BigDecimal.valueOf(10000));
    inputWithoutLoadAmount.setMaxAmtPerTxn(BigDecimal.valueOf(5000));
    inputWithoutLoadAmount.setMaxAmtPerLoad(BigDecimal.valueOf(5000));
    inputWithoutLoadAmount.setMagnetTktValidDays(BigDecimal.valueOf(90));

    INVALID_INPUTS.add(inputWithNullField);
    INVALID_INPUTS.add(inputWithLoadAmountLenOver8);
    INVALID_INPUTS.add(inputWithoutLoadAmount);
  }
}
