/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketEndorsementInput;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketEndorsementOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.TicketEndorsementType;
import com.ibm.tw.thsrc.bsm.fare.entity.TicketEndorsement;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.IntStream;

public class TicketEndorsementTestData {

  public static final LocalDate DATE1 = LocalDate.now();
  public static final LocalDate DATE2 = LocalDate.now().plusDays(1);
  public static final LocalDate DATE3 = LocalDate.now().plusMonths(1);
  public List<TicketEndorsement> ENTITIES = new LinkedList<>();
  public List<TicketEndorsementOutput> RECENT_OUTPUTS = new LinkedList<>();
  public CollectionModificationInput<TicketEndorsementInput> UPDATE_FUTURE_INPUTS = new CollectionModificationInput<>();
  public CollectionModificationInput<TicketEndorsementInput> CREATE_FUTURE_INPUTS = new CollectionModificationInput<>();
  public CollectionModificationInput<TicketEndorsementInput> NOT_EXIST_INPUT = new CollectionModificationInput<>();
  public List<TicketEndorsementInput> INVALID_INPUTS = new LinkedList<>();
  private List<TicketEndorsementType> types = Arrays.asList(TicketEndorsementType.ENDORSEMENT_1,
      TicketEndorsementType.ENDORSEMENT_2, TicketEndorsementType.ENDORSEMENT_3,
      TicketEndorsementType.ENDORSEMENT_4, TicketEndorsementType.ENDORSEMENT_5,
      TicketEndorsementType.ENDORSEMENT_6, TicketEndorsementType.ENDORSEMENT_7,
      TicketEndorsementType.ENDORSEMENT_8, TicketEndorsementType.ENDORSEMENT_9);


  public TicketEndorsementTestData() {
    setEntities();
    setOutputs();
    setInputs();
  }

  private void setEntities() {
    IntStream.range(1, 10).forEach(e -> ENTITIES.add(getBasicEntity(e, DATE1)));
    IntStream.range(1, 10).forEach(e -> ENTITIES.add(getBasicEntity(e, DATE2)));
  }

  private TicketEndorsement getBasicEntity(int e, LocalDate effDate) {
    TicketEndorsement entity = new TicketEndorsement();
    entity.setType(types.get(e - 1));
    entity.setEffDate(effDate);
    entity.setEnName("test" + e);
    entity.setZhName("測試" + e);

    return entity;
  }

  private void setOutputs() {
    IntStream.range(1, 10).forEach(e -> RECENT_OUTPUTS.add(getOutput(e, DATE1)));
  }

  private TicketEndorsementOutput getOutput(int e, LocalDate effDate) {
    TicketEndorsementOutput output = new TicketEndorsementOutput();
    output.setId(e);
    output.setType(types.get(e - 1));
    output.setEffDate(effDate);
    output.setEnName("test update" + e);
    output.setZhName("測試更新" + e);

    return output;
  }

  private void setInputs() {
    IntStream.range(1, 10).forEach(
        e -> UPDATE_FUTURE_INPUTS.getReplacements().put((e - 1) * 2L + 1L, getInput(e, DATE2)));
    IntStream.range(1, 10)
        .forEach(e -> CREATE_FUTURE_INPUTS.getCreations().add(getInput(e, DATE3)));

    // invalid for controller
    TicketEndorsementInput inputWithNull = getInput(1, DATE1);
    inputWithNull.setEffDate(null);

    TicketEndorsementInput inputWithZhNameOver15 = getInput(1, DATE1);
    inputWithZhNameOver15.setZhName("12345678910123456");

    INVALID_INPUTS.add(inputWithNull);
    INVALID_INPUTS.add(inputWithZhNameOver15);

    // invalid for service
    NOT_EXIST_INPUT.getReplacements().put(Long.MAX_VALUE - 1, getInput(1, DATE1));
  }

  private TicketEndorsementInput getInput(int e, LocalDate effDate) {
    TicketEndorsementInput input = new TicketEndorsementInput();
    input.setDataVersion(0L);
    input.setType(types.get(e - 1));
    input.setEffDate(effDate);
    input.setEnName("test create" + e);
    input.setZhName("測試新增" + e);

    return input;
  }
}
