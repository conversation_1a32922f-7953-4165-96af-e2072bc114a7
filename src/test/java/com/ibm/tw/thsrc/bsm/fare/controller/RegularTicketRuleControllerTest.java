/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.RegularTicketRuleInput;
import com.ibm.tw.thsrc.bsm.fa.dto.RegularTicketRuleOutput;
import com.ibm.tw.thsrc.bsm.fare.service.RegularTicketRuleCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.RegularTicketRuleQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.RegularTicketRuleTestData;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest(classes = {FareApplication.class})
@AutoConfigureMockMvc
@ActiveProfiles("test")
class RegularTicketRuleControllerTest {

  private static final String API_PATH = "/RegularTicketRules/VersionUpserts";
  private static final RegularTicketRuleTestData TEST_DATA = new RegularTicketRuleTestData();
  @Autowired
  private RegularTicketRuleController controller;
  @MockBean
  private RegularTicketRuleCommandService commandService;
  @MockBean
  private RegularTicketRuleQueryService queryService;
  @Autowired
  private MockMvc mockMvc;
  @Autowired
  private ObjectMapper mapper;

  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any()))
        .thenReturn(new PageImpl<>(Collections.singletonList(TEST_DATA.FUTURE_OUTPUT)));

    List<RegularTicketRuleOutput> expected = Collections.singletonList(TEST_DATA.FUTURE_OUTPUT);

    Page<RegularTicketRuleOutput> resultPage = controller.search(new Search());
    Assertions.assertEquals(expected.size(), resultPage.getContent().size());
  }

  @Test
  void upsert() {
    Mockito.when(queryService.read(ArgumentMatchers.<Pair<List<Long>, Set<Long>>>any()))
        .thenReturn(TEST_DATA.FUTURE_OUTPUT);

    RegularTicketRuleOutput expected = TEST_DATA.FUTURE_OUTPUT;
    RegularTicketRuleOutput actual = controller.upsert(TEST_DATA.UPDATE_INPUT);

    Assertions.assertEquals(expected.getTicketProductPolicy().getId(),
        actual.getTicketProductPolicy().getId());
    Assertions.assertEquals(expected.getTicketProductPolicy().getEffDate(),
        actual.getTicketProductPolicy().getEffDate());
    Assertions.assertEquals(expected.getTicketProductPolicy().getDataVersion(),
        actual.getTicketProductPolicy().getDataVersion());
    Assertions.assertEquals(expected.getTicketProductPolicy().getCscCardDepositAmt(),
        actual.getTicketProductPolicy().getCscCardDepositAmt());
    Assertions.assertEquals(expected.getTicketProductPolicy().getCscCardMarketingFeeAmt(),
        actual.getTicketProductPolicy().getCscCardMarketingFeeAmt());
    Assertions.assertEquals(expected.getTicketProductPolicy().getIsPlatFormTktActivated(),
        actual.getTicketProductPolicy().getIsPlatFormTktActivated());
    Assertions.assertEquals(expected.getTicketProductPolicy().getOnewayTktRefundFeeAmt(),
        actual.getTicketProductPolicy().getOnewayTktRefundFeeAmt());
    Assertions.assertEquals(expected.getTicketProductPolicy().getPlatformTktUnitPrice(),
        actual.getTicketProductPolicy().getPlatformTktUnitPrice());
    Assertions.assertEquals(expected.getTicketProductPolicy().getReservedSeatTktValidDays(),
        actual.getTicketProductPolicy().getReservedSeatTktValidDays());
    Assertions.assertEquals(expected.getTicketProductPolicy().getRoundTripTktInboundValidDays(),
        actual.getTicketProductPolicy().getRoundTripTktInboundValidDays());
    Assertions.assertEquals(expected.getTicketProductPolicy().getRoundTripTktOutboundValidDays(),
        actual.getTicketProductPolicy().getRoundTripTktOutboundValidDays());
    Assertions.assertEquals(expected.getRefundFeeRules().size(), actual.getRefundFeeRules().size());

  }

  @Test
  void inputValidation() throws Exception {
    for (RegularTicketRuleInput invalidInput : TEST_DATA.INVALID_INPUTS) {
      MvcResult mvcResult = mockMvc
          .perform(MockMvcRequestBuilders.post(API_PATH)
              .content(mapper.writeValueAsString(invalidInput))
              .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
          .andReturn();

      Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(),
          mvcResult.getResponse().getStatus());
    }
  }
}
