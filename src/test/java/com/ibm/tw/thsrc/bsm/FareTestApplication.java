/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm;

import javax.annotation.PostConstruct;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class FareTestApplication {
  public static void main(String[] args) {
    SpringApplication.run(FareApplication.class, args);
  }

  @PostConstruct
  public void init() {
    // make select xxx from yyy limit ? work in h2 2.x
    org.h2.engine.Mode.getInstance("MSSQLServer").limit = true;
  }

}
