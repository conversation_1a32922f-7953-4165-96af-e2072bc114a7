/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.fa.dto.ReissueTicketRefundFeeInput;
import com.ibm.tw.thsrc.bsm.fa.dto.ReissueTicketRefundFeeOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.ReissueTicketRefundFee;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ReissueTicketRefundFeeTestData {

  public List<ReissueTicketRefundFee> QUERY_ENTITIES = new ArrayList<>();
  public List<ReissueTicketRefundFee> ENTITIES = new ArrayList<>();
  public List<ReissueTicketRefundFeeOutput> OUTPUTS = new ArrayList<>();
  public ReissueTicketRefundFeeInput UPDATE_INPUT = new ReissueTicketRefundFeeInput();
  public ReissueTicketRefundFeeInput CREATE_INPUT = new ReissueTicketRefundFeeInput();
  public List<ReissueTicketRefundFeeInput> INVALID_INPUTS = new ArrayList<>();

  public ReissueTicketRefundFeeTestData() {
    setEntities();
    setOutputs(true);
    setInputs();
  }

  public void resetOutput() {
    setOutputs(false);
  }

  private void setEntities() {
    ReissueTicketRefundFee reissue1 = new ReissueTicketRefundFee();
    reissue1.setFeePct(BigDecimal.valueOf(20));
    reissue1.setIsAdditionalCharges(Boolean.FALSE);

    ReissueTicketRefundFee reissue2 = new ReissueTicketRefundFee();
    reissue2.setEffDate(LocalDate.now());
    reissue2.setFeePct(BigDecimal.valueOf(20));
    reissue2.setIsAdditionalCharges(Boolean.FALSE);

    QUERY_ENTITIES.add(reissue1);
    QUERY_ENTITIES.add(reissue2);

    ReissueTicketRefundFee reissue3 = new ReissueTicketRefundFee();
    reissue3.setFeePct(BigDecimal.valueOf(20));
    reissue3.setIsAdditionalCharges(Boolean.FALSE);

    ReissueTicketRefundFee reissue4 = new ReissueTicketRefundFee();
    reissue4.setEffDate(LocalDate.now());
    reissue4.setFeePct(BigDecimal.valueOf(20));
    reissue4.setIsAdditionalCharges(Boolean.FALSE);

    ENTITIES.add(reissue3);
    ENTITIES.add(reissue4);
  }

  private void setOutputs(boolean isInit) {

    ReissueTicketRefundFeeOutput reissueOutput1 = new ReissueTicketRefundFeeOutput();
    reissueOutput1.setId(1L);
    reissueOutput1.setEffDate(null);
    reissueOutput1.setFeePct(BigDecimal.valueOf(20));
    reissueOutput1.setIsAdditionalCharges(Boolean.FALSE);

    ReissueTicketRefundFeeOutput reissueOutput2 = new ReissueTicketRefundFeeOutput();
    reissueOutput2.setId(2L);
    reissueOutput2.setEffDate(LocalDate.now());
    reissueOutput2.setFeePct(BigDecimal.valueOf(20));
    reissueOutput2.setIsAdditionalCharges(Boolean.FALSE);

    if (!isInit) {
      reissueOutput1.setId(QUERY_ENTITIES.get(0).getId());
      reissueOutput2.setId(QUERY_ENTITIES.get(1).getId());
    }

    OUTPUTS.clear();
    OUTPUTS.add(reissueOutput1);
    OUTPUTS.add(reissueOutput2);
  }

  private void setInputs() {
    UPDATE_INPUT = getBasicInput(1L, null);
    CREATE_INPUT = getBasicInput(null, LocalDate.now());

    // for controller
    ReissueTicketRefundFeeInput inputWithNull = getBasicInput(null, null);
    inputWithNull.setIsAdditionalCharges(null);
    ReissueTicketRefundFeeInput inputPctNeg = getBasicInput(null, null);
    inputPctNeg.setFeePct(BigDecimal.valueOf(-1));
    ReissueTicketRefundFeeInput inputPctOver100 = getBasicInput(null, null);
    inputPctOver100.setFeePct(BigDecimal.valueOf(101));
    INVALID_INPUTS.add(inputWithNull);
    INVALID_INPUTS.add(inputPctNeg);
    INVALID_INPUTS.add(inputPctOver100);
  }

  private ReissueTicketRefundFeeInput getBasicInput(Long baseId, LocalDate date) {

    ReissueTicketRefundFeeInput reissueInput = new ReissueTicketRefundFeeInput();
    reissueInput.setId(Objects.isNull(baseId) ? null : baseId);
    reissueInput.setEffDate(date);
    reissueInput.setDataVersion(0L);
    reissueInput.setFeePct(BigDecimal.valueOf(30));
    reissueInput.setIsAdditionalCharges(Boolean.FALSE);

    return reissueInput;
  }
}
