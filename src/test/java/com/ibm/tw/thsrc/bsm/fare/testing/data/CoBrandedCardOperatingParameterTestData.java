/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.core.entity.AggregateRoot;
import com.ibm.tw.thsrc.bsm.core.entity.BaseEntity;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardOperatingParameterInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardOperatingParameterOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardPenaltyPolicyInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardPenaltyPolicyOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardProfileDiscountInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardProfileDiscountOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardTransferBonusInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardTransferBonusOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.CoBrandedCardTransportMode;
import com.ibm.tw.thsrc.bsm.fa.enums.ElectronicMoneyType;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardPenaltyPolicy;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardProfileDiscount;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardRoundOffRule;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardTransferBonus;
import com.ibm.tw.thsrc.bsm.fare.entity.StationProjection;
import com.ibm.tw.thsrc.bsm.sc.enums.StationOperationalStatus;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.IntStream;
import org.mockito.Mockito;

public class CoBrandedCardOperatingParameterTestData {

  public final List<CoBrandedCardTransportMode> transportModes = Arrays.asList(
      CoBrandedCardTransportMode.MRT, CoBrandedCardTransportMode.BUS_1,
      CoBrandedCardTransportMode.BUS_2, CoBrandedCardTransportMode.BUS_3,
      CoBrandedCardTransportMode.MRT_PARKING_LOT, CoBrandedCardTransportMode.PARKING_LOT);
  public CoBrandedCardTestData CARD_DATA = new CoBrandedCardTestData();
  public CoBrandedCardRoundOffRule MOCK_ROUND_RULE = Mockito.mock(CoBrandedCardRoundOffRule.class);
  public CoBrandedCardRoundOffRule QUERY_ROUND_RULE = new CoBrandedCardRoundOffRule();
  public CoBrandedCardMunicipalityTestData MUNICIPALITY_DATA = new CoBrandedCardMunicipalityTestData();
  public CoBrandedCardProfileTestData PROFILE_DATA = new CoBrandedCardProfileTestData();
  public List<CoBrandedCardTransferBonus> BONUS_ENTITIES = new LinkedList<>();
  public CoBrandedCardProfileDiscount DISCOUNT_ENTITY = new CoBrandedCardProfileDiscount();
  public CoBrandedCardPenaltyPolicy PENALTY_ENTITY = new CoBrandedCardPenaltyPolicy();

  public CoBrandedCardOperatingParameterOutput OUTPUT = new CoBrandedCardOperatingParameterOutput();
  public CoBrandedCardOperatingParameterInput INPUT = new CoBrandedCardOperatingParameterInput();

  public CoBrandedCardOperatingParameterTestData() throws Exception {
    setEntities();
    setOutputs();
    setInputs();
  }

  private void setEntities() throws Exception {
    Field id = BaseEntity.class.getDeclaredField("id");
    id.setAccessible(true);
    Field dataVersion = AggregateRoot.class.getDeclaredField("dataVersion");
    dataVersion.setAccessible(true);

    IntStream.range(0, 6).forEach(e -> {
      try {
        BONUS_ENTITIES.add(getBonusEntity(e));
      } catch (Exception ex) {
        throw new RuntimeException(ex);
      }
    });

    id.set(DISCOUNT_ENTITY, 1L);
    dataVersion.set(DISCOUNT_ENTITY, 0L);
    DISCOUNT_ENTITY.setCoBrandedCard(CARD_DATA.ENTITIES.get(0));
    DISCOUNT_ENTITY.setProfile(PROFILE_DATA.ENTITIES.get(0));
    DISCOUNT_ENTITY.setMunicipality(MUNICIPALITY_DATA.ENTITIES.get(0));
    DISCOUNT_ENTITY.setDiscountPct(BigDecimal.valueOf(80));

    id.set(PENALTY_ENTITY, 1L);
    dataVersion.set(PENALTY_ENTITY, 0L);
    PENALTY_ENTITY.setCoBrandedCard(CARD_DATA.ENTITIES.get(0));
    PENALTY_ENTITY.setOvertimePenalty(BigDecimal.valueOf(100));
    PENALTY_ENTITY.setSameStationEntryExitPenalty(BigDecimal.valueOf(100));
    PENALTY_ENTITY.setEntryExitCodeMismatchPenalty(BigDecimal.valueOf(100));

    id.set(PENALTY_ENTITY, 1L);
    dataVersion.set(PENALTY_ENTITY, 0L);
    PENALTY_ENTITY.setCoBrandedCard(CARD_DATA.ENTITIES.get(0));
    PENALTY_ENTITY.setOvertimePenalty(BigDecimal.valueOf(100));
    PENALTY_ENTITY.setSameStationEntryExitPenalty(BigDecimal.valueOf(100));
    PENALTY_ENTITY.setEntryExitCodeMismatchPenalty(BigDecimal.valueOf(100));

    QUERY_ROUND_RULE.setElectronicMoneyType(ElectronicMoneyType.EASY_CARD);
    QUERY_ROUND_RULE.setValue(5);
    QUERY_ROUND_RULE.setThreshold(100_000_000);

    Mockito.when(MOCK_ROUND_RULE.getId()).thenReturn(1L);
    Mockito.when(MOCK_ROUND_RULE.getElectronicMoneyType()).thenReturn(ElectronicMoneyType.I_PASS);
    Mockito.when(MOCK_ROUND_RULE.getValue()).thenReturn(QUERY_ROUND_RULE.getValue());
    Mockito.when(MOCK_ROUND_RULE.getThreshold()).thenReturn(QUERY_ROUND_RULE.getThreshold());
  }

  private CoBrandedCardTransferBonus getBonusEntity(int num) throws Exception {
    Field id = BaseEntity.class.getDeclaredField("id");
    id.setAccessible(true);
    Field dataVersion = AggregateRoot.class.getDeclaredField("dataVersion");
    dataVersion.setAccessible(true);
    CoBrandedCardTransferBonus bonus = new CoBrandedCardTransferBonus();
    id.set(bonus, num + 1L);
    dataVersion.set(bonus, 0L);
    bonus.setCoBrandedCard(CARD_DATA.ENTITIES.get(0));
    bonus.setProfile(PROFILE_DATA.ENTITIES.get(0));
    bonus.setTransportMode(transportModes.get(num));
    bonus.setDiscountAmt(BigDecimal.valueOf(20));

    return bonus;
  }

  private void setOutputs() {
    OUTPUT.setCoBrandedCardId(CARD_DATA.COMMON_OUTPUTS.get(0).getId());
    OUTPUT.setCoBrandedCardType(CARD_DATA.COMMON_OUTPUTS.get(0).getType());
    OUTPUT.getRoutes().addAll(CARD_DATA.OPERATE_ROUTE_OUTPUTS);
    IntStream.range(0, 6).forEach(e -> OUTPUT.getTransferBonuses().add(getBonusOutput(e)));

    CoBrandedCardProfileDiscountOutput discount = new CoBrandedCardProfileDiscountOutput();
    discount.setId(DISCOUNT_ENTITY.getId());
    discount.setDataVersion(DISCOUNT_ENTITY.getDataVersion());
    discount.setDiscountPct(DISCOUNT_ENTITY.getDiscountPct());
    discount.setProfileId(1L);
    discount.setProfileName(PROFILE_DATA.ENTITIES.get(0).getName());
    discount.setMunicipalityId(1L);
    discount.setMunicipalityName(MUNICIPALITY_DATA.ENTITIES.get(0).getName());
    OUTPUT.getProfileDiscounts().add(discount);

    CoBrandedCardPenaltyPolicyOutput penalty = new CoBrandedCardPenaltyPolicyOutput();
    penalty.setId(PENALTY_ENTITY.getId());
    penalty.setDataVersion(PENALTY_ENTITY.getDataVersion());
    penalty.setOvertimePenalty(PENALTY_ENTITY.getOvertimePenalty());
    penalty.setSameStationEntryExitPenalty(PENALTY_ENTITY.getSameStationEntryExitPenalty());
    penalty.setEntryExitCodeMismatchPenalty(PENALTY_ENTITY.getEntryExitCodeMismatchPenalty());
    OUTPUT.setPenaltyPolicy(penalty);
  }

  private CoBrandedCardTransferBonusOutput getBonusOutput(int num) {

    CoBrandedCardTransferBonusOutput bonus = new CoBrandedCardTransferBonusOutput();
    bonus.setId(num + 1L);
    bonus.setDataVersion(0L);
    bonus.setProfileId(1L);
    bonus.setProfileName(PROFILE_DATA.ENTITIES.get(0).getName());
    bonus.setTransport(transportModes.get(num));
    bonus.setDiscountAmt(BigDecimal.valueOf(20));

    return bonus;
  }

  private void setInputs() {
    INPUT.setCoBrandedCardId(CARD_DATA.COMMON_OUTPUTS.get(0).getId());
    INPUT.getRoutes().addAll(CARD_DATA.OPERATE_ROUTE_INPUTS);
    IntStream.range(0, 6).forEach(e -> INPUT.getTransferBonuses().add(getBonusInput(e)));

    CoBrandedCardProfileDiscountInput discount = new CoBrandedCardProfileDiscountInput();
    discount.setId(DISCOUNT_ENTITY.getId());
    discount.setDataVersion(DISCOUNT_ENTITY.getDataVersion());
    discount.setDiscountPct(DISCOUNT_ENTITY.getDiscountPct());
    discount.setProfileId(1L);
    discount.setMunicipalityId(1L);
    INPUT.getProfileDiscounts().add(discount);

    CoBrandedCardPenaltyPolicyInput penalty = new CoBrandedCardPenaltyPolicyInput();
    penalty.setId(PENALTY_ENTITY.getId());
    penalty.setDataVersion(PENALTY_ENTITY.getDataVersion());
    penalty.setOvertimePenalty(PENALTY_ENTITY.getOvertimePenalty());
    penalty.setSameStationEntryExitPenalty(PENALTY_ENTITY.getSameStationEntryExitPenalty());
    penalty.setEntryExitCodeMismatchPenalty(PENALTY_ENTITY.getEntryExitCodeMismatchPenalty());
    INPUT.setPenaltyPolicy(penalty);
  }

  private CoBrandedCardTransferBonusInput getBonusInput(int num) {

    CoBrandedCardTransferBonusInput bonus = new CoBrandedCardTransferBonusInput();
    bonus.setId(num + 1L);
    bonus.setDataVersion(0L);
    bonus.setProfileId(1L);
    bonus.setTransport(transportModes.get(num));
    bonus.setDiscountAmt(BigDecimal.valueOf(20));

    return bonus;
  }
}
