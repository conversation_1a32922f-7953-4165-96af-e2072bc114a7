package com.ibm.tw.thsrc.bsm.fare.translate;

import static org.junit.jupiter.api.Assertions.*;

import com.ibm.tw.thsrc.bsm.fa.dto.FreeSeatingTicketTypeOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.FreeSeatingTicketType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class FreeSeatingTicketTypeTranslatorTest {

  @Test
  void toFreeSeatingTicketTypeOutput() {

    FreeSeatingTicketType entity = new FreeSeatingTicketType();
    entity.setCode("code");
    FreeSeatingTicketTypeOutput output = FreeSeatingTicketTypeTranslator.toFreeSeatingTicketTypeOutput(entity);
    Assertions.assertEquals(output.getCode(), entity.getCode());
  }
}