/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.fa.dto.FareEvaluationInput;
import com.ibm.tw.thsrc.bsm.fa.dto.FareEvaluationOutput;
import com.ibm.tw.thsrc.bsm.sc.dto.StationPairOutput;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FareEvaluationTestData {

  public List<FareEvaluationOutput> OUTPUTS = new ArrayList<>();
  public Map<Long, BigDecimal> FARE_PRICES = new HashMap<>();
  public FareEvaluationInput INPUT = new FareEvaluationInput();
  public List<FareEvaluationInput> INVALID_INPUTS = new ArrayList<>();

  public FareEvaluationTestData() {
    setOutputs();
    setInputs();
  }

  private void setOutputs() {

    FareEvaluationOutput output1 = new FareEvaluationOutput();
    StationPairOutput pairOutput1 = new StationPairOutput();
    pairOutput1.setId(1L);
    pairOutput1.setDepartureStationCode("NAK");
    pairOutput1.setArrivalStationCode("ZUY");
    pairOutput1.setDepartureStationName("南港");
    pairOutput1.setArrivalStationName("左營");

    output1.setStationPair(pairOutput1);
    output1.setUnitPrice(BigDecimal.valueOf(200.00));

    FareEvaluationOutput output2 = new FareEvaluationOutput();
    StationPairOutput pairOutput2 = new StationPairOutput();
    pairOutput2.setId(2L);
    pairOutput2.setDepartureStationCode("TPE");
    pairOutput2.setArrivalStationCode("ZUY");
    pairOutput2.setDepartureStationName("台北");
    pairOutput2.setArrivalStationName("左營");

    output2.setStationPair(pairOutput2);
    output2.setUnitPrice(BigDecimal.valueOf(180.00));

    OUTPUTS.add(output1);
    OUTPUTS.add(output2);

    FARE_PRICES.put(1L, BigDecimal.valueOf(200.00));
    FARE_PRICES.put(2L, BigDecimal.valueOf(180.00));
  }

  private void setInputs() {
    INPUT.setFarePlanCode("YOFC");
    INPUT.setProfileCode("F");
    INPUT.setTrainDate(LocalDate.of(2022, 10, 1));

    FareEvaluationInput inputWithoutFarePlanCode = new FareEvaluationInput();
    inputWithoutFarePlanCode.setProfileCode("F");
    inputWithoutFarePlanCode.setTrainDate(LocalDate.of(2022, 10, 1));

    FareEvaluationInput inputWithBlankFarePlanCode = new FareEvaluationInput();
    inputWithBlankFarePlanCode.setFarePlanCode("");
    inputWithBlankFarePlanCode.setProfileCode("F");
    inputWithBlankFarePlanCode.setTrainDate(LocalDate.of(2022, 10, 1));

    FareEvaluationInput inputOnlySpaceFarePlanCode = new FareEvaluationInput();
    inputOnlySpaceFarePlanCode.setFarePlanCode(" ");
    inputOnlySpaceFarePlanCode.setProfileCode("F");
    inputOnlySpaceFarePlanCode.setTrainDate(LocalDate.of(2022, 10, 1));

    FareEvaluationInput inputWithoutProfileCode = new FareEvaluationInput();
    inputWithoutProfileCode.setFarePlanCode("YOFC");
    inputWithoutProfileCode.setTrainDate(LocalDate.of(2022, 10, 1));

    FareEvaluationInput inputWithBlankProfileCode = new FareEvaluationInput();
    inputWithBlankProfileCode.setFarePlanCode("YOFC");
    inputWithBlankProfileCode.setProfileCode("");
    inputWithBlankProfileCode.setTrainDate(LocalDate.of(2022, 10, 1));

    FareEvaluationInput inputOnlySpaceProfileCode = new FareEvaluationInput();
    inputOnlySpaceProfileCode.setFarePlanCode("YOFC");
    inputOnlySpaceProfileCode.setProfileCode(" ");
    inputOnlySpaceProfileCode.setTrainDate(LocalDate.of(2022, 10, 1));

    FareEvaluationInput inputWithoutTrainDate = new FareEvaluationInput();
    inputWithoutTrainDate.setFarePlanCode("YOFC");
    inputWithoutTrainDate.setProfileCode("F");

    INVALID_INPUTS.add(inputWithoutFarePlanCode);
    INVALID_INPUTS.add(inputWithBlankFarePlanCode);
    INVALID_INPUTS.add(inputOnlySpaceFarePlanCode);
    INVALID_INPUTS.add(inputWithoutProfileCode);
    INVALID_INPUTS.add(inputWithBlankProfileCode);
    INVALID_INPUTS.add(inputOnlySpaceProfileCode);
    INVALID_INPUTS.add(inputWithoutTrainDate);
  }

}
