/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.core.entity.AggregateRoot;
import com.ibm.tw.thsrc.bsm.core.entity.BaseEntity;
import com.ibm.tw.thsrc.bsm.fa.dto.ColorInput;
import com.ibm.tw.thsrc.bsm.fa.dto.ColorOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.FareProjectInput;
import com.ibm.tw.thsrc.bsm.fa.dto.FareProjectOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.ProjectFlagSettingOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.ProjectOverviewOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.ProjectSalesChannelFlagSettingInput;
import com.ibm.tw.thsrc.bsm.fa.dto.ProjectSalesChannelFlagSettingOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.FarePlanType;
import com.ibm.tw.thsrc.bsm.fa.enums.FareProjectType;
import com.ibm.tw.thsrc.bsm.fa.enums.FareType;
import com.ibm.tw.thsrc.bsm.fa.enums.ProjectFlag;
import com.ibm.tw.thsrc.bsm.fa.enums.ProjectFlagChangeRuleType;
import com.ibm.tw.thsrc.bsm.fa.enums.ProjectFlagRefundRuleType;
import com.ibm.tw.thsrc.bsm.fa.enums.ProjectSalesChannelFlag;
import com.ibm.tw.thsrc.bsm.fa.enums.ServiceType;
import com.ibm.tw.thsrc.bsm.fa.enums.TicketEndorsementType;
import com.ibm.tw.thsrc.bsm.fare.entity.BasicFarePlan;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProject;
import com.ibm.tw.thsrc.bsm.fare.entity.FareProjectMapping;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerProfile;
import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountPlan;
import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountType;
import com.ibm.tw.thsrc.bsm.fare.entity.ProjectFlagSetting;
import com.ibm.tw.thsrc.bsm.fare.entity.ProjectSalesChannelFlagSetting;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan;
import com.ibm.tw.thsrc.bsm.util.BeanUtils;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.IntStream;

public class FareProjectTestData {

  public static final FareProjectInput VALID_INPUT = new FareProjectInput();
  public static final FareProjectInput INVALID_COLOR_INPUT = new FareProjectInput();
  public static final FareProjectInput INVALID_AI_VALUE_INPUT = new FareProjectInput();
  public List<FareProject> ENTITIES = new LinkedList<>();
  public FareProject FAKE_PROJECT = new FareProject();
  public List<FareProjectMapping> MAPPING_ENTITIES = new LinkedList<>();
  public PromotionPlan PROMOTION = new PromotionPlan();
  public BasicFarePlan BASIC_PLAN = new BasicFarePlan();
  public ProfileDiscountPlan PROFILE_PLAN = new ProfileDiscountPlan();
  public List<FareProjectOutput> OUTPUTS = new LinkedList<>();
  public List<ProjectOverviewOutput> OVERVIEW_OUTPUTS = new LinkedList<>();

  public FareProjectInput CREATE_INPUT = new FareProjectInput();
  public FareProjectInput UPDATE_INPUT = new FareProjectInput();
  public ProjectSalesChannelFlagSettingInput UPSERT_CHANNEL_FLAG_INPUT = new ProjectSalesChannelFlagSettingInput();
  public FareProjectInput UPDATE_CHANGE_CODE_INPUT = new FareProjectInput();
  public FareProjectInput CREATE_DUPLICATE_CODE_INPUT = new FareProjectInput();
  public FareProjectInput CREATE_DUPLICATE_AI_INPUT = new FareProjectInput();

  public FareProjectTestData() throws Exception {
    setEntities();
    setOutputs();
    createInputTestData();
  }

  private void setEntities() throws Exception {
    IntStream.range(1, 4).forEach(e -> {
      FareProject entity = getBasicEntity(e);
      // 3,4 沒設定過 TWE 欄位設定
      if (e > 2) {
        entity.getSalesChannelFlagSettings().clear();
      }
      ENTITIES.add(entity);
    });

    // set fake data for overview
    Field id = BaseEntity.class.getDeclaredField("id");
    id.setAccessible(true);
    Field dataVersion = AggregateRoot.class.getDeclaredField("dataVersion");
    dataVersion.setAccessible(true);

    FAKE_PROJECT = getBasicEntity(1);
    id.set(FAKE_PROJECT, 1L);
    dataVersion.set(FAKE_PROJECT, 0L);

    id.set(PROMOTION, 1L);
    dataVersion.set(PROMOTION, 0L);
    PROMOTION.setCode("P000");
    PROMOTION.setType(FarePlanType.PROMOTION);

    id.set(BASIC_PLAN, 1L);
    dataVersion.set(BASIC_PLAN, 0L);
    BASIC_PLAN.setCode("Y000");
    BASIC_PLAN.setServiceType(ServiceType.RESERVED);
    BASIC_PLAN.setFareType(FareType.FULL_FARE);
    BASIC_PLAN.setType(FarePlanType.BASIC_FARE_PLAN);

    id.set(PROFILE_PLAN, 1L);
    dataVersion.set(PROFILE_PLAN, 0L);
    PassengerProfile profile = new PassengerProfile();
    id.set(profile, 1L);
    dataVersion.set(profile, 0L);
    profile.setCode("F");
    profile.setZhPrintName("成人");
    ProfileDiscountType discountType = new ProfileDiscountType();
    id.set(discountType, 1L);
    dataVersion.set(discountType, 0L);
    discountType.setCode("A");
    PROFILE_PLAN.setPassengerProfile(profile);
    PROFILE_PLAN.setProfileDiscountType(discountType);

    FareProjectMapping mapping1 = new FareProjectMapping();
    id.set(mapping1, 1L);
    dataVersion.set(mapping1, 0L);
    mapping1.setFareProject(FAKE_PROJECT);
    mapping1.setPromotionPlan(PROMOTION);
    mapping1.setBasicFarePlan(BASIC_PLAN);
    mapping1.setProfileDiscountPlan(PROFILE_PLAN);

    MAPPING_ENTITIES.add(mapping1);
  }

  private FareProject getBasicEntity(int num) {
    FareProject entity = new FareProject();
    entity.setName("測試專案" + num);
    entity.setCode("Y21" + num);
    entity.setType(FareProjectType.STANDARD);
    entity.setEnName("project" + num);
    entity.setZhName("專案" + num);
    entity.setTicketEndorsementType(TicketEndorsementType.ENDORSEMENT_1);
    entity.setTextColor("0,0,0");
    entity.setBackgroundColor("0,0,0");
    entity.setAiValue(String.valueOf(num));

    List<ProjectFlagSetting> settings = new ArrayList<>();
    ProjectFlagSetting setting1 = new ProjectFlagSetting();
    setting1.setFareProject(entity);
    setting1.setFlag(ProjectFlag.SMIS_CHECK_S);
    setting1.setSetting(String.valueOf(Boolean.TRUE));
    settings.add(setting1);

    ProjectFlagSetting setting2 = new ProjectFlagSetting();
    setting2.setFareProject(entity);
    setting2.setFlag(ProjectFlag.ID_DIGIT_DISPLAY_P);
    setting2.setSetting(String.valueOf(Boolean.TRUE));
    settings.add(setting2);

    ProjectFlagSetting setting3 = new ProjectFlagSetting();
    setting3.setFareProject(entity);
    setting3.setFlag(ProjectFlag.TICKET_BOOTH_LIGHT_T);
    setting3.setSetting(String.valueOf(Boolean.FALSE));
    settings.add(setting3);

    ProjectFlagSetting setting4 = new ProjectFlagSetting();
    setting4.setFareProject(entity);
    setting4.setFlag(ProjectFlag.NO_REISSUE_N);
    setting4.setSetting(String.valueOf(Boolean.TRUE));
    settings.add(setting4);

    entity.getFlagSettings().addAll(settings);

    List<ProjectSalesChannelFlagSetting> channelSettings = new ArrayList<>();
    ProjectSalesChannelFlagSetting channelSetting1 = new ProjectSalesChannelFlagSetting();
    channelSetting1.setFareProject(entity);
    channelSetting1.setFlag(ProjectSalesChannelFlag.TWE_COUPON_CODE_REQ);
    channelSetting1.setSetting(Boolean.TRUE);
    channelSettings.add(channelSetting1);

    ProjectSalesChannelFlagSetting channelSetting2 = new ProjectSalesChannelFlagSetting();
    channelSetting2.setFareProject(entity);
    channelSetting2.setFlag(ProjectSalesChannelFlag.TWE_MEMBER_POINT_GIVEN);
    channelSetting2.setSetting(Boolean.TRUE);
    channelSettings.add(channelSetting2);

    ProjectSalesChannelFlagSetting channelSetting3 = new ProjectSalesChannelFlagSetting();
    channelSetting3.setFareProject(entity);
    channelSetting3.setFlag(ProjectSalesChannelFlag.TWE_CPR_MEMBER_PTS_GIVEN);
    channelSetting3.setSetting(Boolean.TRUE);
    channelSettings.add(channelSetting3);

    ProjectSalesChannelFlagSetting channelSetting4 = new ProjectSalesChannelFlagSetting();
    channelSetting4.setFareProject(entity);
    channelSetting4.setFlag(ProjectSalesChannelFlag.TWE_OD_UNMODIFIABLE);
    channelSetting4.setSetting(Boolean.TRUE);
    channelSettings.add(channelSetting4);

    ProjectSalesChannelFlagSetting channelSetting5 = new ProjectSalesChannelFlagSetting();
    channelSetting5.setFareProject(entity);
    channelSetting5.setFlag(ProjectSalesChannelFlag.TWE_SEAT_MODIFIABLE);
    channelSetting5.setSetting(Boolean.TRUE);
    channelSettings.add(channelSetting5);

    ProjectSalesChannelFlagSetting channelSetting6 = new ProjectSalesChannelFlagSetting();
    channelSetting6.setFareProject(entity);
    channelSetting6.setFlag(ProjectSalesChannelFlag.TWE_ADULT_CHILD_ID_REQ);
    channelSetting6.setSetting(Boolean.FALSE);
    channelSettings.add(channelSetting6);

    ProjectSalesChannelFlagSetting channelSetting7 = new ProjectSalesChannelFlagSetting();
    channelSetting7.setFareProject(entity);
    channelSetting7.setFlag(ProjectSalesChannelFlag.TWE_MANUAL_SEATING_ONLY);
    channelSetting7.setSetting(Boolean.FALSE);
    channelSettings.add(channelSetting7);

    ProjectSalesChannelFlagSetting channelSetting8 = new ProjectSalesChannelFlagSetting();
    channelSetting8.setFareProject(entity);
    channelSetting8.setFlag(ProjectSalesChannelFlag.TWE_GROUP_CANCELABLE);
    channelSetting8.setSetting(Boolean.FALSE);
    channelSettings.add(channelSetting8);

    ProjectSalesChannelFlagSetting channelSetting9 = new ProjectSalesChannelFlagSetting();
    channelSetting9.setFareProject(entity);
    channelSetting9.setFlag(ProjectSalesChannelFlag.TWE_SIGN_PRINT_REMINDER);
    channelSetting9.setSetting(Boolean.FALSE);
    channelSettings.add(channelSetting9);

    ProjectSalesChannelFlagSetting channelSetting10 = new ProjectSalesChannelFlagSetting();
    channelSetting10.setFareProject(entity);
    channelSetting10.setFlag(ProjectSalesChannelFlag.TWE_CURRENT_DAY_TRAIN_ONLY);
    channelSetting10.setSetting(Boolean.FALSE);
    channelSettings.add(channelSetting10);

    entity.getSalesChannelFlagSettings().addAll(channelSettings);
    return entity;
  }

  private void setOutputs() {
    IntStream.range(1, 4).forEach(e -> {
      FareProjectOutput output = getBasicOutput(e);
      // 3,4 沒設定過 TWE 欄位設定
      if (e > 2) {
        output.setProjectSalesChannelFlagSettings(null);
      }
      OUTPUTS.add(output);
    });

    // overview output
    ProjectOverviewOutput overview = new ProjectOverviewOutput();
    overview.setProjectInfo(getBasicOutput(1));
    overview.setStandardProjectCode("Y211");
    overview.setBasicFarePlanCode("Y000");
    overview.setPromotionPlanCode("P000");
    overview.setProfileDiscountTypeCode("A");
    overview.setPassengerProfileCode("F");
    overview.setPassengerProfileName("成人");

    OVERVIEW_OUTPUTS.add(overview);
  }

  private FareProjectOutput getBasicOutput(int num) {
    FareProjectOutput out = new FareProjectOutput();
    out.setId(num);
    out.setDataVersion(0L);
    out.setName("測試專案" + num);
    out.setCode("Y21" + num);
    out.setType(FareProjectType.STANDARD);
    out.setEnName("project" + num);
    out.setZhName("專案" + num);

    ProjectFlagSettingOutput setting = new ProjectFlagSettingOutput();
    setting.setTicketEndorsementType(TicketEndorsementType.ENDORSEMENT_1);
    setting.setIdDigitDisplay(Boolean.TRUE);
    setting.setSmisCheck(Boolean.TRUE);
    setting.setTbLight(Boolean.FALSE);
    setting.setNoReissue(Boolean.TRUE);
    setting.setAiValue(String.valueOf(num));
    ColorOutput color = new ColorOutput();
    color.setRed("0");
    color.setGreen("0");
    color.setBlue("0");
    setting.setTextColor(color);
    setting.setBackgroundColor(color);
    out.setProjectFlagSettings(setting);

    ProjectSalesChannelFlagSettingOutput channelSettings = new ProjectSalesChannelFlagSettingOutput();
    channelSettings.setCouponRequired(Boolean.TRUE);
    channelSettings.setMemberPointGiven(Boolean.TRUE);
    channelSettings.setCprMemberPointGiven(Boolean.TRUE);
    channelSettings.setOdUnmodified(Boolean.TRUE);
    channelSettings.setSeatModifiable(Boolean.TRUE);
    channelSettings.setAdultChildIdRequired(Boolean.FALSE);
    channelSettings.setManualSeatOnly(Boolean.FALSE);
    channelSettings.setGroupCancelable(Boolean.FALSE);
    channelSettings.setSignPrintReminder(Boolean.FALSE);
    channelSettings.setCurrentDayTrainOnly(Boolean.FALSE);
    out.setProjectSalesChannelFlagSettings(channelSettings);

    return out;
  }

  public void createInputTestData() {

    FareProjectInput commonTestData = new FareProjectInput();
    commonTestData.setDataVersion(0L);
    commonTestData.setCode("YO51");
    commonTestData.setName("學生五折");
    commonTestData.setType(FareProjectType.STANDARD);
    commonTestData.setZhName("學生五折");
    commonTestData.setEnName("STUDENT");
    commonTestData.setAiValue("6");

    commonTestData.setRefundRule(ProjectFlagRefundRuleType.REFUND_RULE_E);
    commonTestData.setChangeRule(ProjectFlagChangeRuleType.CHANGE_RULE_I);
    commonTestData.setIdDigitDisplay(true);
    commonTestData.setSmisCheck(true);
    commonTestData.setTbLight(false);
    commonTestData.setNoReissue(false);
    commonTestData.setTicketEndorsementType(TicketEndorsementType.ENDORSEMENT_1);

    ColorInput bgColor = new ColorInput();
    bgColor.setRed(255);
    bgColor.setGreen(255);
    bgColor.setBlue(255);

    ColorInput txtColor = new ColorInput();
    txtColor.setRed(0);
    txtColor.setGreen(0);
    txtColor.setBlue(0);

    commonTestData.setBackgroundColor(bgColor);
    commonTestData.setTextColor(txtColor);

    BeanUtils.copyProperties(VALID_INPUT, commonTestData);
    VALID_INPUT.setBackgroundColor(bgColor);
    VALID_INPUT.setTextColor(txtColor);

    BeanUtils.copyProperties(INVALID_COLOR_INPUT, commonTestData);
    INVALID_COLOR_INPUT.setTextColor(createInvalidColorInput());
    INVALID_COLOR_INPUT.setBackgroundColor(txtColor);

    BeanUtils.copyProperties(INVALID_AI_VALUE_INPUT, commonTestData);
    INVALID_AI_VALUE_INPUT.setBackgroundColor(bgColor);
    INVALID_AI_VALUE_INPUT.setTextColor(txtColor);
    INVALID_AI_VALUE_INPUT.setAiValue("");

    BeanUtils.copyProperties(CREATE_INPUT, commonTestData);
    CREATE_INPUT.setCode("Y215");

    BeanUtils.copyProperties(UPDATE_INPUT, commonTestData);
    UPDATE_INPUT.setCode("Y211");
    UPDATE_INPUT.setAiValue("A");

    BeanUtils.copyProperties(UPDATE_CHANGE_CODE_INPUT, commonTestData);
    UPDATE_CHANGE_CODE_INPUT.setCode("Y212");

    BeanUtils.copyProperties(CREATE_DUPLICATE_CODE_INPUT, commonTestData);
    CREATE_DUPLICATE_CODE_INPUT.setCode("Y211");

    BeanUtils.copyProperties(CREATE_DUPLICATE_AI_INPUT, commonTestData);
    CREATE_DUPLICATE_AI_INPUT.setAiValue("1");

    ProjectSalesChannelFlagSettingInput channelFlagInput = new ProjectSalesChannelFlagSettingInput();
    channelFlagInput.setDataVersion(0L);
    channelFlagInput.setAdultChildIdRequired(Boolean.FALSE);
    channelFlagInput.setCouponRequired(Boolean.FALSE);
    channelFlagInput.setCprMemberPointGiven(Boolean.FALSE);
    channelFlagInput.setCurrentDayTrainOnly(Boolean.FALSE);
    channelFlagInput.setGroupCancelable(Boolean.FALSE);
    channelFlagInput.setManualSeatOnly(Boolean.TRUE);
    channelFlagInput.setMemberPointGiven(Boolean.TRUE);
    channelFlagInput.setSeatModifiable(Boolean.TRUE);
    channelFlagInput.setOdUnmodified(Boolean.TRUE);
    channelFlagInput.setSignPrintReminder(Boolean.TRUE);

    BeanUtils.copyProperties(UPSERT_CHANNEL_FLAG_INPUT, channelFlagInput);
  }

  public ColorInput createInvalidColorInput() {
    ColorInput invalidColorInput = new ColorInput();
    invalidColorInput.setRed(255);
    invalidColorInput.setGreen(0);
    invalidColorInput.setBlue(null);
    return invalidColorInput;
  }
}