package com.ibm.tw.thsrc.bsm.fare.translate;

import com.ibm.tw.thsrc.bsm.fa.dto.ProfileDiscountTypeOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.ProfileDiscountType;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class ProfileDiscountTypeTranslatorTest {

  @Test
  void toProfileDiscountTypeOutput() {

    ProfileDiscountType entity = new ProfileDiscountType();
    entity.setCode("code");
    ProfileDiscountTypeOutput output = ProfileDiscountTypeTranslator.toProfileDiscountTypeOutput(entity);
    Assertions.assertEquals(output.getCode(), entity.getCode());
  }
}