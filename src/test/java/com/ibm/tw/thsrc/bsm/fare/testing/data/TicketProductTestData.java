/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketProductInput;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketProductOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.TicketProduct;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class TicketProductTestData {

  public final LocalDate DATE1 = LocalDate.now().minusDays(2);
  public final LocalDate DATE2 = LocalDate.now();
  public final LocalDate DATE3 = LocalDate.now().plusDays(1);
  public final LocalDate DATE4 = LocalDate.now().plusDays(3);
  public List<TicketProduct> ENTITIES = new ArrayList<>();
  public Set<TicketProductOutput> UPSERT_OUTPUTS = new HashSet<>();
  public List<TicketProductOutput> RECENT_OUTPUTS = new ArrayList<>();
  public List<TicketProductOutput> FUTURE_OUTPUTS = new ArrayList<>();
  public CollectionModificationInput<TicketProductInput> CREATE_INPUT = new CollectionModificationInput<>();
  public CollectionModificationInput<TicketProductInput> UPDATE_INPUT = new CollectionModificationInput<>();
  public TicketProductInput NOT_EXIST_INPUT = new TicketProductInput();
  public List<CollectionModificationInput<TicketProductInput>> INVALID_INPUTS = new ArrayList<>();

  public TicketProductTestData() {
    setEntities();
    setOutputs();
    setInputs();
  }

  public List<TicketProductOutput> sorting(List<TicketProductOutput> filteredOutput,
      String sortByKey, String order) {
    List<TicketProductOutput> orderOutput = new ArrayList<>(filteredOutput);

    if (order.equals(Search.ASCENDING)) {
      if (sortByKey.equals("ticketId")) {
        orderOutput.sort(Comparator.comparing(TicketProductOutput::getTicketId));

      } else if (sortByKey.equals("enName")) {
        orderOutput.sort(Comparator.comparing(TicketProductOutput::getEnName));

      } else if (sortByKey.equals("zhName")) {
        orderOutput.sort(Comparator.comparing(TicketProductOutput::getZhName));

      } else if (sortByKey.equals("enPrintName")) {
        orderOutput.sort(Comparator.comparing(TicketProductOutput::getEnPrintName));

      } else if (sortByKey.equals("zhPrintName")) {
        orderOutput.sort(Comparator.comparing(TicketProductOutput::getZhPrintName));
      }

    } else if (order.equals(Search.DESCENDING)) {
      if (sortByKey.equals("ticketId")) {
        orderOutput.sort(Comparator.comparing(TicketProductOutput::getTicketId).reversed());

      } else if (sortByKey.equals("enName")) {
        orderOutput.sort(Comparator.comparing(TicketProductOutput::getEnName).reversed());

      } else if (sortByKey.equals("zhName")) {
        orderOutput.sort(Comparator.comparing(TicketProductOutput::getZhName).reversed());

      } else if (sortByKey.equals("enPrintName")) {
        orderOutput.sort(Comparator.comparing(TicketProductOutput::getEnPrintName).reversed());

      } else if (sortByKey.equals("zhPrintName")) {
        orderOutput.sort(Comparator.comparing(TicketProductOutput::getZhPrintName).reversed());
      }
    }

    return orderOutput;
  }

  private void setEntities() {
    TicketProduct ticket1 = new TicketProduct();
    ticket1.setTicketId(1);
    ticket1.setEnName("TEST1");
    ticket1.setZhName("測試票種一");
    ticket1.setEnPrintName("TES1");
    ticket1.setZhPrintName("測試一");
    ticket1.setEffDate(DATE1);

    TicketProduct ticket2 = new TicketProduct();
    ticket2.setTicketId(2);
    ticket2.setEnName("TEST2");
    ticket2.setZhName("測試票種二");
    ticket2.setEnPrintName("TES2");
    ticket2.setZhPrintName("測試二");
    ticket2.setEffDate(DATE1);

    TicketProduct ticket3 = new TicketProduct();
    ticket3.setTicketId(1);
    ticket3.setEnName("TEST1");
    ticket3.setZhName("測試票種一");
    ticket3.setEnPrintName("TES1");
    ticket3.setZhPrintName("測試一");
    ticket3.setEffDate(DATE2);

    TicketProduct ticket4 = new TicketProduct();
    ticket4.setTicketId(2);
    ticket4.setEnName("TEST2");
    ticket4.setZhName("測試票種二");
    ticket4.setEnPrintName("TES2");
    ticket4.setZhPrintName("測試二");
    ticket4.setEffDate(DATE2);

    TicketProduct ticket5 = new TicketProduct();
    ticket5.setTicketId(1);
    ticket5.setEnName("TEST1");
    ticket5.setZhName("測試票種一");
    ticket5.setEnPrintName("TES1");
    ticket5.setZhPrintName("測試一");
    ticket5.setEffDate(DATE3);

    TicketProduct ticket6 = new TicketProduct();
    ticket6.setTicketId(2);
    ticket6.setEnName("TEST2");
    ticket6.setZhName("測試票種二");
    ticket6.setEnPrintName("TES2");
    ticket6.setZhPrintName("測試二");
    ticket6.setEffDate(DATE3);

    ENTITIES.add(ticket1);
    ENTITIES.add(ticket2);
    ENTITIES.add(ticket3);
    ENTITIES.add(ticket4);
    ENTITIES.add(ticket5);
    ENTITIES.add(ticket6);
  }

  private void setOutputs() {

    TicketProductOutput recent1 = new TicketProductOutput();
    recent1.setId(3L);
    recent1.setTicketId(1);
    recent1.setEnName("TEST1");
    recent1.setZhName("測試票種一");
    recent1.setEnPrintName("TES1");
    recent1.setZhPrintName("測試一");
    recent1.setDataVersion(0L);
    recent1.setEffDate(DATE2);

    TicketProductOutput recent2 = new TicketProductOutput();
    recent2.setId(4L);
    recent2.setTicketId(2);
    recent2.setEnName("TEST2");
    recent2.setZhName("測試票種二");
    recent2.setEnPrintName("TES2");
    recent2.setZhPrintName("測試二");
    recent2.setDataVersion(0L);
    recent2.setEffDate(DATE2);

    RECENT_OUTPUTS.add(recent1);
    RECENT_OUTPUTS.add(recent2);

    TicketProductOutput future1 = new TicketProductOutput();
    future1.setId(5L);
    future1.setTicketId(1);
    future1.setEnName("TEST1");
    future1.setZhName("測試票種一");
    future1.setEnPrintName("TES1");
    future1.setZhPrintName("測試一");
    future1.setDataVersion(0L);
    future1.setEffDate(DATE3);

    TicketProductOutput future2 = new TicketProductOutput();
    future2.setId(6L);
    future2.setTicketId(2);
    future2.setEnName("TEST2");
    future2.setZhName("測試票種二");
    future2.setEnPrintName("TES2");
    future2.setZhPrintName("測試二");
    future2.setDataVersion(0L);
    future2.setEffDate(DATE3);

    FUTURE_OUTPUTS.add(future1);
    FUTURE_OUTPUTS.add(future2);

    UPSERT_OUTPUTS.add(recent1);
    UPSERT_OUTPUTS.add(recent2);

  }

  private void setInputs() {
    TicketProductInput create1 = new TicketProductInput();
    create1.setTicketId(1);
    create1.setEnName("TEST1");
    create1.setZhName("測試票種一");
    create1.setEnPrintName("TES1");
    create1.setZhPrintName("測試一");
    create1.setEffDate(DATE4);

    TicketProductInput create2 = new TicketProductInput();
    create2.setTicketId(2);
    create2.setEnName("TEST2");
    create2.setZhName("測試票種二");
    create2.setEnPrintName("TES2");
    create2.setZhPrintName("測試二");
    create2.setEffDate(DATE4);

    CREATE_INPUT.getCreations().add(create1);
    CREATE_INPUT.getCreations().add(create2);

    TicketProductInput update1 = new TicketProductInput();
    update1.setDataVersion(0L);
    update1.setTicketId(1);
    update1.setEnName("TEST1");
    update1.setZhName("測試票一");
    update1.setEnPrintName("TES1");
    update1.setZhPrintName("測試一");
    update1.setEffDate(DATE3);

    TicketProductInput update2 = new TicketProductInput();
    update2.setDataVersion(0L);
    update2.setTicketId(2);
    update2.setEnName("TEST2");
    update2.setZhName("測試票二");
    update2.setEnPrintName("TES2");
    update2.setZhPrintName("測試二");
    update2.setEffDate(DATE3);

    NOT_EXIST_INPUT = update1;
    UPDATE_INPUT.getReplacements().put(3L, update1);
    UPDATE_INPUT.getReplacements().put(4L, update2);

    TicketProductInput inputWithoutEffDate = new TicketProductInput();
    inputWithoutEffDate.setDataVersion(0L);
    inputWithoutEffDate.setTicketId(2);
    inputWithoutEffDate.setEnName("TEST2");
    inputWithoutEffDate.setZhName("測試票二");
    inputWithoutEffDate.setEnPrintName("TES2");
    inputWithoutEffDate.setZhPrintName("測試二");

    TicketProductInput inputWithoutTicketId = new TicketProductInput();
    inputWithoutTicketId.setDataVersion(0L);
    inputWithoutTicketId.setEnName("TEST2");
    inputWithoutTicketId.setZhName("測試票二");
    inputWithoutTicketId.setEnPrintName("TES2");
    inputWithoutTicketId.setZhPrintName("測試二");
    inputWithoutTicketId.setEffDate(DATE3);

    TicketProductInput inputWithNameOver20 = new TicketProductInput();
    inputWithNameOver20.setDataVersion(0L);
    inputWithNameOver20.setTicketId(2);
    inputWithNameOver20.setEnName("TESTing For name over 20 characters");
    inputWithNameOver20.setZhName("測試票二");
    inputWithNameOver20.setEnPrintName("TES2");
    inputWithNameOver20.setZhPrintName("測試二");
    inputWithNameOver20.setEffDate(DATE3);

    TicketProductInput inputWithPrintNameOver6 = new TicketProductInput();
    inputWithPrintNameOver6.setDataVersion(0L);
    inputWithPrintNameOver6.setTicketId(2);
    inputWithPrintNameOver6.setEnName("TEST2");
    inputWithPrintNameOver6.setZhName("測試票二");
    inputWithPrintNameOver6.setEnPrintName("TESTing2");
    inputWithPrintNameOver6.setZhPrintName("測試二");
    inputWithPrintNameOver6.setEffDate(DATE3);

    CollectionModificationInput<TicketProductInput> invalid1 = new CollectionModificationInput<>();
    invalid1.getCreations().add(inputWithoutEffDate);
    CollectionModificationInput<TicketProductInput> invalid2 = new CollectionModificationInput<>();
    invalid2.getCreations().add(inputWithoutTicketId);
    CollectionModificationInput<TicketProductInput> invalid3 = new CollectionModificationInput<>();
    invalid3.getCreations().add(inputWithNameOver20);
    CollectionModificationInput<TicketProductInput> invalid4 = new CollectionModificationInput<>();
    invalid4.getCreations().add(inputWithPrintNameOver6);

    INVALID_INPUTS.add(invalid1);
    INVALID_INPUTS.add(invalid2);
    INVALID_INPUTS.add(invalid3);
    INVALID_INPUTS.add(invalid4);
  }
}
