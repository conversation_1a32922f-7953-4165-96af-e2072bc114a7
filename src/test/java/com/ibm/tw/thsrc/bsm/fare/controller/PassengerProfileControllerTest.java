/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.PassengerProfileOutput;
import com.ibm.tw.thsrc.bsm.fare.service.PassengerProfileCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.PassengerProfileQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.PassengerProfileTestData;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest(classes = {FareApplication.class})
@AutoConfigureMockMvc
@ActiveProfiles("test")
class PassengerProfileControllerTest {

  private static PassengerProfileTestData TEST_DATA = new PassengerProfileTestData();
  @MockBean
  PassengerProfileCommandService commandService;
  @MockBean
  PassengerProfileQueryService queryService;
  @Autowired
  private PassengerProfileController controller;

  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any()))
        .thenReturn(new PageImpl<>(TEST_DATA.OUTPUTS));
    Page<PassengerProfileOutput> page = controller.search(new Search());
    Assertions.assertEquals(TEST_DATA.OUTPUTS.size(), page.getContent().size());
  }

  @Test
  void modify() {
    Set<Long> ids = new HashSet<>(Arrays.asList(1L, 2L));
    Set<PassengerProfileOutput> readSet = new HashSet<>(TEST_DATA.OUTPUTS);
    Mockito.when(commandService.patch(TEST_DATA.CONTROLLER_UPDATE_INPUT)).thenReturn(ids);
    Mockito.when(queryService.read(ids)).thenReturn(readSet);
    Set<PassengerProfileOutput> page = controller.modify(TEST_DATA.CONTROLLER_UPDATE_INPUT);
    Assertions.assertEquals(TEST_DATA.OUTPUTS.size(), page.size());
  }
}
