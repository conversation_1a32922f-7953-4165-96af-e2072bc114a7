/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.fa.dto.RefundFeeRuleInput;
import com.ibm.tw.thsrc.bsm.fa.dto.RefundFeeRuleLevelInput;
import com.ibm.tw.thsrc.bsm.fa.dto.RefundFeeRuleLevelOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.RefundFeeRuleOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.RegularTicketRuleInput;
import com.ibm.tw.thsrc.bsm.fa.dto.RegularTicketRuleOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketProductPolicyInput;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketProductPolicyOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.RefundFeeCategory;
import com.ibm.tw.thsrc.bsm.fa.enums.RefundFeeRuleType;
import com.ibm.tw.thsrc.bsm.fare.entity.RefundFeeRule;
import com.ibm.tw.thsrc.bsm.fare.entity.RefundFeeRuleLevel;
import com.ibm.tw.thsrc.bsm.fare.entity.RefundFeeRuleLevelTemp;
import com.ibm.tw.thsrc.bsm.fare.entity.RegularTicketRuleFutureVersionControl;
import com.ibm.tw.thsrc.bsm.fare.entity.RegularTicketRuleTemp;
import com.ibm.tw.thsrc.bsm.fare.entity.ReissueTicketRefundFee;
import com.ibm.tw.thsrc.bsm.fare.entity.TicketProductPolicy;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class RegularTicketRuleTestData {

  public final LocalDate DATE1 = LocalDate.now().minusDays(2);
  public final LocalDate DATE2 = LocalDate.now();
  public final LocalDate DATE3 = LocalDate.now().plusDays(1);
  // for create
  public final LocalDate DATE4 = LocalDate.now().plusDays(3);
  public List<RefundFeeRule> RULE_ENTITIES = new ArrayList<>();
  public List<RefundFeeRuleLevel> LEVEL_ENTITIES = new ArrayList<>();
  public List<TicketProductPolicy> POLICY_ENTITIES = new ArrayList<>();
  public RegularTicketRuleTemp TEMP_DATA = new RegularTicketRuleTemp();
  public RegularTicketRuleFutureVersionControl FUTURE_VERSION_CONTROL = new RegularTicketRuleFutureVersionControl();


  // future version
  public RegularTicketRuleOutput FUTURE_OUTPUT = new RegularTicketRuleOutput();
  public RegularTicketRuleInput CREATE_INPUT = new RegularTicketRuleInput();
  public RegularTicketRuleInput UPDATE_INPUT = new RegularTicketRuleInput();
  public RegularTicketRuleInput UPDATE_FUTURE_INPUT = new RegularTicketRuleInput();
  public List<RegularTicketRuleInput> INVALID_INPUTS = new ArrayList<>();
  public List<RefundFeeRuleLevel> DISCONTINUOUS_LEVEL_ENTITIES = new ArrayList<>();
  public List<List<RefundFeeRuleLevel>> OVERLAP_LEVEL_ENTITIES = new ArrayList<>();

  public RegularTicketRuleTestData() {
    setEntities();
    setOutputs(true);
    setInputs();
  }

  public void resetOutput() {
    setOutputs(false);
  }

  private void setEntities() {
    RefundFeeRule rule1 = new RefundFeeRule();
    rule1.setType(RefundFeeRuleType.RULE_TYPE_A);
    rule1.setCategory(RefundFeeCategory.RESERVED_SEAT_INDIVIDUAL);

    RefundFeeRule rule2 = new RefundFeeRule();
    rule2.setType(RefundFeeRuleType.RULE_TYPE_A);
    rule2.setCategory(RefundFeeCategory.RESERVED_SEAT_GROUP);

    RefundFeeRuleLevel level1 = new RefundFeeRuleLevel();
    level1.setRefundFeeRule(rule1);
    level1.setEffDate(DATE1);
    level1.setMinPreDepDays(0);
    level1.setMaxPreDepDays(255);
    level1.setFeeAmt(BigDecimal.valueOf(20.00));
    level1.setFeePct(BigDecimal.valueOf(0.00));

    RefundFeeRuleLevel level2 = new RefundFeeRuleLevel();
    level2.setRefundFeeRule(rule2);
    level2.setEffDate(DATE1);
    level2.setMinPreDepDays(0);
    level2.setMaxPreDepDays(255);
    level2.setFeeAmt(BigDecimal.valueOf(20.00));
    level2.setFeePct(BigDecimal.valueOf(0.00));

    RefundFeeRuleLevel level3 = new RefundFeeRuleLevel();
    level3.setRefundFeeRule(rule1);
    level3.setEffDate(DATE2);
    level3.setMinPreDepDays(0);
    level3.setMaxPreDepDays(255);
    level3.setFeeAmt(BigDecimal.valueOf(20.00));
    level3.setFeePct(BigDecimal.valueOf(0.00));

    RefundFeeRuleLevel level4 = new RefundFeeRuleLevel();
    level4.setRefundFeeRule(rule2);
    level4.setEffDate(DATE2);
    level4.setMinPreDepDays(0);
    level4.setMaxPreDepDays(255);
    level4.setFeeAmt(BigDecimal.valueOf(20.00));
    level4.setFeePct(BigDecimal.valueOf(0.00));

    RefundFeeRuleLevel level5 = new RefundFeeRuleLevel();
    level5.setRefundFeeRule(rule1);
    level5.setEffDate(DATE3);
    level5.setMinPreDepDays(0);
    level5.setMaxPreDepDays(255);
    level5.setFeeAmt(BigDecimal.valueOf(20.00));
    level5.setFeePct(BigDecimal.valueOf(0.00));

    RefundFeeRuleLevel level6 = new RefundFeeRuleLevel();
    level6.setRefundFeeRule(rule2);
    level6.setEffDate(DATE3);
    level6.setMinPreDepDays(0);
    level6.setMaxPreDepDays(255);
    level6.setFeeAmt(BigDecimal.valueOf(20.00));
    level6.setFeePct(BigDecimal.valueOf(0.00));

    rule1.getRuleLevels().add(level1);
    rule1.getRuleLevels().add(level3);
    rule1.getRuleLevels().add(level5);
    rule2.getRuleLevels().add(level2);
    rule2.getRuleLevels().add(level4);
    rule2.getRuleLevels().add(level6);

    RULE_ENTITIES.add(rule1);
    RULE_ENTITIES.add(rule2);
    LEVEL_ENTITIES.add(level1);
    LEVEL_ENTITIES.add(level2);
    LEVEL_ENTITIES.add(level3);
    LEVEL_ENTITIES.add(level4);
    LEVEL_ENTITIES.add(level5);
    LEVEL_ENTITIES.add(level6);

    TicketProductPolicy policy1 = new TicketProductPolicy();
    policy1.setEffDate(DATE1);
    policy1.setIsPlatFormTktActivated(true);
    policy1.setPlatformTktUnitPrice(BigDecimal.valueOf(0.00));
    policy1.setRoundTripTktOutboundValidDays(1);
    policy1.setRoundTripTktInboundValidDays(1);
    policy1.setOnewayTktRefundFeeAmt(BigDecimal.valueOf(20.00));
    policy1.setOnewayTktValidDays(1);
    policy1.setReservedSeatTktValidDays(1);
    policy1.setCscCardMarketingFeeAmt(BigDecimal.valueOf(100.00));
    policy1.setCscCardDepositAmt(BigDecimal.valueOf(50.00));

    TicketProductPolicy policy2 = new TicketProductPolicy();
    policy2.setEffDate(DATE2);
    policy2.setIsPlatFormTktActivated(true);
    policy2.setPlatformTktUnitPrice(BigDecimal.valueOf(0.00));
    policy2.setRoundTripTktOutboundValidDays(1);
    policy2.setRoundTripTktInboundValidDays(1);
    policy2.setOnewayTktRefundFeeAmt(BigDecimal.valueOf(20.00));
    policy2.setOnewayTktValidDays(1);
    policy2.setReservedSeatTktValidDays(1);
    policy2.setCscCardMarketingFeeAmt(BigDecimal.valueOf(100.00));
    policy2.setCscCardDepositAmt(BigDecimal.valueOf(50.00));

    TicketProductPolicy policy3 = new TicketProductPolicy();
    policy3.setEffDate(DATE3);
    policy3.setIsPlatFormTktActivated(true);
    policy3.setPlatformTktUnitPrice(BigDecimal.valueOf(0.00));
    policy3.setRoundTripTktOutboundValidDays(1);
    policy3.setRoundTripTktInboundValidDays(1);
    policy3.setOnewayTktRefundFeeAmt(BigDecimal.valueOf(20.00));
    policy3.setOnewayTktValidDays(1);
    policy3.setReservedSeatTktValidDays(1);
    policy3.setCscCardMarketingFeeAmt(BigDecimal.valueOf(100.00));
    policy3.setCscCardDepositAmt(BigDecimal.valueOf(50.00));

    POLICY_ENTITIES.add(policy1);
    POLICY_ENTITIES.add(policy2);
    POLICY_ENTITIES.add(policy3);

    ReissueTicketRefundFee reissue1 = new ReissueTicketRefundFee();
    reissue1.setEffDate(DATE1);
    reissue1.setFeePct(BigDecimal.valueOf(20));
    reissue1.setIsAdditionalCharges(Boolean.FALSE);

    ReissueTicketRefundFee reissue2 = new ReissueTicketRefundFee();
    reissue2.setEffDate(DATE2);
    reissue2.setFeePct(BigDecimal.valueOf(20));
    reissue2.setIsAdditionalCharges(Boolean.FALSE);

    ReissueTicketRefundFee reissue3 = new ReissueTicketRefundFee();
    reissue3.setEffDate(DATE3);
    reissue3.setFeePct(BigDecimal.valueOf(20));
    reissue3.setIsAdditionalCharges(Boolean.FALSE);

    // invariant level entities
    RefundFeeRuleLevel discontinuousLevel1 = new RefundFeeRuleLevel();
    discontinuousLevel1.setRefundFeeRule(rule1);
    discontinuousLevel1.setEffDate(DATE1);
    discontinuousLevel1.setMinPreDepDays(0);
    discontinuousLevel1.setMaxPreDepDays(0);
    discontinuousLevel1.setFeeAmt(BigDecimal.valueOf(20.00));
    discontinuousLevel1.setFeePct(BigDecimal.valueOf(0.00));

    RefundFeeRuleLevel discontinuousLevel2 = new RefundFeeRuleLevel();
    discontinuousLevel2.setRefundFeeRule(rule1);
    discontinuousLevel2.setEffDate(DATE1);
    discontinuousLevel2.setMinPreDepDays(2);
    discontinuousLevel2.setMaxPreDepDays(255);
    discontinuousLevel2.setFeeAmt(BigDecimal.valueOf(20.00));
    discontinuousLevel2.setFeePct(BigDecimal.valueOf(0.00));

    DISCONTINUOUS_LEVEL_ENTITIES.add(discontinuousLevel1);
    DISCONTINUOUS_LEVEL_ENTITIES.add(discontinuousLevel2);

    RefundFeeRuleLevel overlapLevel1 = new RefundFeeRuleLevel();
    overlapLevel1.setRefundFeeRule(rule1);
    overlapLevel1.setEffDate(DATE1);
    overlapLevel1.setMinPreDepDays(0);
    overlapLevel1.setMaxPreDepDays(-1);
    overlapLevel1.setFeeAmt(BigDecimal.valueOf(20.00));
    overlapLevel1.setFeePct(BigDecimal.valueOf(0.00));

    List<RefundFeeRuleLevel> overlapCondition1 = new ArrayList<>();
    overlapCondition1.add(overlapLevel1);

    RefundFeeRuleLevel overlapLevel3 = new RefundFeeRuleLevel();
    overlapLevel3.setRefundFeeRule(rule1);
    overlapLevel3.setEffDate(DATE1);
    overlapLevel3.setMinPreDepDays(0);
    overlapLevel3.setMaxPreDepDays(2);
    overlapLevel3.setFeeAmt(BigDecimal.valueOf(20.00));
    overlapLevel3.setFeePct(BigDecimal.valueOf(0.00));

    RefundFeeRuleLevel overlapLevel4 = new RefundFeeRuleLevel();
    overlapLevel4.setRefundFeeRule(rule1);
    overlapLevel4.setEffDate(DATE1);
    overlapLevel4.setMinPreDepDays(2);
    overlapLevel4.setMaxPreDepDays(255);
    overlapLevel4.setFeeAmt(BigDecimal.valueOf(20.00));
    overlapLevel4.setFeePct(BigDecimal.valueOf(0.00));

    List<RefundFeeRuleLevel> overlapCondition2 = new ArrayList<>();
    overlapCondition2.add(overlapLevel3);
    overlapCondition2.add(overlapLevel4);

    OVERLAP_LEVEL_ENTITIES.add(overlapCondition1);
    OVERLAP_LEVEL_ENTITIES.add(overlapCondition2);

    TEMP_DATA.setCorrelationId("1234-5678-9012-3456");
    TEMP_DATA.setUserId("User");
    TEMP_DATA.setTicketProductPolicy(policy1);
    RefundFeeRuleLevelTemp levelTemp = new RefundFeeRuleLevelTemp();
    levelTemp.setRegularTicketRuleTemp(TEMP_DATA);
    levelTemp.setRefundFeeRule(rule1);
    levelTemp.setFeePct(level1.getFeePct());
    levelTemp.setFeeAmt(level1.getFeeAmt());
    levelTemp.setEffDate(level1.getEffDate());
    levelTemp.setMinPreDepDays(level1.getMinPreDepDays());
    levelTemp.setMaxPreDepDays(level1.getMaxPreDepDays());
    TEMP_DATA.getRefundFeeRuleLevels().add(levelTemp);

    FUTURE_VERSION_CONTROL.setRegularTicketRuleTemp(TEMP_DATA);
  }

  private void setOutputs(boolean isInit) {
    TicketProductPolicyOutput policyOutput = new TicketProductPolicyOutput();
    policyOutput.setId(3L);
    policyOutput.setEffDate(DATE3);
    policyOutput.setDataVersion(0L);
    policyOutput.setIsPlatFormTktActivated(true);
    policyOutput.setPlatformTktUnitPrice(BigDecimal.valueOf(0.00));
    policyOutput.setRoundTripTktOutboundValidDays(1);
    policyOutput.setRoundTripTktInboundValidDays(1);
    policyOutput.setOnewayTktRefundFeeAmt(BigDecimal.valueOf(20.00));
    policyOutput.setOnewayTktValidDays(1);
    policyOutput.setReservedSeatTktValidDays(1);
    policyOutput.setCscCardMarketingFeeAmt(BigDecimal.valueOf(100.00));
    policyOutput.setCscCardDepositAmt(BigDecimal.valueOf(50.00));

    if (!isInit) {
      policyOutput.setId(POLICY_ENTITIES.get(2).getId());
    }

    RefundFeeRuleOutput ruleOutput1 = new RefundFeeRuleOutput();
    ruleOutput1.setId(1L);
    ruleOutput1.setDataVersion(0L);
    ruleOutput1.setType(RefundFeeRuleType.RULE_TYPE_A);
    ruleOutput1.setCategory(RefundFeeCategory.RESERVED_SEAT_INDIVIDUAL);

    if (!isInit) {
      ruleOutput1.setId(RULE_ENTITIES.get(0).getId());
    }

    RefundFeeRuleOutput ruleOutput2 = new RefundFeeRuleOutput();
    ruleOutput2.setId(2L);
    ruleOutput2.setDataVersion(0L);
    ruleOutput2.setType(RefundFeeRuleType.RULE_TYPE_A);
    ruleOutput2.setCategory(RefundFeeCategory.RESERVED_SEAT_GROUP);

    if (!isInit) {
      ruleOutput2.setId(RULE_ENTITIES.get(1).getId());
    }

    RefundFeeRuleLevelOutput levelOutput1 = new RefundFeeRuleLevelOutput();
    levelOutput1.setId(3L);
    levelOutput1.setEffDate(DATE3);
    levelOutput1.setMinPreDepDays(0);
    levelOutput1.setMaxPreDepDays(255);
    levelOutput1.setFeeAmt(BigDecimal.valueOf(20.00));
    levelOutput1.setFeePct(BigDecimal.valueOf(0.00));

    RefundFeeRuleLevelOutput levelOutput2 = new RefundFeeRuleLevelOutput();
    levelOutput2.setId(6L);
    levelOutput2.setEffDate(DATE3);
    levelOutput2.setMinPreDepDays(0);
    levelOutput2.setMaxPreDepDays(255);
    levelOutput2.setFeeAmt(BigDecimal.valueOf(20.00));
    levelOutput2.setFeePct(BigDecimal.valueOf(0.00));

    if (!isInit) {
      levelOutput1.setId(RULE_ENTITIES.get(0).getRuleLevels().get(2).getId());
      levelOutput2.setId(RULE_ENTITIES.get(1).getRuleLevels().get(2).getId());
      ruleOutput1.getRuleLevels().clear();
      ruleOutput2.getRuleLevels().clear();
    }

    ruleOutput1.getRuleLevels().add(levelOutput1);
    ruleOutput2.getRuleLevels().add(levelOutput2);

    if (!isInit) {
      FUTURE_OUTPUT.getRefundFeeRules().clear();
    }
    FUTURE_OUTPUT.setTicketProductPolicy(policyOutput);
    FUTURE_OUTPUT.getRefundFeeRules().add(ruleOutput1);
    FUTURE_OUTPUT.getRefundFeeRules().add(ruleOutput2);
  }

  private void setInputs() {
    UPDATE_INPUT = getBasicInput(2L, DATE2);
    UPDATE_FUTURE_INPUT = getBasicInput(3L, DATE3);
    CREATE_INPUT = getBasicInput(null, DATE4);

    // invalid input
    RegularTicketRuleInput inputPolicyWithNullField = getBasicInput(2L, DATE2);
    inputPolicyWithNullField.getTicketProductPolicy().setEffDate(null);

    RegularTicketRuleInput inputLevelWithDayOver255 = getBasicInput(2L, DATE2);
    inputLevelWithDayOver255.getRefundFeeRules().clear();
    RefundFeeRuleInput ruleInput7 = new RefundFeeRuleInput();
    ruleInput7.setId(1L);
    ruleInput7.setDataVersion(0L);
    ruleInput7.setType(RefundFeeRuleType.RULE_TYPE_A);
    ruleInput7.setCategory(RefundFeeCategory.RESERVED_SEAT_INDIVIDUAL);
    RefundFeeRuleLevelInput levelWithDayOver255 = new RefundFeeRuleLevelInput();
    levelWithDayOver255.setId(3L);
    levelWithDayOver255.setEffDate(DATE2);
    levelWithDayOver255.setMinPreDepDays(0);
    levelWithDayOver255.setMaxPreDepDays(256);
    levelWithDayOver255.setFeeAmt(BigDecimal.valueOf(20.00));
    levelWithDayOver255.setFeePct(BigDecimal.valueOf(0.00));
    ruleInput7.getRuleLevels().add(levelWithDayOver255);
    inputLevelWithDayOver255.getRefundFeeRules().add(ruleInput7);

    RegularTicketRuleInput inputLevelWithNegDay = getBasicInput(2L, DATE2);
    RefundFeeRuleInput ruleInput8 = new RefundFeeRuleInput();
    ruleInput8.setId(1L);
    ruleInput8.setDataVersion(0L);
    ruleInput8.setType(RefundFeeRuleType.RULE_TYPE_A);
    ruleInput8.setCategory(RefundFeeCategory.RESERVED_SEAT_INDIVIDUAL);
    RefundFeeRuleLevelInput levelWithNegDay = new RefundFeeRuleLevelInput();
    levelWithNegDay.setId(3L);
    levelWithNegDay.setEffDate(DATE2);
    levelWithNegDay.setMinPreDepDays(0);
    levelWithNegDay.setMaxPreDepDays(-1);
    levelWithNegDay.setFeeAmt(BigDecimal.valueOf(20.00));
    levelWithNegDay.setFeePct(BigDecimal.valueOf(0.00));
    ruleInput8.getRuleLevels().add(levelWithNegDay);

    inputLevelWithNegDay.getRefundFeeRules().clear();
    inputLevelWithNegDay.getRefundFeeRules().add(ruleInput8);

    RegularTicketRuleInput inputRuleHasOver2Levels = getBasicInput(2L, DATE2);
    inputRuleHasOver2Levels.getRefundFeeRules().get(0).getRuleLevels()
        .addAll(UPDATE_INPUT.getRefundFeeRules().get(0).getRuleLevels());
    inputRuleHasOver2Levels.getRefundFeeRules().get(0).getRuleLevels()
        .addAll(UPDATE_INPUT.getRefundFeeRules().get(0).getRuleLevels());

    INVALID_INPUTS.add(inputPolicyWithNullField);
    INVALID_INPUTS.add(inputLevelWithDayOver255);
    INVALID_INPUTS.add(inputLevelWithNegDay);
    INVALID_INPUTS.add(inputRuleHasOver2Levels);
  }

  private RegularTicketRuleInput getBasicInput(Long baseId, LocalDate date) {
    RegularTicketRuleInput input = new RegularTicketRuleInput();

    TicketProductPolicyInput policyInput = new TicketProductPolicyInput();
    policyInput.setId(Objects.isNull(baseId) ? null : baseId);
    policyInput.setEffDate(date);
    policyInput.setDataVersion(0L);
    policyInput.setIsPlatFormTktActivated(true);
    policyInput.setPlatformTktUnitPrice(BigDecimal.valueOf(0.00));
    policyInput.setRoundTripTktOutboundValidDays(1);
    policyInput.setRoundTripTktInboundValidDays(1);
    policyInput.setOnewayTktRefundFeeAmt(BigDecimal.valueOf(20.00));
    policyInput.setOnewayTktValidDays(1);
    policyInput.setReservedSeatTktValidDays(1);
    policyInput.setCscCardMarketingFeeAmt(BigDecimal.valueOf(100.00));
    policyInput.setCscCardDepositAmt(BigDecimal.valueOf(50.00));

    RefundFeeRuleInput ruleInput1 = new RefundFeeRuleInput();
    ruleInput1.setId(1L);
    ruleInput1.setDataVersion(0L);
    ruleInput1.setType(RefundFeeRuleType.RULE_TYPE_A);
    ruleInput1.setCategory(RefundFeeCategory.RESERVED_SEAT_INDIVIDUAL);

    RefundFeeRuleInput ruleInput2 = new RefundFeeRuleInput();
    ruleInput2.setId(2L);
    ruleInput2.setDataVersion(0L);
    ruleInput2.setType(RefundFeeRuleType.RULE_TYPE_A);
    ruleInput2.setCategory(RefundFeeCategory.RESERVED_SEAT_GROUP);

    RefundFeeRuleLevelInput levelInput1 = new RefundFeeRuleLevelInput();
    levelInput1.setId(Objects.isNull(baseId) ? null : ((baseId - 1) * 2 + 1));
    levelInput1.setEffDate(date);
    levelInput1.setMinPreDepDays(0);
    levelInput1.setMaxPreDepDays(255);
    levelInput1.setFeeAmt(BigDecimal.valueOf(20.00));
    levelInput1.setFeePct(BigDecimal.valueOf(0.00));

    RefundFeeRuleLevelInput levelInput2 = new RefundFeeRuleLevelInput();
    levelInput2.setId(Objects.isNull(baseId) ? null : baseId * 2);
    levelInput2.setEffDate(date);
    levelInput2.setMinPreDepDays(0);
    levelInput2.setMaxPreDepDays(255);
    levelInput2.setFeeAmt(BigDecimal.valueOf(20.00));
    levelInput2.setFeePct(BigDecimal.valueOf(0.00));

    ruleInput1.getRuleLevels().add(levelInput1);
    ruleInput2.getRuleLevels().add(levelInput2);

    input.setTicketProductPolicy(policyInput);
    input.getRefundFeeRules().add(ruleInput1);
    input.getRefundFeeRules().add(ruleInput2);

    return input;
  }
}
