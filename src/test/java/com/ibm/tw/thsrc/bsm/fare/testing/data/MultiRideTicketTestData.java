/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.fa.dto.MultiRideTicketInput;
import com.ibm.tw.thsrc.bsm.fa.dto.MultiRideTicketOptionDto;
import com.ibm.tw.thsrc.bsm.fa.dto.MultiRideTicketOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.IssueTravelCardExpiryType;
import com.ibm.tw.thsrc.bsm.fare.entity.MultiRideTicket;
import com.ibm.tw.thsrc.bsm.fare.entity.MultiRideTicketOption;
import java.math.BigDecimal;
import java.util.Arrays;

public class MultiRideTicketTestData {

  public final MultiRideTicket INVALID_ENTITY = new MultiRideTicket();
  public final MultiRideTicketInput INPUT = new MultiRideTicketInput();
  public final MultiRideTicketOutput OUTPUT = new MultiRideTicketOutput();

  public MultiRideTicketTestData() {
    createInputTestData();
    createInput();
    createOutput();
  }

  public void createInputTestData() {

    MultiRideTicketOption option = new MultiRideTicketOption();
    option.setRides(Integer.valueOf(20));
    option.setValidDays(Integer.valueOf(999));
    option.setDiscountPct(BigDecimal.valueOf(66));
    option.setIsAppSale(Boolean.TRUE);
    option.setAppEffSaleDate(null);
    option.setDiscountPct(null);

    INVALID_ENTITY.setExpiryType(IssueTravelCardExpiryType.BY_ISSUE_DATE);
    INVALID_ENTITY.setRefundFeeAmt(BigDecimal.valueOf(*********));
    INVALID_ENTITY.setMultiRideTicketOptions(Arrays.asList(option));
  }

  public void createInput() {

    MultiRideTicketOptionDto option = new MultiRideTicketOptionDto();
    option.setValidDays(Integer.valueOf(999));
    option.setDiscountPct(BigDecimal.valueOf(66));
    option.setIsAppSale(Boolean.TRUE);
    option.setAppEffSaleDate(null);
    option.setDiscountPct(null);

    INPUT.setDataVersion(1L);
    INPUT.setExpiryType(IssueTravelCardExpiryType.BY_ISSUE_DATE);
    INPUT.setRefundFeeAmt(BigDecimal.valueOf(*********));
    INPUT.setOptions(Arrays.asList(option));
  }

  public void createOutput() {

    MultiRideTicketOptionDto option = new MultiRideTicketOptionDto();
    option.setValidDays(Integer.valueOf(999));
    option.setDiscountPct(BigDecimal.valueOf(66));
    option.setIsAppSale(Boolean.TRUE);
    option.setAppEffSaleDate(null);
    option.setDiscountPct(null);

    OUTPUT.setId(1L);
    OUTPUT.setDataVersion(1L);
    OUTPUT.setExpiryType(IssueTravelCardExpiryType.BY_ISSUE_DATE);
    OUTPUT.setRefundFeeAmt(BigDecimal.valueOf(*********));
    OUTPUT.setOptions(Arrays.asList(option));
  }
}