/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare;

import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.core.event.EventStore;
import com.ibm.tw.thsrc.bsm.core.event.ExceptionMessages;
import com.ibm.tw.thsrc.bsm.core.service.HttpHeaderInterceptor;
import com.ibm.tw.thsrc.bsm.exception.FareErrorCode;
import com.ibm.tw.thsrc.bsm.fa.client.RoundingRuleClient;
import com.ibm.tw.thsrc.bsm.fa.dto.RoundingRuleInput;
import com.ibm.tw.thsrc.bsm.fa.dto.RoundingRuleOutput;
import com.ibm.tw.thsrc.bsm.meig.serivce.implementation.FareServiceMeigImpl;
import com.ibm.tw.thsrc.bsm.meig.vo.UpdateParamRequest;
import com.ibm.tw.thsrc.bsm.res.connection.ResCommunicationService;
import com.ibm.tw.thsrc.bsm.test.support.ApiTestAssertions;
import com.ibm.tw.thsrc.bsm.test.support.TestFeignClientFactory;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.http.HttpEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;

@SpringBootTest(classes = FareApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@ActiveProfiles("test")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class RoundingRuleTest {

  @Autowired
  ObjectMapper objectMapper;
  @Autowired
  TestFeignClientFactory testFeignClientFactory;
  @Autowired
  ApiTestAssertions apiTestAssertions;
  @SpyBean
  ResCommunicationService<String, String> resCommunicationService;
  @SpyBean
  RestTemplate restTemplate;
  @SpyBean
  WebClient webClient;
  @Captor
  ArgumentCaptor<HttpEntity<UpdateParamRequest>> meigRequestCaptor;
  @MockBean
  FareServiceMeigImpl fareServiceMeig;
  RoundingRuleClient roundingRuleApi;
  @MockBean
  private HttpHeaderInterceptor interceptor;
  @MockBean
  private EventStore eventStore;

  @BeforeAll
  void setUp() {
    roundingRuleApi = testFeignClientFactory.create(RoundingRuleClient.class, "/RoundingRules");

    String response = "KDT06AUG22\n"
        + "DATE: 06AUG22    AMDATE: 46C3 (18115)    YEAR: 2022    DOW: SA \n"
        + "JULIAN: 22218    OMS: 08/06/22    INVENTORY IND: FF    DOW#: 6\n"
        + ">+";
    doReturn(response).when(resCommunicationService)
        .executeRESConnWithSession(ArgumentMatchers.anyString(), ArgumentMatchers.any());
  }

  @Sql("classpath:data/RoundingRule/init-data.sql")
  @Test
  void searchDefaultRoundingRule() {
    Search future = new Search();
    future.setFilter("isFuture eq true");
    Collection<RoundingRuleOutput> futureRoundingRules =
        roundingRuleApi.search(future).getContent();
    Assertions.assertEquals(1, futureRoundingRules.size());

    Search notFuture = new Search();
    notFuture.setFilter("isFuture eq false");
    Collection<RoundingRuleOutput> currentRoundingRules =
        roundingRuleApi.search(notFuture).getContent();
    Assertions.assertEquals(1, currentRoundingRules.size());

    Collection<RoundingRuleOutput> allRoundingRules =
        roundingRuleApi.search(new Search()).getContent();
    Assertions.assertEquals(2, allRoundingRules.size());
  }

  @Sql("classpath:data/RoundingRule/no-future.sql")
  @Test
  void createUpdateFutureRoundingRuleSuccess() throws Throwable {
    Collection<RoundingRuleOutput> allRoundingRules =
        roundingRuleApi.search(new Search()).getContent();
    Assertions.assertEquals(1, allRoundingRules.size());

    LocalDate effDate = LocalDate.now().plusDays(1);

    RoundingRuleInput input = new RoundingRuleInput();
    input.setMultiple(1);
    input.setEffDate(effDate);
    input.setDataVersion(Long.valueOf(0));

    Long id = roundingRuleApi.create(input).getId();

    allRoundingRules = roundingRuleApi.search(new Search()).getContent();
    Assertions.assertEquals(2, allRoundingRules.size());

    Optional<RoundingRuleOutput> foundOptional =
        allRoundingRules.stream().filter(r -> r.getId().equals(id)).findFirst();
    Assertions.assertTrue(foundOptional.isPresent());

    RoundingRuleOutput created = foundOptional.get();
    Assertions.assertEquals(1, created.getMultiple());
    Assertions.assertEquals(effDate, created.getEffDate());

    effDate = effDate.plusDays(1);

    RoundingRuleInput input2 = new RoundingRuleInput();
    input2.setMultiple(5);
    input2.setEffDate(effDate);
    input2.setDataVersion(0L);

    RoundingRuleOutput updated = roundingRuleApi.replace(id, input2);

    Assertions.assertEquals(5, updated.getMultiple());
    Assertions.assertEquals(effDate, updated.getEffDate());

    // 未來版不會馬上 call res/meig
    // verify(resCommunicationService, never()).executeRESConn(any());
  }

  @Sql("classpath:data/RoundingRule/no-future.sql")
  @Test
  void updateCurrentRoundingRuleSuccess() throws Throwable {
    Search notFuture = new Search();
    notFuture.setFilter("isFuture eq false");
    RoundingRuleOutput current = roundingRuleApi.search(notFuture).getContent().get(0);

    RoundingRuleInput input = new RoundingRuleInput();
    input.setMultiple(5);
    input.setEffDate(LocalDate.now());
    input.setDataVersion(0L);

    RoundingRuleOutput updated = roundingRuleApi.replace(current.getId(), input);

    Assertions.assertEquals(5, updated.getMultiple());
    Assertions.assertEquals(LocalDate.now(), updated.getEffDate()); // date being ignored
    // for updating current
    // RoundingRule


    /*
     * verify(restTemplate).postForObject(eq("/updateParam"), meigRequestCaptor.capture(),
     * eq(CommonResponse.class));
     */



    /*
     * UpdateParamRequest updateParamRequest = meigRequestCaptor.getValue().getBody();
     * Assertions.assertNotNull(updateParamRequest);
     * Assertions.assertEquals(ParamTypeEnum.ROUNDING.CODE,
     * updateParamRequest.getBody().getParamType());
     *
     * String rawDataJson = new
     * String(Base64Utils.decodeFromString(updateParamRequest.getBody().getRawData()));
     * MeigMainVo<MeigRoundingRuleVo> meigRoundingRuleVo = objectMapper.readValue(rawDataJson, new
     * TypeReference<MeigMainVo<MeigRoundingRuleVo>>() {});
     *
     * Assertions.assertEquals(5, meigRoundingRuleVo.getMain().getUnit());
     */

  }

  @Sql("classpath:data/RoundingRule/init-data.sql")
  @Test
  void failToCreateRoundingRuleWhenFutureExists() {

    RoundingRuleInput input = new RoundingRuleInput();
    input.setMultiple(1);
    input.setEffDate(LocalDate.now().plusDays(1));
    input.setDataVersion(Long.valueOf(0));

    apiTestAssertions.assertErrorCode(FareErrorCode.FA_ROUNDING_RULE_FUTURE_ALREADY_EXIST,
        () -> roundingRuleApi.create(input));
  }

  @Test
  void failToCreateRoundingRuleWithInvalidEffDate() {

    RoundingRuleInput input1 = new RoundingRuleInput();
    input1.setMultiple(1);
    input1.setEffDate(LocalDate.now());
    input1.setDataVersion(Long.valueOf(0));

    RoundingRuleInput input2 = new RoundingRuleInput();
    input2.setMultiple(1);
    input2.setEffDate(null);
    input2.setDataVersion(Long.valueOf(0));

    apiTestAssertions.assertErrorCode(FareErrorCode.FA_ROUNDING_RULE_EFF_DATE_MUST_BE_IN_THE_FUTURE,
        () -> roundingRuleApi.create(input1));

    apiTestAssertions.assertErrorCode(FareErrorCode.FA_ROUNDING_RULE_EFF_DATE_MUST_BE_IN_THE_FUTURE,
        () -> roundingRuleApi.create(input2));
  }
}
