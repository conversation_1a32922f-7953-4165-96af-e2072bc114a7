package com.ibm.tw.thsrc.bsm.fare.stepservice;

import static org.junit.jupiter.api.Assertions.*;

import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.cronjob.value.StepResult;
import com.ibm.tw.thsrc.bsm.cronjob.value.TaskExecution;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardOperatingParameterFutureVersionControl;
import com.ibm.tw.thsrc.bsm.fare.repository.CoBrandedCardOperatingParameterFutureVersionControlRepository;
import com.ibm.tw.thsrc.bsm.fare.service.CoBrandedCardOperatingParameterCommandService;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

class CoBrandedCardOperatingParameterStepServiceTest {

  CoBrandedCardOperatingParameterStepService service;

  @Mock
  CoBrandedCardOperatingParameterCommandService commandService;

  @Mock
  CoBrandedCardOperatingParameterFutureVersionControlRepository repo;


  @BeforeEach
  void init() throws IllegalAccessException {
    MockitoAnnotations.openMocks(this);
    service = new CoBrandedCardOperatingParameterStepService();

    FieldUtils.writeField(service, "commandService", commandService, true);
    FieldUtils.writeField(service, "repo", repo, true);

    Mockito.doNothing().when(commandService).cronJob(Mockito.any(), Mockito.anyString());
    CoBrandedCardOperatingParameterFutureVersionControl control = new CoBrandedCardOperatingParameterFutureVersionControl();
    control.setCorrelationId(UUID.randomUUID().toString());
    control.setEffDate(LocalDate.now());
    control.setCreateUser("user");

    Mockito.when(repo.save(Mockito.any())).thenReturn(control);
    Mockito.when(repo.findById(Mockito.any())).thenReturn(Optional.of(control));
  }

  @Test
  void testExecute() {
    Map<String, Object> map = new HashMap<>();
    map.put("correlationId", UUID.randomUUID().toString());
    map.put("futureVersionControlId", String.valueOf(1));

    TaskExecution execution = new TaskExecution();
    execution.setParameters(map);

    StepResult<String> resultStep = service.execute(execution);
    Assertions.assertNotNull(resultStep);
  }
}