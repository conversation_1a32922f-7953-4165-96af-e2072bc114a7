/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.translate;

import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.fa.dto.PassengerProfileOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.PassengerProfile;
import com.ibm.tw.thsrc.bsm.fare.testing.data.PromotionPlanTestData;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest(classes = {FareApplication.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@ActiveProfiles("test")
class PassengerProfileTranslatorTest {

  private PromotionPlanTestData PROMOTION_TEST_DATA = new PromotionPlanTestData();

  private PassengerProfileTranslatorTest() throws Exception {
  }

  @Test
  void testToPassengerProfileOutput() {
    PassengerProfile entity = PROMOTION_TEST_DATA.PROFILE_ENTITY;
    PassengerProfileOutput output = PassengerProfileTranslator.toPassengerProfileOutput(entity);

    Assertions.assertEquals(entity.getId(), output.getId());
    Assertions.assertEquals(entity.getCode(), output.getCode());
    Assertions.assertEquals(entity.getEnName(), output.getEnName());
    Assertions.assertEquals(entity.getZhName(), output.getZhName());
    Assertions.assertEquals(entity.getIsDisplay(), output.isDisplay());
    Assertions.assertEquals(entity.getEnPrintName(), output.getEnPrintName());
    Assertions.assertEquals(entity.getZhPrintName(), output.getZhPrintName());
    Assertions.assertEquals(entity.getDataVersion(), output.getDataVersion());
    Assertions.assertEquals(entity.getDisplayOrder(), output.getDisplayOrder());
    Assertions.assertEquals(entity.getTbLightColor(), output.getTbLightColor());
  }
}
