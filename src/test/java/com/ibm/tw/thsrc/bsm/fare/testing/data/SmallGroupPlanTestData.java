/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.fa.dto.SmallGroupPlanInput;
import com.ibm.tw.thsrc.bsm.fa.dto.SmallGroupPlanOutput;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan;
import com.ibm.tw.thsrc.bsm.fare.entity.SmallGroupPlan;
import java.math.BigDecimal;
import java.time.LocalDate;

public class SmallGroupPlanTestData {

  public final SmallGroupPlanInput INPUT = new SmallGroupPlanInput();
  public final SmallGroupPlanOutput OUTPUT = new SmallGroupPlanOutput();
  public final SmallGroupPlan INVALID_GROUP_SUM_ENTITY = new SmallGroupPlan();
  public final SmallGroupPlan ENTITY = new SmallGroupPlan();

  public SmallGroupPlanTestData() {
    createInputTestData();
  }

  void createInputTestData() {

    INPUT.setDataVersion(1L);
    INPUT.setCode("P001");
    INPUT.setGroupSize(4);
    INPUT.setMainProfileExpr("F");
    INPUT.setMainProfileName("成人");
    INPUT.setMainProfileQty(0);
    INPUT.setAccompanyProfileExpr("H");
    INPUT.setAccompanyProfileName("孩童");
    INPUT.setAccompanyProfileQty(4);
    INPUT.setEffDate(LocalDate.now());
    INPUT.setDscDate(LocalDate.now().plusDays(1));
    INPUT.setIsCouponReq(Boolean.FALSE);
    INPUT.setIsPartialRefund(Boolean.FALSE);

    OUTPUT.setId(1L);
    OUTPUT.setDataVersion(1L);
    OUTPUT.setCode("P001");
    OUTPUT.setGroupSize(4);
    OUTPUT.setMainProfileExpr("F");
    OUTPUT.setMainProfileName("成人");
    OUTPUT.setMainProfileQty(0);
    OUTPUT.setAccompanyProfileExpr("H");
    OUTPUT.setAccompanyProfileName("孩童");
    OUTPUT.setAccompanyProfileQty(4);
    OUTPUT.setEffDate(LocalDate.now());
    OUTPUT.setDscDate(LocalDate.now().plusDays(1));
    OUTPUT.setIsCouponReq(Boolean.FALSE);
    OUTPUT.setIsPartialRefund(Boolean.FALSE);

    PromotionPlan promotionPlan = new PromotionPlan();
    promotionPlan.setSmallGroup(Boolean.TRUE);
    promotionPlan.setCode("P001");
    promotionPlan.setEffDate(LocalDate.now());
    promotionPlan.setDscDate(LocalDate.now().plusDays(1));
    promotionPlan.setDiscountAmt(null);
    promotionPlan.setDiscountPct(BigDecimal.valueOf(50));
    promotionPlan.setName("小團體");

    INVALID_GROUP_SUM_ENTITY.setPromotionPlan(promotionPlan);
    INVALID_GROUP_SUM_ENTITY.setGroupSize(5);

    INVALID_GROUP_SUM_ENTITY.setMainProfileExpr("F");
    INVALID_GROUP_SUM_ENTITY.setMainProfileName("成人");
    INVALID_GROUP_SUM_ENTITY.setMainProfileQty(4);

    INVALID_GROUP_SUM_ENTITY.setAccompanyProfileExpr(null);
    INVALID_GROUP_SUM_ENTITY.setAccompanyProfileName("");
    INVALID_GROUP_SUM_ENTITY.setAccompanyProfileQty(null);

    INVALID_GROUP_SUM_ENTITY.setEffDate(LocalDate.now());
    INVALID_GROUP_SUM_ENTITY.setDscDate(LocalDate.now().plusDays(1));
    INVALID_GROUP_SUM_ENTITY.setIsCouponReq(Boolean.FALSE);
    INVALID_GROUP_SUM_ENTITY.setIsPartialRefund(Boolean.FALSE);

    ENTITY.setPromotionPlan(promotionPlan);
    ENTITY.setGroupSize(4);

    ENTITY.setMainProfileExpr("F");
    ENTITY.setMainProfileName("成人");
    ENTITY.setMainProfileQty(4);

    ENTITY.setAccompanyProfileExpr(null);
    ENTITY.setAccompanyProfileName("");
    ENTITY.setAccompanyProfileQty(null);

    ENTITY.setEffDate(LocalDate.now());
    ENTITY.setDscDate(LocalDate.now().plusDays(1));
    ENTITY.setIsCouponReq(Boolean.FALSE);
    ENTITY.setIsPartialRefund(Boolean.TRUE);

  }
}
