/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.CardIssuingBankInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CardIssuingBankOutput;
import com.ibm.tw.thsrc.bsm.fare.service.CardIssuingBankCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.CardIssuingBankQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.CardIssuingBankTestData;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest(classes = {FareApplication.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@AutoConfigureMockMvc
class CardIssuingBankControllerTest {

  private static final String API_PATH = "/CardIssuingBanks";
  private static final String API_PATH_WITH_ID = "/CardIssuingBanks/1";
  private static final CardIssuingBankTestData TEST_DATA = new CardIssuingBankTestData();
  @Autowired
  private CardIssuingBankController controller;
  @MockBean
  private CardIssuingBankCommandService commandService;
  @MockBean
  private CardIssuingBankQueryService queryService;
  @Autowired
  private MockMvc mockMvc;
  @Autowired
  private ObjectMapper mapper;

  @Test
  void search() {
    Mockito.when(queryService.search(Mockito.any()))
        .thenReturn(new PageImpl<>(TEST_DATA.EZ_OUTPUTS));

    List<CardIssuingBankOutput> expected = TEST_DATA.EZ_OUTPUTS;

    Page<CardIssuingBankOutput> resultPage = controller.search(new Search());
    Assertions.assertEquals(expected.size(), resultPage.getContent().size());
  }

  @Test
  void create() {
    Mockito.when(commandService.create(ArgumentMatchers.any())).thenReturn(1L);
    Mockito.when(queryService.read(ArgumentMatchers.anyLong()))
        .thenReturn(TEST_DATA.EZ_OUTPUTS.get(0));

    CardIssuingBankOutput expected = TEST_DATA.EZ_OUTPUTS.get(0);
    CardIssuingBankOutput actual = controller.create(TEST_DATA.CREATE_INPUT);

    Assertions.assertEquals(expected.getId(), actual.getId());
    Assertions.assertEquals(expected.getCode(), actual.getCode());
    Assertions.assertEquals(expected.getName(), actual.getName());
  }

  @Test
  void modifyDelete() {
    CollectionModificationInput<CardIssuingBankInput> input = new CollectionModificationInput<>();
    input.getDeletions().add(1L);
    Mockito.when(commandService.patch(ArgumentMatchers.any())).thenReturn(new HashSet<>());
    Mockito.when(queryService.read(ArgumentMatchers.anySet())).thenReturn(new HashSet<>());

    Set<CardIssuingBankOutput> actual = controller.modify(input);

    Assertions.assertEquals(0, actual.size());
  }

  @Test
  void delete() throws Exception {
    MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
            .delete(API_PATH_WITH_ID))
        .andExpect(status().isOk())
        .andReturn();

    String content = mvcResult.getResponse().getContentAsString();
    assertEquals(StringUtils.EMPTY, content);
  }

  @Test
  void inputValidation() throws Exception {
    for (CardIssuingBankInput invalidInput : TEST_DATA.INVALID_INPUTS) {
      MvcResult mvcResult = mockMvc
          .perform(MockMvcRequestBuilders.post(API_PATH)
              .content(mapper.writeValueAsString(invalidInput))
              .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
          .andReturn();

      Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(), mvcResult.getResponse().getStatus());
    }
  }

}
