/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.MultiRideTicketOutput;
import com.ibm.tw.thsrc.bsm.fare.service.MultiRideTicketCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.MultiRideTicketQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.MultiRideTicketTestData;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest(classes = {FareApplication.class})
@AutoConfigureMockMvc
@ActiveProfiles("test")
class MultiRideTicketApiControllerTest {

private static MultiRideTicketTestData TEST_DATA = new MultiRideTicketTestData();

  @Autowired
  private MultiRideTicketApiController controller;

  @MockBean
  MultiRideTicketCommandService commandService;

  @MockBean
  MultiRideTicketQueryService queryService;


  @BeforeEach
  void reset() {
    Mockito.when(commandService.create(ArgumentMatchers.any())).thenReturn(TEST_DATA.OUTPUT.getId());
    Mockito.when(queryService.read(ArgumentMatchers.any())).thenReturn(TEST_DATA.OUTPUT);
    Mockito.doNothing().when(commandService).delete(ArgumentMatchers.any());
    List<MultiRideTicketOutput> outputs = new ArrayList<>();
    outputs.add(TEST_DATA.OUTPUT);
    Mockito.when(queryService.search(ArgumentMatchers.any())).thenReturn(new PageImpl<>(outputs));
  }

  @Test
  @Order(1)
  final void read() {
    MultiRideTicketOutput output = controller.read(1L);
    Assertions.assertEquals(TEST_DATA.OUTPUT.getId(), output.getId());
  }

  @Test
  @Order(2)
  final void replace() {
    MultiRideTicketOutput output = controller.replace(1L, TEST_DATA.INPUT);
    Assertions.assertEquals(TEST_DATA.OUTPUT.getId(), output.getId());
  }

  @Test
  @Order(3)
  final void search() {

    Page<MultiRideTicketOutput> page = controller.search(new Search());
    Assertions.assertEquals(TEST_DATA.OUTPUT.getId(), page.getContent().get(0).getId());
  }
}
