/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardBankPromotionInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardBankPromotionOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.ElectronicMoneyType;
import com.ibm.tw.thsrc.bsm.fare.entity.CardIssuingBank;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCard;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardBankPromotion;
import com.ibm.tw.thsrc.bsm.util.BeanUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

public class CoBrandedCardBankPromotionTestData {

  public static final String FREQUENCY = "SM-W--S";
  public CardIssuingBankTestData BANK_DATA = new CardIssuingBankTestData();
  public List<CoBrandedCardBankPromotion> ENTITIES = new LinkedList<>();
  public List<CoBrandedCardBankPromotion> QUERY_ENTITIES = new LinkedList<>();
  public CoBrandedCard EZ_CARD = new CoBrandedCard();
  public CoBrandedCard IPA_CARD = new CoBrandedCard();
  public List<CoBrandedCardBankPromotionOutput> OUTPUTS = new LinkedList<>();
  public CoBrandedCardBankPromotionInput CREATE_INPUT = new CoBrandedCardBankPromotionInput();
  public CoBrandedCardBankPromotionInput UPDATE_INPUT = new CoBrandedCardBankPromotionInput();
  // entities.get(0)
  public CoBrandedCardBankPromotionInput OVERLAP_UPDATE_INPUT = new CoBrandedCardBankPromotionInput();

  public CoBrandedCardBankPromotionInput OVERLAP_CREATE_INPUT = new CoBrandedCardBankPromotionInput();
  public List<CoBrandedCardBankPromotionInput> INVALID_INPUTS = new LinkedList<>();
  public List<CardIssuingBank> EZ_BANK_ENTITIES = new ArrayList<>();
  public List<CardIssuingBank> IPA_BANK_ENTITIES = new ArrayList<>();

  public CoBrandedCardBankPromotionTestData() throws Exception {

    setEntities();
    setOutputs();
    setInputs();
  }

  private void setBanks() {
    BANK_DATA.EZ_ENTITIES.forEach(b -> {
      CardIssuingBank bank = new CardIssuingBank();
      BeanUtils.copyProperties(bank, b);
      bank.setCoBrandedCard(EZ_CARD);
      EZ_BANK_ENTITIES.add(bank);
    });

    BANK_DATA.IPA_ENTITIES.forEach(b -> {
      CardIssuingBank bank = new CardIssuingBank();
      BeanUtils.copyProperties(bank, b);
      bank.setCoBrandedCard(IPA_CARD);
      IPA_BANK_ENTITIES.add(bank);
    });
  }

  public void resetBankIdInput() {
    UPDATE_INPUT.setBankId(EZ_BANK_ENTITIES.get(0).getId());
    UPDATE_INPUT.setCoBrandedCardId(EZ_CARD.getId());
    CREATE_INPUT.setBankId(EZ_BANK_ENTITIES.get(1).getId());
    CREATE_INPUT.setCoBrandedCardId(EZ_CARD.getId());
    OVERLAP_UPDATE_INPUT.setBankId(EZ_BANK_ENTITIES.get(1).getId());
    OVERLAP_UPDATE_INPUT.setCoBrandedCardId(EZ_CARD.getId());
    BeanUtils.copyProperties(OVERLAP_CREATE_INPUT, OVERLAP_UPDATE_INPUT);
  }

  private void setEntities() {
    EZ_CARD.setAutoReloaded(Boolean.TRUE);
    EZ_CARD.setName("悠遊卡");
    EZ_CARD.setDiffStationEntryExitSecsLimit(60);
    EZ_CARD.setElectronicMoneyType(ElectronicMoneyType.EASY_CARD);
    EZ_CARD.setTransferBonusMinsLimit(2);
    EZ_CARD.setDiffStationEntryExitSecsLimit(180);

    IPA_CARD.setAutoReloaded(Boolean.TRUE);
    IPA_CARD.setName("一卡通");
    IPA_CARD.setDiffStationEntryExitSecsLimit(60);
    IPA_CARD.setElectronicMoneyType(ElectronicMoneyType.I_PASS);
    IPA_CARD.setTransferBonusMinsLimit(2);
    IPA_CARD.setDiffStationEntryExitSecsLimit(180);

    setBanks();

    CoBrandedCardBankPromotion entity1 = new CoBrandedCardBankPromotion();
    entity1.setBank(EZ_BANK_ENTITIES.get(0));
    entity1.setCoBrandedCard(EZ_CARD);
    entity1.setFrequency(FREQUENCY);
    entity1.setEffSaleDate(LocalDate.now());
    entity1.setDscSaleDate(LocalDate.now().plusDays(2));
    entity1.setDiscountPct(BigDecimal.valueOf(80));
    ENTITIES.add(entity1);

    CoBrandedCardBankPromotion entity2 = new CoBrandedCardBankPromotion();
    BeanUtils.copyProperties(entity2, entity1);
    entity2.setBank(EZ_BANK_ENTITIES.get(1));
    entity2.setCoBrandedCard(EZ_CARD);
    ENTITIES.add(entity2);

    CoBrandedCardBankPromotion qEntity1 = new CoBrandedCardBankPromotion();
    BeanUtils.copyProperties(qEntity1, entity1);
    qEntity1.setBank(IPA_BANK_ENTITIES.get(0));
    qEntity1.setCoBrandedCard(IPA_CARD);
    QUERY_ENTITIES.add(qEntity1);

    CoBrandedCardBankPromotion qEntity2 = new CoBrandedCardBankPromotion();
    BeanUtils.copyProperties(qEntity2, qEntity1);
    qEntity2.setBank(IPA_BANK_ENTITIES.get(1));
    QUERY_ENTITIES.add(qEntity2);
  }

  private void setOutputs() {
    CoBrandedCardBankPromotionOutput output = new CoBrandedCardBankPromotionOutput();
    output.setId(1L);
    output.setDataVersion(0L);
    output.setBankId(BANK_DATA.EZ_OUTPUTS.get(0).getId());
    output.setBankCode(BANK_DATA.EZ_OUTPUTS.get(0).getCode());
    output.setBankName(BANK_DATA.EZ_OUTPUTS.get(0).getName());
    output.setCoBrandedCardId(1L);
    output.setCoBrandedCardType(EZ_CARD.getElectronicMoneyType());
    output.setFrequency(FREQUENCY);
    output.setEffSaleDate(LocalDate.now());
    output.setDscSaleDate(LocalDate.now().plusDays(2));
    output.setDiscountPct(BigDecimal.valueOf(80));
    OUTPUTS.add(output);
  }

  private void setInputs() {
    UPDATE_INPUT.setDataVersion(0L);
    UPDATE_INPUT.setBankId(BANK_DATA.EZ_OUTPUTS.get(0).getId());
    UPDATE_INPUT.setCoBrandedCardId(1L);
    UPDATE_INPUT.setFrequency("SMT----");
    UPDATE_INPUT.setEffSaleDate(LocalDate.now().plusDays(3));
    UPDATE_INPUT.setDscSaleDate(LocalDate.now().plusDays(5));
    UPDATE_INPUT.setDiscountPct(BigDecimal.valueOf(80));

    BeanUtils.copyProperties(CREATE_INPUT, UPDATE_INPUT);
    CREATE_INPUT.setBankId(BANK_DATA.EZ_OUTPUTS.get(1).getId());

    BeanUtils.copyProperties(OVERLAP_CREATE_INPUT, UPDATE_INPUT);
    OVERLAP_CREATE_INPUT.setBankId(BANK_DATA.EZ_OUTPUTS.get(0).getId());
    OVERLAP_CREATE_INPUT.setEffSaleDate(LocalDate.now().plusDays(1));
    OVERLAP_CREATE_INPUT.setDscSaleDate(LocalDate.now().plusDays(3));

    BeanUtils.copyProperties(OVERLAP_UPDATE_INPUT, UPDATE_INPUT);
    OVERLAP_UPDATE_INPUT.setBankId(BANK_DATA.EZ_OUTPUTS.get(1).getId());
    OVERLAP_UPDATE_INPUT.setEffSaleDate(LocalDate.now().plusDays(1));
    OVERLAP_UPDATE_INPUT.setDscSaleDate(LocalDate.now().plusDays(3));

    // invalid for controller
    CoBrandedCardBankPromotionInput inputWithNull = new CoBrandedCardBankPromotionInput();
    BeanUtils.copyProperties(inputWithNull, UPDATE_INPUT);
    inputWithNull.setBankId(null);
    INVALID_INPUTS.add(inputWithNull);

    CoBrandedCardBankPromotionInput inputPctOver100 = new CoBrandedCardBankPromotionInput();
    BeanUtils.copyProperties(inputPctOver100, UPDATE_INPUT);
    inputPctOver100.setDiscountPct(BigDecimal.valueOf(101));
    INVALID_INPUTS.add(inputPctOver100);

    CoBrandedCardBankPromotionInput inputWithBlank = new CoBrandedCardBankPromotionInput();
    BeanUtils.copyProperties(inputWithBlank, UPDATE_INPUT);
    inputWithBlank.setFrequency(StringUtils.EMPTY);
    INVALID_INPUTS.add(inputWithBlank);
  }
}
