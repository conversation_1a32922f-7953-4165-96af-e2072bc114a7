package com.ibm.tw.thsrc.bsm.fare.stepservice;

import static org.junit.jupiter.api.Assertions.*;

import com.ibm.tw.thsrc.bsm.cronjob.service.StepService;
import com.ibm.tw.thsrc.bsm.cronjob.value.TaskExecution;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class FareStepDecisionStrategyTest {


  FareStepDecisionStrategy stepDecisionStrategy = new FareStepDecisionStrategy();

  @Test
  void decide() {

    TaskExecution execution = new TaskExecution();
    execution.setStepName(StringUtils.EMPTY);
    Optional<StepService> result = stepDecisionStrategy.decide(execution);
    Assertions.assertEquals(Optional.empty(), result);
  }
}