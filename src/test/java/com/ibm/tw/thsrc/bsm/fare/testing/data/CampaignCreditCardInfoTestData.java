/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardBatchOutput;
import com.ibm.tw.thsrc.bsm.fa.dto.CampaignCreditCardInfoOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.CreditCardCampaignType;
import com.ibm.tw.thsrc.bsm.fa.enums.FarePlanType;
import com.ibm.tw.thsrc.bsm.fare.entity.CampaignCreditCardBatch;
import com.ibm.tw.thsrc.bsm.fare.entity.CampaignCreditCardInfo;
import com.ibm.tw.thsrc.bsm.fare.entity.CreditCardWeekdayPromotion;
import com.ibm.tw.thsrc.bsm.fare.entity.PromotionPlan;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.IntStream;

public class CampaignCreditCardInfoTestData {

  public List<Long> IDS = Arrays.asList(1L, 2L, 3L, 4L, 5L);

  public List<CampaignCreditCardInfo> ENTITIES = new ArrayList<>();
  public List<CampaignCreditCardBatch> BATCH_ENTITIES = new ArrayList<>();

  public List<CampaignCreditCardInfoOutput> OUTPUTS = new ArrayList<>();
  public PromotionPlan PROMOTION = new PromotionPlan();

  public List<String> bizBankNames = Arrays.asList("合庫", "彰化銀行", "台灣銀行", "玉山銀行",
      "台新銀行");
  public List<String> bizCardNos = Arrays.asList("111111", "222222", "333333", "444444", "555555");

  public CampaignCreditCardInfoTestData() {
    setEntity();
    setOutput();
  }

  public void setEntity() {

    PROMOTION.setName("P000");
    PROMOTION.setType(FarePlanType.PROMOTION);
    PROMOTION.setName("測試促銷");

    // 商務升等
    IntStream.range(0, bizBankNames.size()).forEach(e -> {
      CampaignCreditCardBatch batch = new CampaignCreditCardBatch();
      batch.setBankName(bizBankNames.get(e));
      batch.setImportDate(LocalDate.of(2023, 1, 1));
      List<CampaignCreditCardInfo> infos = new ArrayList<>();

      IntStream.range(0, bizCardNos.size()).forEach(c -> {
        CampaignCreditCardInfo info = new CampaignCreditCardInfo();
        info.setCreditCardCampaignType(CreditCardCampaignType.BUSINESS_UPGRADE);
        info.setCardNo(bizCardNos.get(c));
        info.setEffDate(LocalDate.of(2023, 1, 1));
        info.setDscDate(LocalDate.now().plusDays(1));
        info.setEffSaleDate(LocalDate.of(2023, 1, 1));
        info.setDscSaleDate(LocalDate.now().plusDays(1));
        info.setBatch(batch);

        List<CreditCardWeekdayPromotion> promotions = new ArrayList<>();
        IntStream.range(1, 7).forEach(day -> {
          CreditCardWeekdayPromotion promo = new CreditCardWeekdayPromotion();
          promo.setDayOfWeek(day);
          promo.setPromotionPlan(PROMOTION);
          promo.setCreditCardInfo(info);
          promotions.add(promo);
        });
        info.setPromotions(promotions);

        infos.add(info);
        ENTITIES.add(info);
      });
      batch.setCardNos(infos);
      BATCH_ENTITIES.add(batch);
    });
  }

  public void setOutput() {
    IntStream.range(0, bizBankNames.size()).forEach(i -> {
      CampaignCreditCardBatchOutput output = new CampaignCreditCardBatchOutput();
      output.setId(Long.valueOf(i) + 1L);
      output.setBankName(bizBankNames.get(i));
      output.setImportDate(LocalDate.of(2023, 1, 1));

      List<CampaignCreditCardInfoOutput> infos = new ArrayList<>();

      IntStream.range(0, bizCardNos.size()).forEach(c -> {
        CampaignCreditCardInfoOutput info = new CampaignCreditCardInfoOutput();
        info.setId(Long.valueOf(c) + 1L);
        info.setCreditCardCampaignType(CreditCardCampaignType.BUSINESS_UPGRADE);
        info.setCardNo(bizCardNos.get(c));
        info.setEffDate(LocalDate.of(2023, 1, 1));
        info.setDscDate(LocalDate.now().plusDays(1));
        info.setEffSaleDate(LocalDate.of(2023, 1, 1));
        info.setDscSaleDate(LocalDate.now().plusDays(1));

        infos.add(info);
      });

      output.setCampaignCreditCardInfos(infos);
      OUTPUTS.addAll(infos);
    });
  }
}
