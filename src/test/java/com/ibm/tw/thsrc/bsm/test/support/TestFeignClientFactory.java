/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.test.support;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.FeignClientBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

@Component
public class TestFeignClientFactory {

  @Autowired
  private ApplicationContext context;

  public <T> T create(Class<T> clazz, String path) {
    Integer port = context.getEnvironment().getProperty("local.server.port", Integer.class);
    return new FeignClientBuilder(context)
      .forType(clazz, clazz.getCanonicalName())
      .path(path)
      .url("http://localhost:" + port)
      .build();
  }
}

