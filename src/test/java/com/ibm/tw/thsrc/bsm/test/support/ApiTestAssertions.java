/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.test.support;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.core.dto.error.ErrorResponse;
import com.ibm.tw.thsrc.bsm.exception.UnprocessableErrorCode;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.function.Executable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ApiTestAssertions {

  @Autowired
  private ObjectMapper objectMapper;

  public void assertErrorCode(UnprocessableErrorCode errorCode, Executable executable) {
    FeignException.UnprocessableEntity unprocessableEntity = Assertions.assertThrows(
        FeignException.UnprocessableEntity.class,
        executable
    );
    try {
      String content = unprocessableEntity.contentUTF8();
      ErrorResponse errorResponse = objectMapper.readValue(content, ErrorResponse.class);
      Assertions.assertEquals(errorCode.getCode(), errorResponse.getErrorCode());
    } catch (JsonProcessingException e) {
      throw new RuntimeException("Failed to parse error response", e);
    }
  }
}
