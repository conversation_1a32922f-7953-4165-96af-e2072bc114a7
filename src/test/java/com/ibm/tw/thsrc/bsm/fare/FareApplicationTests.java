/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare;

import com.ibm.tw.thsrc.bsm.FareApplication;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest(classes = FareApplication.class)
@ActiveProfiles("test")
class FareApplicationTests {

  @Test
  void contextLoads() {
    Assertions.assertTrue(true);
  }
}
