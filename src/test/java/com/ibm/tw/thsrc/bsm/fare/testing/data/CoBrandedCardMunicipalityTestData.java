/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.fa.dto.CoBrandedCardMunicipalityOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.ElectronicMoneyType;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCardMunicipality;
import java.util.LinkedList;
import java.util.List;

public class CoBrandedCardMunicipalityTestData {

  public List<CoBrandedCardMunicipality> ENTITIES = new LinkedList<>();
  public List<CoBrandedCardMunicipalityOutput> OUTPUTS = new LinkedList<>();

  public CoBrandedCardMunicipalityTestData() {
    setEntities();
    setOutputs();
  }

  private void setEntities() {
    CoBrandedCardMunicipality municipality1 = new CoBrandedCardMunicipality();
    municipality1.setCode("1");
    municipality1.setName("台北");
    municipality1.setElectronicMoneyType(ElectronicMoneyType.EASY_CARD);
    ENTITIES.add(municipality1);
  }

  private void setOutputs() {
    CoBrandedCardMunicipalityOutput municipality1 = new CoBrandedCardMunicipalityOutput();
    municipality1.setId(1L);
    municipality1.setCode("1");
    municipality1.setName("台北");
    municipality1.setType(ElectronicMoneyType.EASY_CARD);
    OUTPUTS.add(municipality1);
  }
}
