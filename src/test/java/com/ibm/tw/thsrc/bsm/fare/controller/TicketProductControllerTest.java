/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationInput;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketProductInput;
import com.ibm.tw.thsrc.bsm.fa.dto.TicketProductOutput;
import com.ibm.tw.thsrc.bsm.fare.service.TicketProductCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.TicketProductQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.TicketProductTestData;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest(classes = {FareApplication.class})
@AutoConfigureMockMvc
@ActiveProfiles("test")
class TicketProductControllerTest {

  @Autowired
  private TicketProductController controller;

  @MockBean
  private TicketProductCommandService commandService;

  @MockBean
  private TicketProductQueryService queryService;

  @Autowired
  private MockMvc mockMvc;

  @Autowired
  private ObjectMapper mapper;

  private static final String API_PATH = "/TicketProducts";
  private static final TicketProductTestData TEST_DATA = new TicketProductTestData();

  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any()))
        .thenReturn(new PageImpl<>(TEST_DATA.RECENT_OUTPUTS));

    List<TicketProductOutput> expected = TEST_DATA.RECENT_OUTPUTS;

    Page<TicketProductOutput> resultPage = controller.search(new Search());
    Assertions.assertEquals(expected.size(), resultPage.getContent().size());
  }

  @Test
  void modifyCreations() {

    Set<Long> ids = TEST_DATA.UPSERT_OUTPUTS.stream().map(o -> o.getId())
        .collect(Collectors.toSet());

    Mockito.when(commandService.patch(ArgumentMatchers.any())).thenReturn(ids);

    Set<TicketProductOutput> prices = TEST_DATA.UPSERT_OUTPUTS;

    Mockito.when(queryService.read(ArgumentMatchers.anySet())).thenReturn(prices);

    Set<TicketProductOutput> results = controller.modify(TEST_DATA.CREATE_INPUT);

    for (TicketProductOutput expected : TEST_DATA.UPSERT_OUTPUTS) {
      Assertions.assertTrue(results.contains(expected));
    }

  }

  @Test
  void modifyReplacements() {

    Set<Long> ids = TEST_DATA.UPSERT_OUTPUTS.stream().map(o -> o.getId())
        .collect(Collectors.toSet());

    Mockito.when(commandService.patch(ArgumentMatchers.any())).thenReturn(ids);

    Set<TicketProductOutput> prices = TEST_DATA.UPSERT_OUTPUTS;

    Mockito.when(queryService.read(ArgumentMatchers.anySet())).thenReturn(prices);

    Set<TicketProductOutput> results = controller.modify(TEST_DATA.UPDATE_INPUT);

    for (TicketProductOutput expected : TEST_DATA.UPSERT_OUTPUTS) {
      Assertions.assertTrue(results.contains(expected));
    }
  }

  @Test
  void inputValidation() throws Exception {

    for (CollectionModificationInput<TicketProductInput> invalidInput : TEST_DATA.INVALID_INPUTS) {
      MvcResult mvcResult = mockMvc
          .perform(MockMvcRequestBuilders.patch(API_PATH)
              .content(mapper.writeValueAsString(invalidInput))
              .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
          .andReturn();

      Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(),
          mvcResult.getResponse().getStatus());
    }
  }
}
