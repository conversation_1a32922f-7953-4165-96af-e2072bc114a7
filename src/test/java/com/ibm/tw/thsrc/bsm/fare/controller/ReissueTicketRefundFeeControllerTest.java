/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */

package com.ibm.tw.thsrc.bsm.fare.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.ReissueTicketRefundFeeInput;
import com.ibm.tw.thsrc.bsm.fa.dto.ReissueTicketRefundFeeOutput;
import com.ibm.tw.thsrc.bsm.fare.service.ReissueTicketRefundFeeCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.ReissueTicketRefundFeeQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.ReissueTicketRefundFeeTestData;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest(classes = {FareApplication.class})
@AutoConfigureMockMvc
@ActiveProfiles("test")
class ReissueTicketRefundFeeControllerTest {

  private static final String API_PATH = "/ReissueTicketRefundFees/VersionUpserts";
  private static final ReissueTicketRefundFeeTestData TEST_DATA = new ReissueTicketRefundFeeTestData();
  @Autowired
  private ReissueTicketRefundFeeController controller;
  @MockBean
  private ReissueTicketRefundFeeCommandService commandService;
  @MockBean
  private ReissueTicketRefundFeeQueryService queryService;
  @Autowired
  private MockMvc mockMvc;
  @Autowired
  private ObjectMapper mapper;

  @Test
  void search() {
    Mockito.when(queryService.search()).thenReturn(new PageImpl<>(TEST_DATA.OUTPUTS));

    List<ReissueTicketRefundFeeOutput> expected = TEST_DATA.OUTPUTS;

    Page<ReissueTicketRefundFeeOutput> resultPage = controller.search(new Search());
    Assertions.assertEquals(expected.size(), resultPage.getContent().size());
  }

  @Test
  void upsert() {
    Mockito.when(queryService.search()).thenReturn(new PageImpl<>(TEST_DATA.OUTPUTS));
    List<ReissueTicketRefundFeeOutput> expected = TEST_DATA.OUTPUTS;
    List<ReissueTicketRefundFeeOutput> actual = controller.upsert(TEST_DATA.UPDATE_INPUT);

    Assertions.assertEquals(expected.size(), actual.size());
  }

  @Test
  void inputValidation() throws Exception {
    for (ReissueTicketRefundFeeInput invalidInput : TEST_DATA.INVALID_INPUTS) {
      MvcResult mvcResult = mockMvc
          .perform(MockMvcRequestBuilders.post(API_PATH)
              .content(mapper.writeValueAsString(invalidInput))
              .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON))
          .andReturn();

      Assertions.assertEquals(HttpStatus.BAD_REQUEST.value(),
          mvcResult.getResponse().getStatus());
    }
  }
}
