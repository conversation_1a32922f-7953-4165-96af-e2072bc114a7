/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import com.ibm.tw.thsrc.bsm.FareApplication;
import com.ibm.tw.thsrc.bsm.core.dto.command.CollectionModificationOutput;
import com.ibm.tw.thsrc.bsm.core.dto.filter.Search;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainInput;
import com.ibm.tw.thsrc.bsm.fa.dto.EligibleTrainOutput;
import com.ibm.tw.thsrc.bsm.fare.service.EligibleTrainBulkService;
import com.ibm.tw.thsrc.bsm.fare.service.EligibleTrainCommandService;
import com.ibm.tw.thsrc.bsm.fare.service.EligibleTrainQueryService;
import com.ibm.tw.thsrc.bsm.fare.testing.data.EligibleTrainTestData;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@SpringBootTest(classes = {FareApplication.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@AutoConfigureMockMvc
class EligibleTrainControllerTest {

  private final String API_PATH = "/EligibleTrains/1";
  private final EligibleTrainTestData TEST_DATA = new EligibleTrainTestData();
  @Autowired
  private EligibleTrainController controller;
  @MockBean
  private EligibleTrainCommandService commandService;
  @MockBean
  private EligibleTrainBulkService bulkService;
  @MockBean
  private EligibleTrainQueryService queryService;
  @Autowired
  private MockMvc mockMvc;
  public EligibleTrainControllerTest() throws Exception {

  }

  @Test
  void search() {
    Mockito.when(queryService.search(ArgumentMatchers.any())).thenReturn(new PageImpl<>(TEST_DATA.OUTPUTS));

    List<EligibleTrainOutput> expected = TEST_DATA.OUTPUTS;
    Search search = new Search();
    Page<EligibleTrainOutput> actual = controller.search(search);

    Assertions.assertEquals(expected.size(), actual.getContent().size());
  }

  @Test
  void read() {
    Long testId = Long.MAX_VALUE - 1;
    Mockito.when(queryService.read(testId)).thenReturn(TEST_DATA.OUTPUTS.get(0));

    EligibleTrainOutput expected = TEST_DATA.OUTPUTS.get(0);
    EligibleTrainOutput actual = controller.read(testId);

    Assertions.assertEquals(expected.getEffDate(), actual.getEffDate());
    Assertions.assertEquals(expected.getDscDate(), actual.getDscDate());
    Assertions.assertEquals(expected.getEffSaleDate(), actual.getEffSaleDate());
    Assertions.assertEquals(expected.getIsAllTrainEligible(),
        actual.getIsAllTrainEligible());
    Assertions.assertEquals(expected.getIsHolidayIneligible(),
        actual.getIsHolidayIneligible());
    Assertions.assertEquals(expected.getPriority(), actual.getPriority());
    Assertions.assertEquals(expected.getRemark(), actual.getRemark());
    if (Objects.nonNull(expected.getPromotion())) {
      Assertions.assertEquals(expected.getPromotion().getId(),
          actual.getPromotion().getId());
      Assertions.assertEquals(expected.getPromotion().getCode(),
          actual.getPromotion().getCode());
      Assertions.assertEquals(expected.getPromotion().getName(),
          actual.getPromotion().getName());
    }
    if (Objects.nonNull(expected.getBasicFarePlan())) {
      Assertions.assertEquals(expected.getBasicFarePlan().getId(),
          actual.getBasicFarePlan().getId());
      Assertions.assertEquals(expected.getBasicFarePlan().getCode(),
          actual.getBasicFarePlan().getCode());
      Assertions.assertEquals(expected.getBasicFarePlan().getName(),
          actual.getBasicFarePlan().getName());
    }

    if (Objects.nonNull(expected.getFareProject())) {
      Assertions.assertEquals(expected.getFareProject().getId(),
          actual.getFareProject().getId());
      Assertions.assertEquals(expected.getFareProject().getName(),
          actual.getFareProject().getName());
      Assertions.assertEquals(expected.getFareProject().getCode(),
          actual.getFareProject().getCode());
      Assertions.assertEquals(expected.getDetails().size(),
          actual.getDetails().size());
    }
  }

  @Test
  void bulk() {
    Long testId = Long.MAX_VALUE - 1;
    Mockito.when(commandService.create(ArgumentMatchers.any())).thenReturn(testId);
    Mockito.when(bulkService.bulk(TEST_DATA.BULK_CREATE_BASIC_PLAN_INPUT)).thenReturn(TEST_DATA.BULK_OUTPUT);

    CollectionModificationOutput actual = controller.bulk(TEST_DATA.BULK_CREATE_BASIC_PLAN_INPUT);
    actual.getCreations()
        .forEach(e -> Assertions.assertTrue(e.contains(TEST_DATA.BULK_SUCCESS_MSG)));
    actual.getDeletions()
        .forEach((k, e) -> Assertions.assertTrue(e.contains(TEST_DATA.BULK_ERROR_MSG)));
  }

  @Test
  void replace() {

    Long testId = TEST_DATA.ENTITIES.get(1).getId();
    EligibleTrainInput input = TEST_DATA.UPDATE_PROMOTION_INPUT;
    Mockito.when(queryService.read(ArgumentMatchers.anyLong())).thenReturn(TEST_DATA.OUTPUTS.get(1));
    EligibleTrainOutput expected = TEST_DATA.OUTPUTS.get(1);
    EligibleTrainOutput actual = controller.replace(testId, input);

    Assertions.assertEquals(expected.getEffDate(), actual.getEffDate());
    Assertions.assertEquals(expected.getDscDate(), actual.getDscDate());
    Assertions.assertEquals(expected.getEffSaleDate(), actual.getEffSaleDate());
    Assertions.assertEquals(expected.getIsAllTrainEligible(),
        actual.getIsAllTrainEligible());
    Assertions.assertEquals(expected.getIsHolidayIneligible(),
        actual.getIsHolidayIneligible());
    Assertions.assertEquals(expected.getPriority(), actual.getPriority());
    Assertions.assertEquals(expected.getRemark(), actual.getRemark());
    if (Objects.nonNull(expected.getPromotion())) {
      Assertions.assertEquals(expected.getPromotion().getId(),
          actual.getPromotion().getId());
      Assertions.assertEquals(expected.getPromotion().getCode(),
          actual.getPromotion().getCode());
      Assertions.assertEquals(expected.getPromotion().getName(),
          actual.getPromotion().getName());
    }
    if (Objects.nonNull(expected.getBasicFarePlan())) {
      Assertions.assertEquals(expected.getBasicFarePlan().getId(),
          actual.getBasicFarePlan().getId());
      Assertions.assertEquals(expected.getBasicFarePlan().getCode(),
          actual.getBasicFarePlan().getCode());
      Assertions.assertEquals(expected.getBasicFarePlan().getName(),
          actual.getBasicFarePlan().getName());
    }

    if (Objects.nonNull(expected.getFareProject())) {
      Assertions.assertEquals(expected.getFareProject().getId(),
          actual.getFareProject().getId());
      Assertions.assertEquals(expected.getFareProject().getName(),
          actual.getFareProject().getName());
      Assertions.assertEquals(expected.getFareProject().getCode(),
          actual.getFareProject().getCode());
      Assertions.assertEquals(expected.getDetails().size(),
          actual.getDetails().size());
    }
  }

  @Test
  void delete() throws Exception {
    MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
            .delete(API_PATH))
        .andExpect(status().isOk())
        .andReturn();

    String content = mvcResult.getResponse().getContentAsString();
    assertEquals(StringUtils.EMPTY, content);
  }
}
