/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
package com.ibm.tw.thsrc.bsm.fare.testing.data;

import com.ibm.tw.thsrc.bsm.fa.dto.CardIssuingBankInput;
import com.ibm.tw.thsrc.bsm.fa.dto.CardIssuingBankOutput;
import com.ibm.tw.thsrc.bsm.fa.enums.ElectronicMoneyType;
import com.ibm.tw.thsrc.bsm.fare.entity.CardIssuingBank;
import com.ibm.tw.thsrc.bsm.fare.entity.CoBrandedCard;
import com.ibm.tw.thsrc.bsm.util.BeanUtils;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.IntStream;

public class CardIssuingBankTestData {

  public List<String> ipaCode = Arrays.asList("001", "002", "003", "004", "005");
  public List<String> ezCode = Arrays.asList("006", "006", "007", "008", "009");
  public List<String> name = Arrays.asList("合庫", "彰化銀行", "台灣銀行", "玉山銀行", "台新銀行");
  public CoBrandedCard EZ_CARD = new CoBrandedCard();
  public CoBrandedCard IPA_CARD = new CoBrandedCard();
  public List<CardIssuingBank> ENTITIES = new LinkedList<>();
  public List<CardIssuingBank> EZ_ENTITIES = new LinkedList<>();
  public List<CardIssuingBank> IPA_ENTITIES = new LinkedList<>();
  public List<CardIssuingBankOutput> EZ_OUTPUTS = new LinkedList<>();
  public List<CardIssuingBankOutput> IPA_OUTPUTS = new LinkedList<>();
  public CardIssuingBankInput CREATE_INPUT = new CardIssuingBankInput();
  public CardIssuingBankInput DUPLICATE_CODE_INPUT = new CardIssuingBankInput();
  public CardIssuingBankInput DUPLICATE_NAME_INPUT = new CardIssuingBankInput();
  public List<CardIssuingBankInput> INVALID_INPUTS = new LinkedList<>();


  public CardIssuingBankTestData() {
    setEntities();
    setOutputs();
    setInputs();
  }

  public void setEntities() {
    EZ_CARD.setName("悠遊卡");
    EZ_CARD.setAutoReloaded(Boolean.TRUE);
    EZ_CARD.setTransferBonusMinsLimit(0);
    EZ_CARD.setDiffStationEntryExitSecsLimit(0);
    EZ_CARD.setSameStationEntryExitSecsLimit(0);
    EZ_CARD.setElectronicMoneyType(ElectronicMoneyType.EASY_CARD);

    BeanUtils.copyProperties(IPA_CARD, EZ_CARD);
    IPA_CARD.setElectronicMoneyType(ElectronicMoneyType.I_PASS);

    IntStream.range(0, name.size()).forEach(e -> {
      CardIssuingBank bank = new CardIssuingBank();
      bank.setName(name.get((e)));
      bank.setCode(ezCode.get(e));
      bank.setCoBrandedCard(EZ_CARD);
      EZ_ENTITIES.add(bank);
      ENTITIES.add(bank);
    });
    IntStream.range(0, name.size()).forEach(e -> {
      CardIssuingBank bank = new CardIssuingBank();
      bank.setName(name.get((e)));
      bank.setCode(ipaCode.get(e));
      bank.setCoBrandedCard(IPA_CARD);
      IPA_ENTITIES.add(bank);
      ENTITIES.add(bank);
    });
  }

  public void setOutputs() {
    IntStream.range(0, name.size()).forEach(e -> {
      CardIssuingBankOutput bank = new CardIssuingBankOutput();
      bank.setId(e + 1L);
      bank.setName(name.get((e)));
      bank.setCode(ezCode.get(e));
      bank.setCardType(ElectronicMoneyType.EASY_CARD);

      EZ_OUTPUTS.add(bank);
    });

    IntStream.range(0, name.size()).forEach(e -> {
      CardIssuingBankOutput bank = new CardIssuingBankOutput();
      bank.setId(e + (long) name.size() + 1L);
      bank.setName(name.get((e)));
      bank.setCode(ipaCode.get(e));
      bank.setCardType(ElectronicMoneyType.I_PASS);

      IPA_OUTPUTS.add(bank);
    });
  }

  public void setInputs() {
    CREATE_INPUT = getBasicInput();

    // invalid for services
    DUPLICATE_CODE_INPUT = getBasicInput();
    DUPLICATE_CODE_INPUT.setCode(ipaCode.get(0));
    DUPLICATE_NAME_INPUT = getBasicInput();
    DUPLICATE_NAME_INPUT.setName(name.get(0));

    // invalid for controller
    CardIssuingBankInput inputWithNull = getBasicInput();
    inputWithNull.setName(null);

    CardIssuingBankInput inputWithCodeOver3 = getBasicInput();
    inputWithCodeOver3.setCode("1234");

    CardIssuingBankInput inputWithNameOver10 = getBasicInput();
    inputWithNameOver10.setName("ABCDEFGHIJK");
    INVALID_INPUTS.clear();
    INVALID_INPUTS.add(inputWithNull);
    INVALID_INPUTS.add(inputWithCodeOver3);
    INVALID_INPUTS.add(inputWithNameOver10);
  }

  private CardIssuingBankInput getBasicInput() {
    CardIssuingBankInput input = new CardIssuingBankInput();
    input.setCode("822");
    input.setName("中信");
    input.setCoBrandedCardId(IPA_CARD.getId());

    return input;
  }
}
