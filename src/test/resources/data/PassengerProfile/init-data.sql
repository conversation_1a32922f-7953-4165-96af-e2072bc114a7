DELETE
FROM PROFILE_DISCOUNT_PLAN
WHERE ID > 0;
DELETE
FROM FREE_SEATING_PROFILE_DISCOUNT
WHERE ID > 0;
DELETE
FROM PROFILE_DISCOUNT_TYPE
WHERE ID > 0;
DELETE
FROM FREE_SEATING_TICKET_TYPE
WHERE ID > 0;
DELETE
FROM PASSENGER_PROFILE
WHERE ID > 0;

INSERT INTO PASSENGER_PROFILE (DATA_VERSION, CREATE_TIMESTAMP, UPDATE_TIMESTAMP, CODE, IS_DISPLAY, DISPLAY_ORDER,
                               EN_NAME,
                               EN_PRINT_NAME, ZH_NAME, ZH_PRINT_NAME, TB_LIGHT_COLOR)
VALUES (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'F', 1, 1, 'Adult', 'ADT', N'成人', N'成人', N'綠'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'H', 1, 2, 'Child', 'CHD', N'孩童', N'孩童', N'綠'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'E', 1, 3, 'Senior', 'SE', N'敬老', N'敬老', N'橘'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'W', 1, 4, 'Handicapped', 'HAD', N'愛心', N'愛心', N'橘'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'P', 1, 5, 'Student', 'STU', N'學生', N'學生', N'橘'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'T', 1, 6, 'Under65', 'U65', N'孩優惠', N'孩優惠', N'橘'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'S', 1, 7, 'CouponFree', 'CFT', N'特惠全', N'特惠全', N'綠'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'M', 1, 8, 'CouponHalf', 'CHT', N'特惠半', N'特惠半', N'綠');

INSERT INTO PROFILE_DISCOUNT_TYPE (DATA_VERSION, CREATE_TIMESTAMP, UPDATE_TIMESTAMP, CODE, NAME)
VALUES (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'A', N'組合A'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'B', N'組合B'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'C', N'組合C'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'D', N'組合D'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'E', N'組合E'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'F', N'組合F'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'G', N'組合G'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'H', N'組合H');

INSERT INTO FREE_SEATING_TICKET_TYPE (DATA_VERSION, CREATE_TIMESTAMP, UPDATE_TIMESTAMP, CODE, NAME)
VALUES (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '1', N'預設值'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '2', ''),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '3', ''),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '4', N'去回票'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '5', N'單程票'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '6', N'補償票'),
       (0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '7', N'回數票');

INSERT INTO PROFILE_DISCOUNT_PLAN (DATA_VERSION, CREATE_TIMESTAMP, UPDATE_TIMESTAMP, DISCOUNT_PCT,
                                   PASSENGER_PROFILE_ID, PROFILE_DISCOUNT_TYPE_ID)
select 0,
       CURRENT_TIMESTAMP,
       CURRENT_TIMESTAMP,
       100,
       PR.ID  as PASSENGER_PROFILE_ID,
       PDT.ID as PROFILE_DISCOUNT_TYPE_ID
from PASSENGER_PROFILE PR
         cross join PROFILE_DISCOUNT_TYPE PDT;

INSERT INTO FREE_SEATING_PROFILE_DISCOUNT (DATA_VERSION, CREATE_TIMESTAMP, UPDATE_TIMESTAMP, DISCOUNT_PCT,
                                           PASSENGER_PROFILE_ID, FREE_SEATING_TICKET_TYPE_ID)
select 0,
       CURRENT_TIMESTAMP,
       CURRENT_TIMESTAMP,
       100,
       PR.ID   as PASSENGER_PROFILE_ID,
       FSTT.ID as FREE_SEATING_TICKET_TYPE_ID
from PASSENGER_PROFILE PR
         cross join FREE_SEATING_TICKET_TYPE FSTT;

UPDATE PROFILE_DISCOUNT_PLAN
SET DISCOUNT_PCT = 50
WHERE ID IN (
    SELECT PDP.ID
    FROM PROFILE_DISCOUNT_PLAN PDP
             JOIN PROFILE_DISCOUNT_TYPE PDT ON PDT.ID = PDP.PROFILE_DISCOUNT_TYPE_ID
             JOIN PASSENGER_PROFILE PP ON PP.ID = PDP.PASSENGER_PROFILE_ID
    WHERE PP.CODE in ('H', 'E', 'W')
);

UPDATE PROFILE_DISCOUNT_PLAN
SET DISCOUNT_PCT = 86
WHERE ID IN (
    SELECT PDP.ID
    FROM PROFILE_DISCOUNT_PLAN PDP
             JOIN PROFILE_DISCOUNT_TYPE PDT ON PDT.ID = PDP.PROFILE_DISCOUNT_TYPE_ID
             JOIN PASSENGER_PROFILE PP ON PP.ID = PDP.PASSENGER_PROFILE_ID
    WHERE PP.CODE in ('F', 'P')
      AND PDT.CODE in ('E', 'F', 'G')
);


UPDATE PROFILE_DISCOUNT_PLAN
SET DISCOUNT_PCT = 0
WHERE ID IN (
    SELECT PDP.ID
    FROM PROFILE_DISCOUNT_PLAN PDP
             JOIN PROFILE_DISCOUNT_TYPE PDT ON PDT.ID = PDP.PROFILE_DISCOUNT_TYPE_ID
             JOIN PASSENGER_PROFILE PP ON PP.ID = PDP.PASSENGER_PROFILE_ID
    WHERE PP.CODE in ('S')
      AND PDT.CODE in ('A', 'B', 'C', 'D', 'F')
);

UPDATE PROFILE_DISCOUNT_PLAN
SET DISCOUNT_PCT = 0
WHERE ID IN (
    SELECT PDP.ID
    FROM PROFILE_DISCOUNT_PLAN PDP
             JOIN PROFILE_DISCOUNT_TYPE PDT ON PDT.ID = PDP.PROFILE_DISCOUNT_TYPE_ID
    WHERE PDT.CODE in ('C', 'D')
);
