spring.datasource.url=jdbc:h2:mem:bsm;MODE=MSSQLServer;INIT=CREATE SCHEMA IF NOT EXISTS fa\\;CREATE SCHEMA IF NOT EXISTS IV\\;CREATE SCHEMA IF NOT EXISTS SC\\;CREATE SCHEMA IF NOT EXISTS DBO\\;CREATE SCHEMA IF NOT EXISTS MEIG\\;CREATE SCHEMA IF NOT EXISTS AUDIT\\;CREATE SCHEMA IF NOT EXISTS RES;IGNORECASE=true;DATABASE_TO_UPPER=false
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.default_schema=fa
spring.jpa.hibernate.ddl-auto=create
spring.jpa.show-sql=false
#spring.jpa.properties.javax.persistence.schema-generation.database.action=create
spring.jpa.database=h2
feign.autoconfiguration.jackson.enabled=true
integration.meig.enabled=false
integration.meig.mock-controller=true
integration.meig.url=http://localhost:${server.port}
logging.level.com.ibm.tw.thsrc.bsm.meig.controller=DEBUG
integration.res.class-name=com.ibm.tw.thsrc.bsm.res.connection.MockSocketConnection
