/*
 * Copyright © 2023 IBM Corporation. IBM retains ownership, copyrights, any title of this source code. IBM Confidential.
 */
plugins {
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
    id 'java'
    id 'org.sonarqube'
}

apply plugin: "jacoco"

group = 'com.ibm.tw.thsrc.bsm'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '1.8'

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
    all {
    	exclude group: 'org.yaml', module: 'snakeyaml'
    }
}

ext {
    set('springCloudVersion', "2021.0.3")
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'com.cosium.spring.data:spring-data-jpa-entity-graph:2.7.10'
    implementation 'org.apache.commons:commons-lang3'

    implementation "com.ibm.tw.thsrc.bsm:aors-logger:${aors_libs_version}"
    implementation "com.ibm.tw.thsrc.bsm:aors-alert:${aors_libs_version}"

    implementation "com.ibm.tw.thsrc.bsm:common-util-lib:${bsm_libs_version}"
    implementation "com.ibm.tw.thsrc.bsm:common-exception-lib:${bsm_libs_version}"
    implementation "com.ibm.tw.thsrc.bsm:common-api-lib:${bsm_libs_version}"

    implementation "com.ibm.tw.thsrc.bsm:ap-common-lib:${bsm_libs_version}"
    implementation "com.ibm.tw.thsrc.bsm:res-service-lib:${bsm_libs_version}"
    implementation "com.ibm.tw.thsrc.bsm:meig-service-lib:${bsm_libs_version}"
    implementation "com.ibm.tw.thsrc.bsm:ap-service-spi-lib:${bsm_libs_version}"
    implementation "com.ibm.tw.thsrc.bsm:ap-service-api-lib:${bsm_libs_version}"
    implementation "com.ibm.tw.thsrc.bsm:ap-domain-event-lib:${bsm_libs_version}"
    implementation "com.ibm.tw.thsrc.bsm:res-event-lib:${bsm_libs_version}"

	implementation "com.ibm.tw.thsrc.bsm:ap-cronjob-lib:${bsm_libs_version}"
    implementation "com.ibm.tw.thsrc.bsm:audit-search-api-lib:${bsm_libs_version}"
    implementation "com.ibm.tw.thsrc.bsm:audit-subdomain-lib:${bsm_libs_version}"
    implementation "com.ibm.tw.thsrc.bsm:audit-appender-api-lib:${bsm_libs_version}"
    implementation "com.ibm.tw.thsrc.bsm:audit-appender-db-lib:${bsm_libs_version}"
    implementation "com.ibm.tw.thsrc.bsm:audit-appender-rest-lib:${bsm_libs_version}"

    runtimeOnly group: 'com.microsoft.sqlserver', name: 'mssql-jdbc', version: '11.2.1.jre8'
    compileOnly 'org.projectlombok:lombok'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    annotationProcessor 'org.projectlombok:lombok'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'com.h2database:h2'
    testImplementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    testImplementation 'io.github.openfeign:feign-httpclient'


    annotationProcessor('org.hibernate:hibernate-jpamodelgen')
    
}

tasks.withType(JavaCompile) {
    options.annotationProcessorGeneratedSourcesDirectory(file("build/generated/java"))
}

sourceSets {
    generated {
        java {
            srcDirs = ['build/generated/java']
        }
    }
}

bootJar {
    enabled = true
    archiveClassifier = ''
}

jar {
    enabled = false
}

test {
    useJUnitPlatform()
    ignoreFailures true // Boolean.getBoolean("test.ignoreFailures")
    systemProperty 'spring.profiles.active', 'test'
    maxHeapSize = "2048m"
}

jacocoTestReport {
    reports {
        xml.enabled true
    }
}

sonarqube {
    properties {
        property "wrapper.java.command", "${java11_dir}"
        property "sonar.host.url", "${sonarHost}"
        property "sonar.login", "${sonarUsername}"
        property "sonar.password", "${sonarPassword}"
        property "sonar.coverage.jacoco.xmlReportPaths", "${buildDir}/reports/jacoco/test/jacocoTestReport.xml"
    }
}

tasks['sonarqube'].with {
    dependsOn tasks.withType(Test)
}
tasks.getByPath('sonarqube').setDependsOn([])

ext {
    dockerImageName = "${project.name}"
    workingDir = System.getProperty("user.dir")
}

def executeCmd(def cmd, boolean suppressed = false, String suppressedReason = "") {
    def output = new StringBuffer()
    if (!suppressed)
        println "Executing command: " + cmd
    else
        println "Executing command (masked): " + suppressedReason
    def process = ['bash', '-c', cmd].execute()
    process.waitForProcessOutput(output, output)
    if (process.exitValue() != 0) throw new GradleException(output.toString())
    println output.toString()
    return output.toString()
}

def dockerToolRegistryParametersValidation() {
    if (!project.hasProperty("dockerToolRegistryUser")) {
        throw new GradleException("Parameter value for 'dockerToolRegistryUser' is required.")
    } else if (project.getProperty("dockerToolRegistryUser") == null || project.getProperty("dockerToolRegistryUser").isEmpty()) {
        throw new GradleException("Parameter value of 'dockerToolRegistryUser' cannot be null or empty.")
    }

    if (!project.hasProperty("dockerToolRegistryPassword")) {
        throw new GradleException("Parameter value for 'dockerToolRegistryPassword' is required.")
    } else if (project.getProperty("dockerToolRegistryPassword") == null || project.getProperty("dockerToolRegistryPassword").isEmpty()) {
        throw new GradleException("Parameter value of 'dockerToolRegistryPassword' cannot be null or empty.")
    }

    if (!project.hasProperty("dockerImageRegistryUrl")) {
        throw new GradleException("Parameter value for 'dockerImageRegistryUrl' is required.")
    } else if (project.getProperty("dockerImageRegistryUrl") == null || project.getProperty("dockerImageRegistryUrl").isEmpty()) {
        throw new GradleException("Parameter value of 'dockerImageRegistryUrl' cannot be null or empty.")
    }
}

def dockerAppRegistryParametersValidation() {
    if (!project.hasProperty("dockerAppRegistryUser")) {
        throw new GradleException("Parameter value for 'dockerAppRegistryUser' is required.")
    } else if (project.getProperty("dockerAppRegistryUser") == null || project.getProperty("dockerAppRegistryUser").isEmpty()) {
        throw new GradleException("Parameter value of 'dockerAppRegistryUser' cannot be null or empty.")
    }

    if (!project.hasProperty("dockerAppRegistryPassword")) {
        throw new GradleException("Parameter value for 'dockerAppRegistryPassword' is required.")
    } else if (project.getProperty("dockerAppRegistryPassword") == null || project.getProperty("dockerAppRegistryPassword").isEmpty()) {
        throw new GradleException("Parameter value of 'dockerAppRegistryPassword' cannot be null or empty.")
    }

    if (!project.hasProperty("dockerImageRegistryUrl")) {
        throw new GradleException("Parameter value for 'dockerImageRegistryUrl' is required.")
    } else if (project.getProperty("dockerImageRegistryUrl") == null || project.getProperty("dockerImageRegistryUrl").isEmpty()) {
        throw new GradleException("Parameter value of 'dockerImageRegistryUrl' cannot be null or empty.")
    }
}

def loginDockerToolRegistry() {
    boolean suppressed = true
    dockerToolRegistryParametersValidation()

    def cmdLoginDockerRegistry = "echo ${dockerToolRegistryPassword} | docker login -u ${dockerToolRegistryUser} --password-stdin ${dockerImageRegistryUrl}"
    executeCmd(cmdLoginDockerRegistry, suppressed, "echo **** | docker login -u ***** --password-stdin ${dockerImageRegistryUrl}")
}

def loginDockerAppRegistry() {
    boolean suppressed = true
    dockerAppRegistryParametersValidation()

    def cmdLoginDockerRegistry = "echo ${dockerAppRegistryPassword} | docker login -u ${dockerAppRegistryUser} --password-stdin ${dockerImageRegistryUrl}"
    executeCmd(cmdLoginDockerRegistry, suppressed, "echo **** | docker login -u ***** --password-stdin ${dockerImageRegistryUrl}")
}

task removeDockerContainer {
    description = "Remove any running ${project.name} container."
    doLast {
        def cmdStop = "docker stop ${project.name}"
        try {
            executeCmd cmdStop
        } catch (GradleException e) {
            if (!e.getMessage().contains("No such container: ${project.name}")) {
                throw e
            }
        }

        def cmdRemove = "docker rm ${project.name}"
        try {
            executeCmd cmdRemove
        } catch (GradleException e) {
            if (!e.getMessage().contains("No such container: ${project.name}")) {
                throw e
            }
        }
    }
}

task removeDockerImage(dependsOn: removeDockerContainer) {
    description = "Remove any ${dockerImageName} image."

    doLast {
        def cmdRemoveImage = "docker images | grep ${dockerImageName} | awk '{ print \$3; }' | xargs docker rmi -f"
        try {
            executeCmd cmdRemoveImage
        } catch (GradleException e) {
            if (!e.getMessage().contains("\"docker rmi\" requires at least 1 argument.") &&
                    !e.getMessage().contains("\"docker rmi\" requires at least 1 argument(s).")) {
                throw e
            }
        }
    }
}

removeDockerImage.mustRunAfter "removeDockerContainer"

task buildDockerImage(dependsOn: ["removeDockerImage", "build"]) {
    description = "Build the docker image of Websphere Liberty Developer version that run the springboot app."

    def dockerfileLocation = "${project.name}"
    println "Working directory: ${workingDir}"

    doLast {
        def image
        if (project.hasProperty("websphereImage")) {
            image = "${websphereImage}"
        } else {
            loginDockerToolRegistry()
            def registry_url = "${dockerImageRegistryUrl}/${dockerBaseImageNamespace}"
            image = "${registry_url}/${baseImage}"
        }

        def cmdDockerBuild = "docker build -t ${dockerImageName} --build-arg WEBSPHERE_IMAGE=${image} ."
        executeCmd cmdDockerBuild
    }
}

task pushDockerImage() {
    description = "Push the spring boot application docker image to a Container Registry."

    doLast {
        // tag the ${project.name}-app image to the format given by qauy
        def cmdTag = "docker tag ${dockerImageName} ${dockerImageRegistryUrl}/${dockerImageNamespace}/${dockerImageName}:${imageVersion}"
        executeCmd cmdTag

        loginDockerAppRegistry()

        def cmdDockerPush = "docker push ${dockerImageRegistryUrl}/${dockerImageNamespace}/${dockerImageName}:${imageVersion}"
        executeCmd cmdDockerPush

        // Remove local Image after push
        def cmdRemoveOldImage = "docker image rm -f ${dockerImageRegistryUrl}/${dockerImageNamespace}/${dockerImageName}:${imageVersion}"
        try {
            executeCmd cmdRemoveOldImage
        } catch (GradleException e) {
            if (!e.getMessage().contains("No such image")) {
                throw e
            }
        }
    }
}
pushDockerImage.mustRunAfter "buildDockerImage"


task validateContainer(dependsOn: pushDockerImage) {
    doLast {
        println "The container image has been successfully validated and pushed to ${dockerImageRegistryUrl}/${dockerImageNamespace}."
    }
}

def loginOpenShift() {
    description = 'Login to OpenShift.'

    if (!project.hasProperty("ocToken")) {
        throw new GradleException("Parameter value for 'ocToken' is required.")
    } else if (project.getProperty("ocToken") == null || project.getProperty("ocToken").isEmpty()) {
        throw new GradleException("Parameter value of 'ocToken' cannot be null or empty.")
    }

    if (!project.hasProperty("ocServer")) {
        throw new GradleException("Parameter value for 'ocServer' is required.")
    } else if (project.getProperty("ocServer") == null || project.getProperty("ocServer").isEmpty()) {
        throw new GradleException("Parameter value of 'ocServer' cannot be null or empty.")
    }

    def cmdLoginOpenShift = "oc login --token=${ocToken} --server=${ocServer}"
    executeCmd cmdLoginOpenShift

    def cmdOcProject = "oc project ${ocProjectName}"
    executeCmd cmdOcProject
}

task checkPodStatus() {
    description = "Check the deployed pods status."

    doLast {
        def tryLimit = 100

        def podReady = false
        def tryCheckPodStatusCount = 0

        def cmdGetPodStatus = "oc get dc ${project.name} | grep ${project.name} | cut -d' ' -f1,4,13,22"
        String podStatusResult = executeCmd cmdGetPodStatus
        def fields = podStatusResult.split(" ")
        while (!podReady && tryCheckPodStatusCount < tryLimit) {
            if (!fields[2].trim().equals(fields[3].trim())) {
                sleep(1000)
                println "Retry pods status checking: " + tryCheckPodStatusCount++
                podStatusResult = executeCmd cmdGetPodStatus
                fields = podStatusResult.split(" ")
            } else {
                podReady = true
            }
        }
        if (!podReady) {
            throw new GradleException("Not all pods are ready! Run 'kubectl describe pod <pod-id>' to debug the problem.")
        }

        def rolloutSuccess = false
        def tryCheckRolloutStatusCount = 0

        def cmdCheckRolloutStatus = "oc rollout status dc ${project.name} -w=false"
        String rolloutStatus = executeCmd cmdCheckRolloutStatus
        while (!rolloutSuccess && tryCheckRolloutStatusCount < tryLimit) {
            if (!rolloutStatus.contains("successfully rolled out")) {
                sleep(5000)
                println "Retry roll-out status checking: " + tryCheckRolloutStatusCount++
                rolloutStatus = executeCmd cmdCheckRolloutStatus
            } else {
                rolloutSuccess = true
            }
        }

        if (!rolloutSuccess) {
            throw new GradleException("Roll-Out failed! Run 'oc rollout status dc <dc-name>' to debug the problem.")
        }

    }
}

checkPodStatus.mustRunAfter "ocPatchDcToNewVersion"

task ocPatchDcToNewVersion() {
    description = "Tag latest tag to new version."

    doLast {
        loginOpenShift()
        def cmdOcPatchDc = "oc patch dc/${project.name} --patch '{\"spec\":{\"template\":{\"spec\":{\"containers\":[{\"name\":\"${project.name}\",\"image\":\"${dockerImageRegistryUrl}/${dockerImageNamespace}/${dockerImageName}:${imageVersion}\"}]}}}}'"
        executeCmd cmdOcPatchDc
    }
}

task deployContainer(dependsOn: [
        'ocPatchDcToNewVersion',
        'checkPodStatus'
]) {
    doLast {
        println "The container has been successfully deployed."
    }
}