# Development Guide

## Getting Started

This guide covers the development setup, coding standards, and best practices for contributing to the FA-PD-APP project.

## Development Environment Setup

### 1. Prerequisites

**Required Software**:
- **Java**: OpenJDK 1.8+ (recommended: OpenJDK 11)
- **IDE**: IntelliJ IDEA (recommended) or Eclipse
- **Database**: SQL Server 2019+ (or Docker container)
- **Redis**: 6.0+ (or Docker container)
- **Git**: Latest version

**IDE Configuration**:

**IntelliJ IDEA**:
1. Install Lombok plugin
2. Enable annotation processing: `Settings → Build → Compiler → Annotation Processors`
3. Import Gradle project
4. Set Project SDK to Java 1.8+

**Eclipse**:
1. Install Lombok: Download lombok.jar and run `java -jar lombok.jar`
2. Import as Gradle project
3. Enable annotation processing

### 2. Local Database Setup

**Using Docker (Recommended)**:
```bash
# Start SQL Server
docker run -e "ACCEPT_EULA=Y" -e "SA_PASSWORD=P@ssw0rd" \
  -p 1433:1433 --name sqlserver \
  -d mcr.microsoft.com/mssql/server:2019-latest

# Start Redis
docker run --name redis -p 6377:6379 -d redis:6.2-alpine

# Create database
docker exec -it sqlserver /opt/mssql-tools/bin/sqlcmd \
  -S localhost -U sa -P "P@ssw0rd" \
  -Q "CREATE DATABASE BSMDB"
```

**Manual Installation**:
- Install SQL Server 2019+
- Create database `BSMDB`
- Create schema `fa`
- Install Redis 6.0+

### 3. Project Setup

```bash
# Clone repository
git clone <repository-url>
cd fa-pd-app

# Make gradlew executable (Unix/Mac)
chmod +x gradlew

# Build project
./gradlew build

# Run tests
./gradlew test

# Start application
./gradlew bootRun
```

**Verify Setup**:
- Application starts on `http://localhost:8087`
- Health check: `http://localhost:8087/actuator/health`
- Database schema is created automatically

## Project Structure Deep Dive

### 1. Package Organization

```
com.ibm.tw.thsrc.bsm.fare/
├── config/                 # Configuration classes
│   └── StepConfiguration.java
├── controller/             # REST controllers (42+ controllers)
│   ├── BasicFarePlanController.java
│   ├── FareEvaluationController.java
│   └── ...
├── service/               # Business logic layer
│   ├── command/           # Write operations
│   │   ├── BasicFarePlanCommandService.java
│   │   └── ...
│   └── query/             # Read operations
│       ├── BasicFarePlanQueryService.java
│       └── ...
├── entity/                # JPA entities
│   ├── BasicFarePlan.java
│   ├── FarePlan.java (abstract)
│   └── ...
├── repository/            # Data access layer
│   ├── BasicFarePlanRepository.java
│   └── ...
├── stepservice/           # Multi-step processes
├── translate/             # Entity-DTO translation
├── vo/                   # Value objects (filters, sorts)
└── scheduler/            # Background jobs
```

### 2. Naming Conventions

**Classes**:
- Controllers: `*Controller.java`
- Command Services: `*CommandService.java`
- Query Services: `*QueryService.java`
- Entities: Domain names (e.g., `BasicFarePlan.java`)
- Repositories: `*Repository.java`
- Value Objects: `*Filter.java`, `*Sort.java`

**Methods**:
- Command operations: `create*`, `update*`, `delete*`
- Query operations: `find*`, `get*`, `search*`
- Boolean methods: `is*`, `has*`, `can*`

**Database**:
- Tables: UPPERCASE with underscores (e.g., `BASIC_FARE_PLAN`)
- Columns: UPPERCASE with underscores (e.g., `EFFECTIVE_DATE`)
- Schema: `fa` (fare)

## Coding Standards

### 1. Java Code Style

**General Guidelines**:
- Follow Google Java Style Guide
- Use 2 spaces for indentation
- Maximum line length: 120 characters
- Use meaningful variable and method names

**Example**:
```java
@Service
@Transactional
@Slf4j
public class BasicFarePlanCommandService extends AbstractCommandService<BasicFarePlan, Long> {

  private final BasicFarePlanRepository repository;
  private final EventStore eventStore;

  public BasicFarePlanCommandService(BasicFarePlanRepository repository, EventStore eventStore) {
    super(repository);
    this.repository = repository;
    this.eventStore = eventStore;
  }

  public BasicFarePlan createBasicFarePlan(CreateBasicFarePlanRequest request) {
    validateRequest(request);
    
    BasicFarePlan farePlan = BasicFarePlan.builder()
        .name(request.getName())
        .description(request.getDescription())
        .effectiveDate(request.getEffectiveDate())
        .status(FarePlanStatus.DRAFT)
        .build();
    
    BasicFarePlan savedPlan = repository.save(farePlan);
    
    // Publish domain event
    eventStore.publish(new BasicFarePlanCreatedEvent(savedPlan.getId()));
    
    log.info("Created basic fare plan: {}", savedPlan.getId());
    return savedPlan;
  }

  private void validateRequest(CreateBasicFarePlanRequest request) {
    if (request.getEffectiveDate().isBefore(LocalDate.now())) {
      throw new ValidationException("Effective date cannot be in the past");
    }
  }
}
```

### 2. Lombok Usage

**Recommended Annotations**:
```java
@Entity
@Table(name = "BASIC_FARE_PLAN")
@Data                    // Generates getters, setters, toString, equals, hashCode
@Builder                 // Generates builder pattern
@NoArgsConstructor       // Default constructor for JPA
@AllArgsConstructor      // Constructor with all fields
@EqualsAndHashCode(callSuper = true)  // For inheritance
public class BasicFarePlan extends FarePlan {
  
  @Column(name = "PLAN_TYPE")
  private String planType;
  
  @Column(name = "BASE_FARE")
  private BigDecimal baseFare;
}
```

**Avoid**:
- `@ToString` on entities with circular references
- `@Data` on entities with complex relationships (use specific annotations)

### 3. Exception Handling

**Custom Exceptions**:
```java
@ResponseStatus(HttpStatus.BAD_REQUEST)
public class ValidationException extends RuntimeException {
  public ValidationException(String message) {
    super(message);
  }
}

@ResponseStatus(HttpStatus.NOT_FOUND)
public class EntityNotFoundException extends RuntimeException {
  public EntityNotFoundException(String entityType, Object id) {
    super(String.format("%s with id %s not found", entityType, id));
  }
}
```

**Controller Exception Handling**:
```java
@RestControllerAdvice
public class GlobalExceptionHandler {

  @ExceptionHandler(ValidationException.class)
  public ResponseEntity<ErrorResponse> handleValidation(ValidationException ex) {
    ErrorResponse error = ErrorResponse.builder()
        .code("VALIDATION_ERROR")
        .message(ex.getMessage())
        .timestamp(Instant.now())
        .build();
    return ResponseEntity.badRequest().body(error);
  }
}
```

## CQRS Implementation

### 1. Command Services (Write Operations)

**Base Class**:
```java
@Transactional
public abstract class AbstractCommandService<T extends AggregateRoot, ID> {
  
  protected final JpaRepository<T, ID> repository;
  
  protected AbstractCommandService(JpaRepository<T, ID> repository) {
    this.repository = repository;
  }
  
  protected T save(T entity) {
    T savedEntity = repository.save(entity);
    publishEvents(savedEntity);
    return savedEntity;
  }
  
  private void publishEvents(T entity) {
    entity.getUncommittedEvents().forEach(eventStore::publish);
    entity.markEventsAsCommitted();
  }
}
```

**Implementation**:
```java
@Service
@Transactional
public class BasicFarePlanCommandService extends AbstractCommandService<BasicFarePlan, Long> {
  
  public BasicFarePlan create(CreateBasicFarePlanRequest request) {
    // Validation
    // Business logic
    // Save entity
    // Publish events
  }
  
  public BasicFarePlan update(Long id, UpdateBasicFarePlanRequest request) {
    // Find entity
    // Validation
    // Update entity
    // Save entity
    // Publish events
  }
  
  public void delete(Long id) {
    // Find entity
    // Soft delete or hard delete
    // Publish events
  }
}
```

### 2. Query Services (Read Operations)

**Base Class**:
```java
@Transactional(readOnly = true)
public abstract class AbstractQueryService<T, ID> {
  
  protected final JpaRepository<T, ID> repository;
  
  protected AbstractQueryService(JpaRepository<T, ID> repository) {
    this.repository = repository;
  }
  
  public Optional<T> findById(ID id) {
    return repository.findById(id);
  }
  
  public Page<T> findAll(Pageable pageable) {
    return repository.findAll(pageable);
  }
}
```

**Implementation**:
```java
@Service
@Transactional(readOnly = true)
public class BasicFarePlanQueryService extends AbstractQueryService<BasicFarePlan, Long> {
  
  public Page<BasicFarePlan> search(BasicFarePlanFilter filter, Pageable pageable) {
    // Build dynamic query
    // Apply filters
    // Return paginated results
  }
  
  public List<BasicFarePlan> findActiveByDateRange(LocalDate from, LocalDate to) {
    // Custom query implementation
  }
  
  public BasicFarePlanDto findByIdWithDetails(Long id) {
    // Find entity with entity graphs
    // Convert to DTO
    // Return DTO
  }
}
```

## Testing Guidelines

### 1. Test Structure

**Test Package Structure**:
```
src/test/java/com/ibm/tw/thsrc/bsm/fare/
├── controller/           # Controller tests
├── service/             # Service tests
├── repository/          # Repository tests
├── testing/            # Test utilities and data
│   ├── data/           # Test data builders
│   └── support/        # Test support classes
└── integration/        # Integration tests
```

### 2. Unit Testing

**Service Test Example**:
```java
@ExtendWith(MockitoExtension.class)
class BasicFarePlanCommandServiceTest {

  @Mock
  private BasicFarePlanRepository repository;
  
  @Mock
  private EventStore eventStore;
  
  @InjectMocks
  private BasicFarePlanCommandService service;
  
  @Test
  void createBasicFarePlan_ValidRequest_ReturnsCreatedPlan() {
    // Given
    CreateBasicFarePlanRequest request = CreateBasicFarePlanRequest.builder()
        .name("Test Plan")
        .effectiveDate(LocalDate.now().plusDays(1))
        .build();
    
    BasicFarePlan expectedPlan = BasicFarePlan.builder()
        .id(1L)
        .name("Test Plan")
        .status(FarePlanStatus.DRAFT)
        .build();
    
    when(repository.save(any(BasicFarePlan.class))).thenReturn(expectedPlan);
    
    // When
    BasicFarePlan result = service.createBasicFarePlan(request);
    
    // Then
    assertThat(result).isNotNull();
    assertThat(result.getName()).isEqualTo("Test Plan");
    assertThat(result.getStatus()).isEqualTo(FarePlanStatus.DRAFT);
    
    verify(repository).save(any(BasicFarePlan.class));
    verify(eventStore).publish(any(BasicFarePlanCreatedEvent.class));
  }
  
  @Test
  void createBasicFarePlan_PastEffectiveDate_ThrowsValidationException() {
    // Given
    CreateBasicFarePlanRequest request = CreateBasicFarePlanRequest.builder()
        .name("Test Plan")
        .effectiveDate(LocalDate.now().minusDays(1))
        .build();
    
    // When & Then
    assertThatThrownBy(() -> service.createBasicFarePlan(request))
        .isInstanceOf(ValidationException.class)
        .hasMessage("Effective date cannot be in the past");
  }
}
```

### 3. Integration Testing

**Integration Test Example**:
```java
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class BasicFarePlanIntegrationTest {

  @Autowired
  private BasicFarePlanCommandService commandService;
  
  @Autowired
  private BasicFarePlanQueryService queryService;
  
  @Autowired
  private TestEntityManager entityManager;
  
  @Test
  void createAndRetrieveBasicFarePlan() {
    // Given
    CreateBasicFarePlanRequest request = CreateBasicFarePlanRequest.builder()
        .name("Integration Test Plan")
        .effectiveDate(LocalDate.now().plusDays(1))
        .build();
    
    // When
    BasicFarePlan created = commandService.createBasicFarePlan(request);
    entityManager.flush();
    entityManager.clear();
    
    Optional<BasicFarePlan> retrieved = queryService.findById(created.getId());
    
    // Then
    assertThat(retrieved).isPresent();
    assertThat(retrieved.get().getName()).isEqualTo("Integration Test Plan");
  }
}
```

### 4. Test Data Builders

**Test Data Builder**:
```java
public class BasicFarePlanTestDataBuilder {
  
  private String name = "Test Plan";
  private String description = "Test Description";
  private LocalDate effectiveDate = LocalDate.now().plusDays(1);
  private FarePlanStatus status = FarePlanStatus.DRAFT;
  
  public static BasicFarePlanTestDataBuilder aBasicFarePlan() {
    return new BasicFarePlanTestDataBuilder();
  }
  
  public BasicFarePlanTestDataBuilder withName(String name) {
    this.name = name;
    return this;
  }
  
  public BasicFarePlanTestDataBuilder withEffectiveDate(LocalDate effectiveDate) {
    this.effectiveDate = effectiveDate;
    return this;
  }
  
  public BasicFarePlan build() {
    return BasicFarePlan.builder()
        .name(name)
        .description(description)
        .effectiveDate(effectiveDate)
        .status(status)
        .build();
  }
}
```

## Database Development

### 1. Entity Design

**Base Entity**:
```java
@MappedSuperclass
@Data
@EqualsAndHashCode(callSuper = false)
public abstract class AggregateRoot {
  
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ID")
  private Long id;
  
  @CreationTimestamp
  @Column(name = "CREATED_DATE")
  private LocalDateTime createdDate;
  
  @UpdateTimestamp
  @Column(name = "UPDATED_DATE")
  private LocalDateTime updatedDate;
  
  @Version
  @Column(name = "VERSION")
  private Long version;
  
  @Transient
  private List<DomainEvent> uncommittedEvents = new ArrayList<>();
  
  protected void publishEvent(DomainEvent event) {
    uncommittedEvents.add(event);
  }
  
  public List<DomainEvent> getUncommittedEvents() {
    return Collections.unmodifiableList(uncommittedEvents);
  }
  
  public void markEventsAsCommitted() {
    uncommittedEvents.clear();
  }
}
```

### 2. Repository Patterns

**Custom Repository Interface**:
```java
public interface BasicFarePlanRepository extends JpaRepository<BasicFarePlan, Long> {
  
  @Query("SELECT p FROM BasicFarePlan p WHERE p.status = :status AND p.effectiveDate <= :date")
  List<BasicFarePlan> findActiveByDate(@Param("status") FarePlanStatus status, @Param("date") LocalDate date);
  
  @EntityGraph(attributePaths = {"farePrices", "eligibleTrains"})
  Optional<BasicFarePlan> findByIdWithDetails(Long id);
  
  @Modifying
  @Query("UPDATE BasicFarePlan p SET p.status = :status WHERE p.id = :id")
  int updateStatus(@Param("id") Long id, @Param("status") FarePlanStatus status);
}
```

**Repository Decorator**:
```java
@Component
public class BasicFarePlanRepositoryDecorator implements BasicFarePlanRepository {
  
  private final BasicFarePlanJpaRepository jpaRepository;
  private final CacheManager cacheManager;
  
  @Override
  public Optional<BasicFarePlan> findById(Long id) {
    return cacheManager.get("fare-plans", id, () -> jpaRepository.findById(id));
  }
  
  @Override
  public BasicFarePlan save(BasicFarePlan entity) {
    BasicFarePlan saved = jpaRepository.save(entity);
    cacheManager.evict("fare-plans", saved.getId());
    return saved;
  }
}
```

## Performance Optimization

### 1. Query Optimization

**Entity Graphs**:
```java
@NamedEntityGraph(
    name = "BasicFarePlan.withDetails",
    attributeNodes = {
        @NamedAttributeNode("farePrices"),
        @NamedAttributeNode("eligibleTrains"),
        @NamedAttributeNode(value = "promotions", subgraph = "promotion-details")
    },
    subgraphs = {
        @NamedSubgraph(
            name = "promotion-details",
            attributeNodes = @NamedAttributeNode("category")
        )
    }
)
@Entity
public class BasicFarePlan extends FarePlan {
  // Entity definition
}
```

**Repository Usage**:
```java
@EntityGraph("BasicFarePlan.withDetails")
Optional<BasicFarePlan> findByIdWithDetails(Long id);
```

### 2. Caching Strategy

**Service-Level Caching**:
```java
@Service
@CacheConfig(cacheNames = "fare-plans")
public class BasicFarePlanQueryService {
  
  @Cacheable(key = "#id")
  public Optional<BasicFarePlan> findById(Long id) {
    return repository.findById(id);
  }
  
  @CacheEvict(key = "#id")
  public void evictCache(Long id) {
    // Cache eviction
  }
}
```

## Debugging and Troubleshooting

### 1. Logging Configuration

**Application Logging**:
```properties
# application-dev.properties
logging.level.com.ibm.tw.thsrc.bsm=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
```

### 2. Common Issues

**Hibernate LazyInitializationException**:
```java
// Problem: Accessing lazy-loaded property outside transaction
public BasicFarePlanDto convertToDto(BasicFarePlan plan) {
  return BasicFarePlanDto.builder()
      .id(plan.getId())
      .name(plan.getName())
      .priceCount(plan.getFarePrices().size()) // LazyInitializationException
      .build();
}

// Solution: Use entity graphs or fetch joins
@EntityGraph(attributePaths = "farePrices")
Optional<BasicFarePlan> findByIdWithPrices(Long id);
```

**N+1 Query Problem**:
```java
// Problem: Loading related entities in loop
public List<BasicFarePlanDto> getAllWithPrices() {
  List<BasicFarePlan> plans = repository.findAll();
  return plans.stream()
      .map(plan -> {
        int priceCount = plan.getFarePrices().size(); // N+1 queries
        return new BasicFarePlanDto(plan.getId(), plan.getName(), priceCount);
      })
      .collect(toList());
}

// Solution: Use batch fetching or entity graphs
@Query("SELECT DISTINCT p FROM BasicFarePlan p LEFT JOIN FETCH p.farePrices")
List<BasicFarePlan> findAllWithPrices();
```
