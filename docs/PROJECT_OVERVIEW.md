# Project Overview

## Executive Summary

FA-PD-APP (Fare Pricing and Discount Application) is a mission-critical enterprise application developed by IBM for Taiwan High Speed Rail Corporation (THSRC). This sophisticated fare management system serves as the core pricing engine, handling complex fare calculations, promotional pricing strategies, and comprehensive ticket management operations for one of Asia's premier high-speed rail networks.

## Business Context

### Taiwan High Speed Rail Corporation (THSRC)
- **Operator**: Taiwan's high-speed rail system
- **Network**: 345 kilometers connecting major cities
- **Technology**: Based on Japanese Shinkansen technology
- **Ridership**: Millions of passengers annually
- **Service**: Premium high-speed rail transportation

### Business Challenges Addressed
1. **Complex Fare Structures**: Multiple fare types, passenger categories, and pricing tiers
2. **Dynamic Pricing**: Peak/off-peak pricing, seasonal adjustments, and demand-based pricing
3. **Promotional Campaigns**: Co-branded credit card promotions, group discounts, and special offers
4. **Regulatory Compliance**: Government fare regulations and reporting requirements
5. **System Integration**: Integration with reservation systems, payment gateways, and external partners

## System Capabilities

### Core Functionality

#### 1. Fare Management
- **Basic Fare Plans**: Standard fare structures for different route segments
- **Dynamic Pricing**: Real-time fare adjustments based on demand and capacity
- **Fare Calculation Engine**: Complex algorithms for multi-segment journeys
- **Price Validation**: Automated validation of fare rules and constraints

#### 2. Promotional Pricing
- **Campaign Management**: Create and manage promotional campaigns
- **Discount Rules**: Flexible discount calculation engines
- **Co-branded Card Integration**: Special pricing for partner credit cards
- **Group Discounts**: Volume-based pricing for group bookings

#### 3. Ticket Product Management
- **Multi-ride Tickets**: Prepaid ticket packages with usage tracking
- **Periodic Tickets**: Season passes and monthly/annual subscriptions
- **Stored Value Tickets**: Prepaid value cards with flexible usage
- **Special Products**: Tourist passes, student discounts, and senior citizen fares

#### 4. Passenger Profile Management
- **Profile-based Discounts**: Personalized pricing based on passenger categories
- **Eligibility Verification**: Automated verification of discount eligibility
- **Usage Tracking**: Monitor discount usage and prevent abuse
- **Loyalty Integration**: Integration with loyalty programs and rewards

### Advanced Features

#### 1. Peak Time Management
- **Dynamic Peak Definitions**: Flexible peak time configuration
- **Calendar-based Pricing**: Special pricing for holidays and events
- **Weekday Variations**: Different pricing for different days of the week
- **Real-time Adjustments**: Automatic pricing adjustments based on demand

#### 2. Integration Capabilities
- **MEIG System**: OAuth2-based integration with external authentication
- **RES (Reservation System)**: Real-time integration with booking systems
- **Payment Gateways**: Integration with multiple payment processors
- **External APIs**: RESTful APIs for third-party integrations

#### 3. Operational Features
- **Audit Trails**: Comprehensive logging of all pricing decisions
- **Reporting**: Real-time and batch reporting capabilities
- **Monitoring**: System health monitoring and performance metrics
- **Backup & Recovery**: Automated backup and disaster recovery procedures

## Technical Architecture

### Architecture Principles

#### 1. Domain-Driven Design (DDD)
- **Bounded Contexts**: Clear separation of business domains
- **Aggregate Roots**: Consistent business entity boundaries
- **Domain Events**: Event-driven architecture for loose coupling
- **Ubiquitous Language**: Consistent terminology across business and technical teams

#### 2. CQRS (Command Query Responsibility Segregation)
- **Command Side**: Optimized for write operations and business logic
- **Query Side**: Optimized for read operations and reporting
- **Event Sourcing**: Complete audit trail of all business events
- **Eventual Consistency**: Scalable architecture with eventual consistency

#### 3. Microservice-Ready Architecture
- **Service Boundaries**: Clear service boundaries for future decomposition
- **API-First Design**: RESTful APIs for all external interactions
- **Stateless Design**: Horizontally scalable stateless services
- **Configuration Management**: Externalized configuration for different environments

### Technology Stack

#### Core Technologies
- **Runtime**: Java 1.8 (OpenJDK compatible)
- **Framework**: Spring Boot 2.7.6 with Spring Framework 5.3.x
- **Build Tool**: Gradle 7.x with Kotlin DSL support
- **Application Server**: WebSphere Liberty (containerized)

#### Data Layer
- **Primary Database**: Microsoft SQL Server 2019+ (production)
- **Test Database**: H2 in-memory database (testing)
- **ORM**: Hibernate 5.6.x with Spring Data JPA
- **Caching**: Redis 6.0+ for distributed caching
- **Schema**: Custom `fa` schema with uppercase naming strategy

#### Integration Layer
- **Message Broker**: Redis Pub/Sub for event messaging
- **HTTP Client**: Spring WebClient for external API calls
- **Authentication**: OAuth2 with MEIG system integration
- **Socket Communication**: Custom socket connection pooling for RES integration

#### Quality & Monitoring
- **Testing**: JUnit 5, Mockito, Spring Boot Test
- **Code Quality**: SonarQube integration with quality gates
- **Coverage**: JaCoCo for code coverage reporting
- **Monitoring**: Spring Boot Actuator with Prometheus metrics

### Deployment Architecture

#### Containerization
- **Base Image**: WebSphere Liberty optimized for Spring Boot
- **Container Registry**: Enterprise container registry with security scanning
- **Image Optimization**: Multi-stage builds for minimal image size
- **Security**: Non-root user execution and minimal attack surface

#### Orchestration
- **Platform**: Red Hat OpenShift 4.8+
- **Deployment**: DeploymentConfig with rolling updates
- **Scaling**: Horizontal Pod Autoscaler (HPA) for automatic scaling
- **Load Balancing**: OpenShift Router with SSL termination

#### Environment Management
- **Development**: Local development with Docker Compose
- **UAT**: User Acceptance Testing environment
- **Staging**: Production-like staging environment
- **Production**: High-availability production deployment

## Data Model

### Core Entities

#### Fare Management
- **FarePlan** (Abstract): Base class for all fare plans
- **BasicFarePlan**: Standard fare structures
- **GroupPlan**: Group booking fare plans
- **SmallGroupPlan**: Small group discounts
- **PromotionPlan**: Promotional fare plans

#### Ticket Products
- **TicketProduct**: Base ticket product entity
- **MultiRideTicket**: Multi-journey ticket products
- **PeriodicTicket**: Time-based ticket products
- **StoredValueTicket**: Prepaid value tickets

#### Pricing Components
- **BasicFarePrice**: Base fare pricing structure
- **ProfileDiscountPlan**: Profile-based discount rules
- **PromotionCategory**: Promotional campaign categories
- **RoundingRule**: Fare rounding and calculation rules

#### Operational Data
- **PeakTime**: Peak time definitions and rules
- **EligibleTrain**: Train eligibility for different fare types
- **PassengerProfile**: Passenger categorization and eligibility
- **ReissueTicketRefundFee**: Refund and reissue fee structures

### Data Relationships

#### Entity Inheritance
```
FarePlan (Abstract)
├── BasicFarePlan
├── GroupPlan
├── SmallGroupPlan
└── PromotionPlan

TicketProduct
├── MultiRideTicket
├── PeriodicTicket
└── StoredValueTicket
```

#### Key Relationships
- **One-to-Many**: FarePlan → FarePrice (pricing tiers)
- **Many-to-Many**: PromotionPlan ↔ EligibleTrain (applicable trains)
- **One-to-Many**: PassengerProfile → ProfileDiscount (applicable discounts)
- **Many-to-One**: All entities → AuditLog (audit trail)

## Integration Ecosystem

### External Systems

#### MEIG (Authentication System)
- **Protocol**: OAuth2 with JWT tokens
- **Purpose**: Centralized authentication and authorization
- **Integration**: RESTful API with token refresh
- **Fallback**: Mock authentication for development/testing

#### RES (Reservation System)
- **Protocol**: Custom socket-based communication
- **Purpose**: Real-time seat availability and booking
- **Features**: Connection pooling, automatic retry, circuit breaker
- **Monitoring**: Connection health monitoring and alerting

#### Payment Systems
- **Credit Card Processing**: Integration with multiple payment gateways
- **Co-branded Cards**: Special processing for partner credit cards
- **Refund Processing**: Automated refund processing workflows
- **Fraud Detection**: Integration with fraud detection systems

### Internal Systems

#### BSM (Business System Management)
- **Common Libraries**: Shared utilities and common functionality
- **Event System**: Domain event publishing and handling
- **Audit System**: Comprehensive audit logging and reporting
- **Alert System**: Real-time alerting and notification

#### Data Warehouse
- **ETL Processes**: Extract, Transform, Load for reporting
- **Analytics**: Business intelligence and analytics
- **Reporting**: Scheduled and ad-hoc reporting
- **Data Archival**: Long-term data retention and archival

## Operational Excellence

### Performance Characteristics
- **Response Time**: < 100ms for fare calculations
- **Throughput**: 10,000+ transactions per minute
- **Availability**: 99.9% uptime SLA
- **Scalability**: Horizontal scaling to handle peak loads

### Security Features
- **Authentication**: Multi-factor authentication support
- **Authorization**: Role-based access control (RBAC)
- **Data Encryption**: Encryption at rest and in transit
- **Audit Logging**: Complete audit trail for compliance

### Monitoring & Observability
- **Health Checks**: Comprehensive health monitoring
- **Metrics**: Business and technical metrics collection
- **Logging**: Centralized logging with log aggregation
- **Alerting**: Proactive alerting for system issues

### Disaster Recovery
- **Backup Strategy**: Automated daily backups with point-in-time recovery
- **Failover**: Automatic failover to secondary data center
- **Recovery Time**: RTO < 4 hours, RPO < 1 hour
- **Testing**: Regular disaster recovery testing and validation

## Development Lifecycle

### Development Process
- **Methodology**: Agile development with 2-week sprints
- **Version Control**: Git with GitFlow branching strategy
- **Code Review**: Mandatory peer code reviews
- **Testing**: Test-driven development (TDD) practices

### Quality Assurance
- **Automated Testing**: Unit, integration, and end-to-end tests
- **Code Quality**: SonarQube quality gates and code analysis
- **Performance Testing**: Load testing and performance benchmarking
- **Security Testing**: Automated security scanning and penetration testing

### Deployment Pipeline
- **CI/CD**: Automated continuous integration and deployment
- **Environment Promotion**: Automated promotion through environments
- **Blue-Green Deployment**: Zero-downtime deployment strategy
- **Rollback**: Automated rollback capabilities for failed deployments

## Future Roadmap

### Short-term Enhancements (3-6 months)
- **API Gateway**: Centralized API management and security
- **Event Streaming**: Apache Kafka for high-volume event processing
- **Machine Learning**: AI-powered dynamic pricing optimization
- **Mobile APIs**: Enhanced mobile application support

### Medium-term Evolution (6-12 months)
- **Microservices**: Decomposition into domain-specific microservices
- **Cloud Migration**: Migration to cloud-native architecture
- **Real-time Analytics**: Stream processing for real-time insights
- **International Expansion**: Multi-currency and multi-language support

### Long-term Vision (1-2 years)
- **Digital Transformation**: Complete digital transformation of fare management
- **Ecosystem Integration**: Integration with broader transportation ecosystem
- **Predictive Analytics**: Predictive modeling for demand forecasting
- **Customer Experience**: Personalized pricing and customer experience

## Success Metrics

### Business Metrics
- **Revenue Optimization**: 15% increase in revenue through dynamic pricing
- **Customer Satisfaction**: 95% customer satisfaction with pricing transparency
- **Operational Efficiency**: 50% reduction in manual pricing operations
- **Market Share**: Maintain market leadership in high-speed rail

### Technical Metrics
- **System Reliability**: 99.9% uptime achievement
- **Performance**: Sub-100ms response times for 95% of requests
- **Scalability**: Support for 10x traffic growth without architecture changes
- **Security**: Zero security incidents and full compliance

This comprehensive fare management system represents a significant investment in digital transformation, positioning THSRC as a technology leader in the transportation industry while delivering exceptional value to passengers and stakeholders.
