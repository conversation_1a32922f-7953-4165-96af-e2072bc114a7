# Deployment Guide

## Overview

This guide covers the deployment process for FA-PD-APP across different environments including local development, staging, and production deployments using Docker and OpenShift.

## Prerequisites

### System Requirements

| Component | Minimum | Recommended |
|-----------|---------|-------------|
| **Java** | 1.8+ | OpenJDK 11+ |
| **Memory** | 2GB RAM | 4GB RAM |
| **CPU** | 2 cores | 4 cores |
| **Disk** | 10GB | 20GB |
| **Network** | 1Gbps | 10Gbps |

### Required Software

- **Docker**: 20.10+
- **OpenShift CLI (oc)**: 4.8+
- **Gradle**: 7.0+ (or use wrapper)
- **SQL Server**: 2019+ (production)
- **Redis**: 6.0+

### Required Credentials

- Docker registry credentials
- OpenShift cluster access
- Database connection credentials
- MEIG OAuth2 credentials (production)

## Environment Configuration

### 1. Development Environment

**Database Setup (SQL Server)**:
```bash
# Using Docker
docker run -e "ACCEPT_EULA=Y" -e "SA_PASSWORD=P@ssw0rd" \
  -p 1433:1433 --name sqlserver \
  -d mcr.microsoft.com/mssql/server:2019-latest

# Create database
sqlcmd -S localhost -U sa -P "P@ssw0rd" -Q "CREATE DATABASE BSMDB"
```

**Redis Setup**:
```bash
# Using Docker
docker run --name redis -p 6377:6379 -d redis:6.2-alpine
```

**Application Configuration**:
```properties
# application-dev.properties
spring.profiles.active=dev
spring.datasource.url=****************************************************************;
spring.datasource.username=sa
spring.datasource.password=P@ssw0rd

integration.meig.enabled=false
integration.meig.mock-controller=true
integration.res.class-name=com.ibm.tw.thsrc.bsm.res.connection.MockSocketConnection
```

### 2. UAT Environment

**Configuration**:
```properties
# application-uat.properties
spring.profiles.active=uat
spring.datasource.url=***********************************************************************;
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}

integration.meig.enabled=true
integration.meig.mock-controller=false
integration.meig.access-token-uri=https://uat-meigkc.fatapat.com.tw:8443/auth/realms/MEIG/protocol/openid-connect/token
```

### 3. Production Environment

**Configuration**:
```properties
# application-prod.properties
spring.profiles.active=prod
spring.datasource.url=*************************************************************************;
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}

integration.meig.enabled=true
integration.meig.mock-controller=false
integration.meig.access-token-uri=https://meigkc.fatapat.com.tw:8443/auth/realms/MEIG/protocol/openid-connect/token

# Production-specific settings
spring.jpa.show-sql=false
spring.jpa.hibernate.ddl-auto=validate
logging.level.com.ibm.tw.thsrc.bsm=INFO
```

## Build Process

### 1. Local Build

```bash
# Clean and build
./gradlew clean build

# Run tests
./gradlew test

# Generate coverage report
./gradlew jacocoTestReport

# Build without tests (faster)
./gradlew build -x test
```

### 2. Docker Build

```bash
# Build application first
./gradlew build

# Build Docker image
./gradlew buildDockerImage

# Verify image
docker images | grep fa-pd-app
```

### 3. Custom Docker Build

```bash
# Manual Docker build
docker build -t fa-pd-app:latest \
  --build-arg WEBSPHERE_IMAGE=websphere-liberty:latest .

# Run locally
docker run -p 8087:8087 \
  -e SPRING_PROFILES_ACTIVE=dev \
  fa-pd-app:latest
```

## Docker Deployment

### 1. Docker Compose (Development)

**docker-compose.yml**:
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8087:8087"
    environment:
      - SPRING_PROFILES_ACTIVE=dev
      - DB_HOST=sqlserver
      - REDIS_HOST=redis
    depends_on:
      - sqlserver
      - redis
    networks:
      - app-network

  sqlserver:
    image: mcr.microsoft.com/mssql/server:2019-latest
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=P@ssw0rd
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    networks:
      - app-network

  redis:
    image: redis:6.2-alpine
    ports:
      - "6377:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network

volumes:
  sqlserver_data:
  redis_data:

networks:
  app-network:
    driver: bridge
```

**Deployment Commands**:
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f app

# Stop all services
docker-compose down

# Rebuild and restart
docker-compose up --build -d
```

### 2. Docker Registry

**Push to Registry**:
```bash
# Tag image
docker tag fa-pd-app:latest registry.example.com/fa-pd-app:v1.0.0

# Login to registry
echo $DOCKER_PASSWORD | docker login -u $DOCKER_USERNAME --password-stdin registry.example.com

# Push image
docker push registry.example.com/fa-pd-app:v1.0.0

# Using Gradle task
./gradlew pushDockerImage \
  -PdockerAppRegistryUser=$DOCKER_USERNAME \
  -PdockerAppRegistryPassword=$DOCKER_PASSWORD \
  -PdockerImageRegistryUrl=registry.example.com \
  -PdockerImageNamespace=thsrc \
  -PimageVersion=v1.0.0
```

## OpenShift Deployment

### 1. Prerequisites

**Login to OpenShift**:
```bash
# Login with token
oc login --token=$OC_TOKEN --server=$OC_SERVER

# Switch to project
oc project fa-pd-app
```

**Required Parameters**:
- `ocToken`: OpenShift authentication token
- `ocServer`: OpenShift cluster server URL
- `ocProjectName`: OpenShift project/namespace
- `dockerImageRegistryUrl`: Container registry URL
- `dockerImageNamespace`: Registry namespace
- `imageVersion`: Image version tag

### 2. Deployment Configuration

**DeploymentConfig (dc.yaml)**:
```yaml
apiVersion: apps.openshift.io/v1
kind: DeploymentConfig
metadata:
  name: fa-pd-app
  namespace: fa-pd-app
spec:
  replicas: 2
  selector:
    app: fa-pd-app
  template:
    metadata:
      labels:
        app: fa-pd-app
    spec:
      containers:
      - name: fa-pd-app
        image: registry.example.com/thsrc/fa-pd-app:latest
        ports:
        - containerPort: 8087
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: password
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8087
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8087
          initialDelaySeconds: 30
          periodSeconds: 10
  triggers:
  - type: ConfigChange
  - type: ImageChange
    imageChangeParams:
      automatic: true
      containerNames:
      - fa-pd-app
      from:
        kind: ImageStreamTag
        name: fa-pd-app:latest
```

### 3. Automated Deployment

**Using Gradle Tasks**:
```bash
# Deploy to OpenShift
./gradlew deployContainer \
  -PocToken=$OC_TOKEN \
  -PocServer=$OC_SERVER \
  -PocProjectName=fa-pd-app \
  -PdockerImageRegistryUrl=registry.example.com \
  -PdockerImageNamespace=thsrc \
  -PimageVersion=v1.0.0

# Check deployment status
./gradlew checkPodStatus \
  -PocToken=$OC_TOKEN \
  -PocServer=$OC_SERVER \
  -PocProjectName=fa-pd-app
```

**Manual Deployment**:
```bash
# Create/update deployment
oc apply -f k8s/

# Patch deployment with new image
oc patch dc/fa-pd-app --patch '{
  "spec": {
    "template": {
      "spec": {
        "containers": [{
          "name": "fa-pd-app",
          "image": "registry.example.com/thsrc/fa-pd-app:v1.0.0"
        }]
      }
    }
  }
}'

# Check rollout status
oc rollout status dc/fa-pd-app

# View pods
oc get pods -l app=fa-pd-app

# View logs
oc logs -f dc/fa-pd-app
```

## Configuration Management

### 1. Environment Variables

**Required Environment Variables**:
```bash
# Database Configuration
DB_USERNAME=sa
DB_PASSWORD=P@ssw0rd
DB_HOST=localhost
DB_PORT=1433
DB_NAME=BSMDB

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6377
REDIS_PASSWORD=

# MEIG Integration
MEIG_CLIENT_ID=meig_bpu
MEIG_CLIENT_SECRET=d82c5454-117b-40d8-9316-58bf0890838a
MEIG_TOKEN_URI=https://meigkc.fatapat.com.tw:8443/auth/realms/MEIG/protocol/openid-connect/token

# Application Configuration
SPRING_PROFILES_ACTIVE=prod
SERVER_PORT=8087
```

### 2. Secrets Management

**OpenShift Secrets**:
```bash
# Create database secret
oc create secret generic db-credentials \
  --from-literal=username=sa \
  --from-literal=password=P@ssw0rd

# Create MEIG secret
oc create secret generic meig-credentials \
  --from-literal=client-id=meig_bpu \
  --from-literal=client-secret=d82c5454-117b-40d8-9316-58bf0890838a

# Create TLS secret
oc create secret tls fa-pd-app-tls \
  --cert=path/to/tls.crt \
  --key=path/to/tls.key
```

### 3. ConfigMaps

**Application Configuration**:
```bash
# Create ConfigMap
oc create configmap fa-pd-app-config \
  --from-file=application-prod.properties

# Mount ConfigMap in deployment
oc set volume dc/fa-pd-app \
  --add --type=configmap \
  --configmap-name=fa-pd-app-config \
  --mount-path=/config
```

## Monitoring and Health Checks

### 1. Health Endpoints

**Kubernetes Probes**:
```yaml
livenessProbe:
  httpGet:
    path: /actuator/health
    port: 8087
  initialDelaySeconds: 60
  periodSeconds: 30
  timeoutSeconds: 10
  failureThreshold: 3

readinessProbe:
  httpGet:
    path: /actuator/health
    port: 8087
  initialDelaySeconds: 30
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 3
```

### 2. Monitoring Setup

**Prometheus Monitoring**:
```yaml
apiVersion: v1
kind: Service
metadata:
  name: fa-pd-app-metrics
  labels:
    app: fa-pd-app
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8087"
    prometheus.io/path: "/actuator/prometheus"
spec:
  ports:
  - port: 8087
    name: metrics
  selector:
    app: fa-pd-app
```

## Troubleshooting

### 1. Common Issues

**Application Won't Start**:
```bash
# Check logs
oc logs -f dc/fa-pd-app

# Check events
oc get events --sort-by=.metadata.creationTimestamp

# Check pod status
oc describe pod <pod-name>
```

**Database Connection Issues**:
```bash
# Test database connectivity
oc exec -it <pod-name> -- nc -zv db-host 1433

# Check database credentials
oc get secret db-credentials -o yaml
```

**Memory Issues**:
```bash
# Check resource usage
oc top pods

# Increase memory limits
oc patch dc/fa-pd-app --patch '{
  "spec": {
    "template": {
      "spec": {
        "containers": [{
          "name": "fa-pd-app",
          "resources": {
            "limits": {
              "memory": "8Gi"
            }
          }
        }]
      }
    }
  }
}'
```

### 2. Rollback Procedures

**Rollback Deployment**:
```bash
# View deployment history
oc rollout history dc/fa-pd-app

# Rollback to previous version
oc rollout undo dc/fa-pd-app

# Rollback to specific revision
oc rollout undo dc/fa-pd-app --to-revision=2

# Check rollback status
oc rollout status dc/fa-pd-app
```

## Performance Tuning

### 1. JVM Tuning

**JVM Options**:
```bash
# Set JVM options in deployment
oc set env dc/fa-pd-app \
  JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

### 2. Database Optimization

**Connection Pool Settings**:
```properties
# application-prod.properties
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
```

### 3. Scaling

**Horizontal Pod Autoscaler**:
```bash
# Create HPA
oc autoscale dc/fa-pd-app --min=2 --max=10 --cpu-percent=70

# Check HPA status
oc get hpa
```
