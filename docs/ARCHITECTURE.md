# Architecture Documentation

## System Overview

FA-PD-APP is a sophisticated fare management system built using modern enterprise architecture patterns. The application serves as the core pricing engine for Taiwan High Speed Rail Corporation (THSRC), handling complex fare calculations, promotional pricing, and ticket management operations.

## Architectural Patterns

### 1. CQRS (Command Query Responsibility Segregation)

The application implements a clear CQRS pattern to separate read and write operations:

#### Command Side (Write Operations)
- **Purpose**: Handle create, update, and delete operations
- **Classes**: `*CommandService` classes
- **Base Class**: `AbstractCommandService`
- **Responsibilities**:
  - Data validation and business rule enforcement
  - Entity persistence
  - Domain event publishing
  - Transaction management

#### Query Side (Read Operations)
- **Purpose**: Handle read operations and complex searches
- **Classes**: `*QueryService` classes
- **Base Class**: `AbstractQueryService`
- **Responsibilities**:
  - Data retrieval and filtering
  - Complex queries and aggregations
  - DTO transformation
  - Caching optimization

### 2. Domain-Driven Design (DDD)

#### Aggregate Roots
All entities extend `AggregateRoot` to support:
- Domain event publishing
- Consistency boundaries
- Business rule enforcement

#### Repository Pattern
- **Interface**: Domain-specific repository interfaces
- **Implementation**: Spring Data JPA repositories
- **Decorators**: Custom decorators for complex integrations (e.g., `ReissueTicketRefundFeeResRepositoryDecorator`)

#### Domain Events
- **Event Store**: Centralized event publishing mechanism
- **Event Handlers**: Asynchronous event processing
- **Audit Trail**: Complete audit logging through events

### 3. Entity Inheritance Strategy

The application uses JPA inheritance to model complex fare structures:

```java
@Entity
@Inheritance(strategy = InheritanceType.JOINED)
public abstract class FarePlan extends AggregateRoot {
    // Base fare plan properties
}

@Entity
public class BasicFarePlan extends FarePlan {
    // Basic fare plan specific properties
}
```

## System Components

### 1. Web Layer (Controllers)

**Location**: `com.ibm.tw.thsrc.bsm.fare.controller`

- **Count**: 42+ REST controllers
- **Naming Convention**: `*Controller.java`
- **Responsibilities**:
  - HTTP request/response handling
  - Input validation
  - Service orchestration
  - Error handling

**Key Controllers**:
- `BasicFarePlanController` - Basic fare plan management
- `FareEvaluationController` - Fare calculation engine
- `PromotionPlanController` - Promotional pricing
- `CoBrandedCardController` - Co-branded card management

### 2. Service Layer

**Location**: `com.ibm.tw.thsrc.bsm.fare.service`

#### Command Services
- **Pattern**: `*CommandService.java`
- **Purpose**: Write operations (Create, Update, Delete)
- **Transaction**: `@Transactional`
- **Validation**: Business rule enforcement

#### Query Services
- **Pattern**: `*QueryService.java`
- **Purpose**: Read operations and complex queries
- **Optimization**: Caching and query optimization
- **Transformation**: Entity to DTO conversion

#### Step Services
**Location**: `com.ibm.tw.thsrc.bsm.fare.stepservice`

Multi-step business processes:
- `EligibleTrainStepService` - Train eligibility processing
- `RegularTicketRuleStepService` - Ticket rule processing
- `CoBrandedCardOperatingParameterStepService` - Card parameter processing

### 3. Data Layer

#### Entities
**Location**: `com.ibm.tw.thsrc.bsm.fare.entity`

- **Count**: 80+ JPA entities
- **Inheritance**: Extensive use of JPA inheritance
- **Naming**: Database-first naming strategy
- **Relationships**: Complex entity relationships with proper cascading

**Key Entity Hierarchies**:
```
FarePlan (Abstract)
├── BasicFarePlan
├── GroupPlan
├── SmallGroupPlan
└── PromotionPlan

TicketProduct
├── MultiRideTicket
├── PeriodicTicket
└── StoredValueTicket
```

#### Repositories
**Location**: `com.ibm.tw.thsrc.bsm.fare.repository`

- **Pattern**: Spring Data JPA repositories
- **Custom Queries**: JPQL and native SQL queries
- **Decorators**: Repository decorators for complex integrations
- **Entity Graphs**: Performance optimization with entity graphs

### 4. Integration Layer

#### External System Integrations

**MEIG Integration**:
- **Protocol**: OAuth2
- **Purpose**: External system authentication
- **Configuration**: Environment-specific endpoints
- **Mock Support**: Development and testing

**RES Integration**:
- **Protocol**: Socket-based communication
- **Purpose**: Real-time reservation system
- **Connection Pooling**: Configurable pool size
- **Retry Logic**: Automatic retry with backoff

**Redis Integration**:
- **Purpose**: Caching and messaging
- **Configuration**: Separate instances for caching and messaging
- **Timeout**: Configurable timeout settings

### 5. Configuration Management

#### Environment-Specific Configuration
- `application-dev.properties` - Development
- `application-uat.properties` - User Acceptance Testing
- `application-stage.properties` - Staging
- `application-prod.properties` - Production

#### Integration Configuration
```properties
# MEIG OAuth2 Configuration
integration.meig.enabled=false
integration.meig.access-token-uri=https://meigkc.fatapat.com.tw:8443/auth/realms/MEIG/protocol/openid-connect/token

# RES Socket Configuration
integration.res.enabled=true
integration.res.host-ip=***********
integration.res.port=1502
integration.res.connection-pool-size=2

# Redis Configuration
integration.redis.host=127.0.0.1
integration.redis.port=6377
integration.redis.timeout=4h
```

## Data Flow Architecture

### 1. Request Processing Flow

```
HTTP Request → Controller → Validation → Service Layer → Repository → Database
                    ↓
Response ← DTO Transformation ← Business Logic ← Entity Mapping ← Query Result
```

### 2. Command Flow (Write Operations)

```
Command Request → CommandService → Validation → Entity Update → Event Publishing → Database Commit
                                                      ↓
                                              Audit Logging → Event Store
```

### 3. Query Flow (Read Operations)

```
Query Request → QueryService → Cache Check → Repository Query → Entity Graph Loading → DTO Transformation
                                   ↓
                            Cache Update ← Result Processing ← Database Query
```

## Security Architecture

### 1. Authentication & Authorization
- **OAuth2**: Integration with MEIG system
- **Session Management**: Configurable session timeout
- **API Security**: Endpoint-level security configuration

### 2. Data Security
- **Database Encryption**: SQL Server encryption support
- **Connection Security**: Encrypted database connections
- **Audit Trail**: Comprehensive audit logging

### 3. Network Security
- **HTTPS**: Enforced HTTPS communication
- **Firewall**: Network-level security controls
- **VPN**: Secure network access

## Performance Considerations

### 1. Database Optimization
- **Entity Graphs**: Optimized entity loading
- **Query Optimization**: Custom JPQL queries
- **Connection Pooling**: Configurable connection pools
- **Indexing**: Database index optimization

### 2. Caching Strategy
- **Redis Caching**: Distributed caching
- **Query Caching**: JPA second-level cache
- **Application Caching**: In-memory caching

### 3. Monitoring & Metrics
- **Health Endpoints**: Spring Boot Actuator
- **Performance Metrics**: Application performance monitoring
- **Log Analysis**: Centralized logging and analysis

## Scalability Design

### 1. Horizontal Scaling
- **Stateless Design**: Stateless service architecture
- **Load Balancing**: Multiple application instances
- **Database Scaling**: Read replicas and partitioning

### 2. Vertical Scaling
- **Resource Optimization**: Memory and CPU optimization
- **JVM Tuning**: Garbage collection optimization
- **Connection Pooling**: Optimized connection management

### 3. Microservice Readiness
- **Service Boundaries**: Clear service boundaries
- **API Design**: RESTful API design
- **Event-Driven**: Event-driven architecture support
