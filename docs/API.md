# API Documentation

## Overview

FA-PD-APP provides a comprehensive REST API for fare management operations. The API follows RESTful principles and implements consistent patterns across all endpoints.

## Base Configuration

- **Base URL**: `http://localhost:8087` (development)
- **Content-Type**: `application/json`
- **Character Encoding**: UTF-8
- **API Prefix**: `/api` (implied in controller mappings)

## Authentication

The application integrates with MEIG OAuth2 system for authentication:

```http
Authorization: Bearer <access_token>
```

For development, mock authentication is available when `integration.meig.mock-controller=true`.

## Core API Endpoints

### 1. Basic Fare Management

#### Basic Fare Plans
```http
GET    /basic-fare-plans              # List all basic fare plans
POST   /basic-fare-plans              # Create new basic fare plan
GET    /basic-fare-plans/{id}         # Get specific basic fare plan
PUT    /basic-fare-plans/{id}         # Update basic fare plan
DELETE /basic-fare-plans/{id}         # Delete basic fare plan
```

#### Basic Fare Prices
```http
GET    /basic-fare-prices             # List all basic fare prices
POST   /basic-fare-prices             # Create new basic fare price
GET    /basic-fare-prices/{id}        # Get specific basic fare price
PUT    /basic-fare-prices/{id}        # Update basic fare price
DELETE /basic-fare-prices/{id}        # Delete basic fare price
```

### 2. Fare Evaluation

#### Fare Calculation Engine
```http
POST   /fare-evaluation               # Calculate fare for given parameters
```

**Request Body Example**:
```json
{
  "originStation": "TPE",
  "destinationStation": "KHH",
  "departureDate": "2024-01-15",
  "passengerType": "ADULT",
  "ticketType": "STANDARD",
  "promotionCode": "PROMO2024"
}
```

**Response Example**:
```json
{
  "baseFare": 1490,
  "discountAmount": 149,
  "finalFare": 1341,
  "currency": "TWD",
  "fareBreakdown": {
    "baseFare": 1490,
    "promotionDiscount": -149,
    "taxes": 0
  },
  "applicablePromotions": ["PROMO2024"]
}
```

### 3. Promotion Management

#### Promotion Plans
```http
GET    /promotion-plans               # List all promotion plans
POST   /promotion-plans               # Create new promotion plan
GET    /promotion-plans/{id}          # Get specific promotion plan
PUT    /promotion-plans/{id}          # Update promotion plan
DELETE /promotion-plans/{id}          # Delete promotion plan
```

#### Promotion Categories
```http
GET    /promotion-categories          # List all promotion categories
POST   /promotion-categories          # Create new promotion category
GET    /promotion-categories/{id}     # Get specific promotion category
PUT    /promotion-categories/{id}     # Update promotion category
DELETE /promotion-categories/{id}     # Delete promotion category
```

### 4. Co-branded Card Management

#### Co-branded Cards
```http
GET    /co-branded-cards              # List all co-branded cards
POST   /co-branded-cards              # Create new co-branded card
GET    /co-branded-cards/{id}         # Get specific co-branded card
PUT    /co-branded-cards/{id}         # Update co-branded card
DELETE /co-branded-cards/{id}         # Delete co-branded card
```

#### Card Issuing Banks
```http
GET    /card-issuing-banks            # List all card issuing banks
POST   /card-issuing-banks            # Create new card issuing bank
GET    /card-issuing-banks/{id}       # Get specific card issuing bank
PUT    /card-issuing-banks/{id}       # Update card issuing bank
DELETE /card-issuing-banks/{id}       # Delete card issuing bank
```

### 5. Ticket Management

#### Ticket Products
```http
GET    /ticket-products               # List all ticket products
POST   /ticket-products               # Create new ticket product
GET    /ticket-products/{id}          # Get specific ticket product
PUT    /ticket-products/{id}          # Update ticket product
DELETE /ticket-products/{id}          # Delete ticket product
```

#### Multi-ride Tickets
```http
GET    /multi-ride-tickets            # List all multi-ride tickets
POST   /multi-ride-tickets            # Create new multi-ride ticket
GET    /multi-ride-tickets/{id}       # Get specific multi-ride ticket
PUT    /multi-ride-tickets/{id}       # Update multi-ride ticket
DELETE /multi-ride-tickets/{id}       # Delete multi-ride ticket
```

#### Periodic Tickets
```http
GET    /periodic-tickets              # List all periodic tickets
POST   /periodic-tickets              # Create new periodic ticket
GET    /periodic-tickets/{id}         # Get specific periodic ticket
PUT    /periodic-tickets/{id}         # Update periodic ticket
DELETE /periodic-tickets/{id}         # Delete periodic ticket
```

### 6. Passenger Management

#### Passenger Profiles
```http
GET    /passenger-profiles            # List all passenger profiles
POST   /passenger-profiles            # Create new passenger profile
GET    /passenger-profiles/{id}       # Get specific passenger profile
PUT    /passenger-profiles/{id}       # Update passenger profile
DELETE /passenger-profiles/{id}       # Delete passenger profile
```

#### Profile Discount Plans
```http
GET    /profile-discount-plans        # List all profile discount plans
POST   /profile-discount-plans        # Create new profile discount plan
GET    /profile-discount-plans/{id}   # Get specific profile discount plan
PUT    /profile-discount-plans/{id}   # Update profile discount plan
DELETE /profile-discount-plans/{id}   # Delete profile discount plan
```

### 7. Peak Time Management

#### Peak Times
```http
GET    /peak-times                    # List all peak times
POST   /peak-times                    # Create new peak time
GET    /peak-times/{id}               # Get specific peak time
PUT    /peak-times/{id}               # Update peak time
DELETE /peak-times/{id}               # Delete peak time
```

#### Peak Calendar Dates
```http
GET    /peak-calendar-dates           # List all peak calendar dates
POST   /peak-calendar-dates           # Create new peak calendar date
GET    /peak-calendar-dates/{id}      # Get specific peak calendar date
PUT    /peak-calendar-dates/{id}      # Update peak calendar date
DELETE /peak-calendar-dates/{id}      # Delete peak calendar date
```

## Query Parameters

### Filtering
Most GET endpoints support filtering through query parameters:

```http
GET /basic-fare-plans?status=ACTIVE&effectiveDate=2024-01-01
GET /promotion-plans?category=DISCOUNT&validFrom=2024-01-01&validTo=2024-12-31
```

### Sorting
```http
GET /basic-fare-plans?sort=createdDate,desc
GET /promotion-plans?sort=name,asc&sort=effectiveDate,desc
```

### Pagination
```http
GET /basic-fare-plans?page=0&size=20
GET /promotion-plans?page=1&size=50
```

## Response Format

### Success Response
```json
{
  "status": "SUCCESS",
  "data": {
    // Response data
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Error Response
```json
{
  "status": "ERROR",
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": [
      {
        "field": "departureDate",
        "message": "Departure date cannot be in the past"
      }
    ]
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Paginated Response
```json
{
  "status": "SUCCESS",
  "data": {
    "content": [
      // Array of items
    ],
    "pageable": {
      "page": 0,
      "size": 20,
      "sort": "createdDate,desc"
    },
    "totalElements": 150,
    "totalPages": 8,
    "first": true,
    "last": false
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## HTTP Status Codes

| Status Code | Description |
|-------------|-------------|
| 200 | OK - Request successful |
| 201 | Created - Resource created successfully |
| 204 | No Content - Request successful, no content returned |
| 400 | Bad Request - Invalid request parameters |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Access denied |
| 404 | Not Found - Resource not found |
| 409 | Conflict - Resource conflict |
| 422 | Unprocessable Entity - Validation error |
| 500 | Internal Server Error - Server error |

## Rate Limiting

The API implements rate limiting to ensure system stability:

- **Rate Limit**: 1000 requests per hour per client
- **Headers**: 
  - `X-RateLimit-Limit`: Maximum requests per hour
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time when rate limit resets

## Versioning

The API uses URL versioning:

```http
GET /api/v1/basic-fare-plans
GET /api/v2/basic-fare-plans
```

Current version: `v1`

## Health Endpoints

### Application Health
```http
GET /actuator/health
```

**Response**:
```json
{
  "status": "UP",
  "components": {
    "db": {
      "status": "UP",
      "details": {
        "database": "SQL Server",
        "validationQuery": "SELECT 1"
      }
    },
    "redis": {
      "status": "UP",
      "details": {
        "version": "6.2.7"
      }
    }
  }
}
```

### Application Info
```http
GET /actuator/info
```

**Response**:
```json
{
  "app": {
    "name": "fa-pd-app",
    "version": "0.0.1-SNAPSHOT",
    "description": "Fare Management Application"
  },
  "build": {
    "version": "0.0.1-SNAPSHOT",
    "time": "2024-01-15T10:00:00Z"
  }
}
```

## SDK and Client Libraries

### Java Client Example
```java
@Service
public class FareApiClient {
    
    @Autowired
    private RestTemplate restTemplate;
    
    public FareEvaluationResponse calculateFare(FareEvaluationRequest request) {
        return restTemplate.postForObject(
            "/fare-evaluation", 
            request, 
            FareEvaluationResponse.class
        );
    }
}
```

### cURL Examples

**Create Basic Fare Plan**:
```bash
curl -X POST http://localhost:8087/basic-fare-plans \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "Standard Fare Plan",
    "description": "Standard fare calculation plan",
    "effectiveDate": "2024-01-01",
    "status": "ACTIVE"
  }'
```

**Calculate Fare**:
```bash
curl -X POST http://localhost:8087/fare-evaluation \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "originStation": "TPE",
    "destinationStation": "KHH",
    "departureDate": "2024-01-15",
    "passengerType": "ADULT",
    "ticketType": "STANDARD"
  }'
```
